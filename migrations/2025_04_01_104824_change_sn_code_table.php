<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeSnCodeTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('sn_code')) {
            Schema::table('sn_code', function (Blueprint $table) {
                if (!Schema::hasColumn('sn_code', 'used_product_code')) {
                    $table->string('used_product_code')->default('')->comment('料号')->after('used_product');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sn_code', function (Blueprint $table) {
            //
        });
    }
}
