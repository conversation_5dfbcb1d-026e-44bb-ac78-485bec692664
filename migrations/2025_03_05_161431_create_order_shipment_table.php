<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderShipmentTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('shipment')) {
            Schema::create('shipment', function (Blueprint $table) {
                $table->integerIncrements('id');
                //销售系统订单号，出库单号，销售单号，客户，产品
                $table->string('order_no')->default('')->comment('销售系统订单号');
                $table->integer('qrcode_id')->default(0)->comment('销售系统二维码id');
                $table->string('out_stock_no')->default('')->comment('销售系统出库单号');
                $table->integer('client_id')->default(0)->comment('销售系统客户id');
                $table->string('client_name')->default('')->comment('销售系统客户名称');
                $table->string('sale_ids',500)->default('')->comment('销售单号id');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipment', function (Blueprint $table) {
            //
        });
    }
}
