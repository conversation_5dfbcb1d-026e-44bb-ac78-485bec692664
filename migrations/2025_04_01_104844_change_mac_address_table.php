<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeMacAddressTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('mac_address')) {
            Schema::table('mac_address', function (Blueprint $table) {
                if (!Schema::hasColumn('mac_address', 'used_product_code')) {
                    $table->string('used_product_code')->default('')->comment('产品料号')->after('used_product');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mac_address', function (Blueprint $table) {
            //
        });
    }
}
