<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeOaUserFilesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_user_files')) {
            Schema::table('oa_user_files', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_user_files', 'resume_attachment_id')) {
                    $table->integer('resume_attachment_id')->default(0)->comment('简历附件');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_user_files', function (Blueprint $table) {
            //
        });
    }
}
