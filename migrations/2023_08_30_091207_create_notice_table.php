<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateNoticeTable extends Migration
{
    /**
     * 消息表
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('notice')) {
            Schema::create('notice', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name', 100)->comment('消息名称');
                $table->char('method', 64)->comment('系统的函数执行名称 (在触发传入该参数)');
                $table->char('notice_type', 16)->comment('消息类型');
                $table->text('workwx_tpl')->nullable()->comment('企业微信模板');
                $table->text('email_tpl')->nullable()->comment('邮件模板');
                $table->text('website_tpl')->nullable()->comment('浏览器模板');
                $table->json('default_subscribe')->nullable()->comment('默认订阅的渠道');
                $table->json('users')->nullable()->comment('默认订阅的用户');
                $table->tinyInteger('status')->default(1)->comment('状态');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notice');
    }
}
