<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateChecklistsTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checklists', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('检查清单主键，自增');
            $table->integer('project_id')->nullable()->comment('项目ID');
            $table->unsignedBigInteger('issue_id')->comment('关联的issue表ID');
            $table->string('title', 255)->comment('检查清单标题（用户可自定义）');
            $table->unsignedBigInteger('assignee')->nullable()->comment('处理人（用户ID）');
            $table->unsignedBigInteger('created_by')->comment('创建人（用户ID）');
            $table->dateTime('created_at')->useCurrent()->comment('创建时间');
            $table->dateTime('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('软删除时间');

            $table->index('issue_id');
            $table->index('project_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checklists');
    }
}
