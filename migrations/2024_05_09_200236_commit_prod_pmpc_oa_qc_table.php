<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProdPmpcOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_qc', 'prod_pmpc')) {
                    $table->smallInteger('prod_pmpc')->comment('物料pmpccode');
                }
                if (!Schema::hasColumn('oa_qc', 'attribution')) {
                    $table->string('attribution', 16)->comment('产品归属地 公司(正常仓位)|工厂(创隆仓)');
                }
                if (!Schema::hasColumn('oa_qc', 'batch')) {
                    $table->string('batch', 64)->nullable()->comment('批次 (PCB类,贴片IC类属性)');
                }
                if (!Schema::hasColumn('oa_qc', 'factory_name')) {
                    $table->string('factory_name', 64)->nullable()->comment('板厂名称 (PCB类专有属性)');
                }
                if (!Schema::hasColumn('oa_qc', 'factory_identifier')) {
                    $table->string('factory_identifier', 64)->nullable()->comment('板厂标识 (PCB类专有属性)');
                }
                if (!Schema::hasColumn('oa_qc', 'silk')) {
                    $table->text('silk')->nullable()->comment('丝印 (贴片IC类专有属性)');
                }
                if (!Schema::hasColumn('oa_qc', 'pmpc_category')) {
                    $table->char('pmpc_category', 16)->default('common')->comment('pmpc分类(tpic:贴片IC,pcb:PCB,common:通用)');
                }
                if (!Schema::hasColumn('oa_qc', 'image')) {
                    $table->json('image')->nullable()->comment('图片json数组');
                }
                if (!Schema::hasColumn('oa_qc', 'check_date')) {
                    $table->date('check_date')->nullable()->comment('检测日期');
                }
                if (!Schema::hasColumn('oa_qc', 'defective_num')) {
                    $table->mediumInteger('defective_num')->default(0)->comment('不合格数量');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
