<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAssembleOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('assemble_order')) {
            Schema::create('assemble_order', function (Blueprint $table) {
                $table->bigIncrements('id')->comment('id');
                $table->string('code')->default('')->comment('订单编号');
                $table->string('product_name')->default('')->comment('产品名称');
                $table->string('product_code')->default('')->comment('料号');
                $table->string('product_spec')->default('')->comment('规格');
                $table->bigInteger('num')->unsigned()->default(0)->comment('数量');
                $table->date('order_date')->nullable()->comment('订单日期');
                $table->string('sales_user')->default('')->comment('业务员');
                $table->integer('sales_user_id')->default(0)->comment('业务员');
                $table->string('assemble_address')->default('')->comment('组装地点');
                $table->dateTime('payment_time')->nullable()->comment('出帐时间');
                $table->dateTime('checked_time')->nullable()->comment('审核时间');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                #建立索引
                $table->index('code');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order', function (Blueprint $table) {
            //
        });
    }
}
