<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectLabelTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_label', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 255)->nullable()->comment('标签名称');
            $table->string('background_color', 100)->nullable()->comment('背景颜色如: #ffffff');
            $table->string('text_color', 100)->nullable()->comment('文本颜色如: #333333');
            $table->integer('project_id')->default(0)->comment('项目id');
            $table->integer('created_by')->default(0)->comment('创建人');
            $table->timestamps();
            $table->softDeletes();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_label');
    }
}
