<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOaAssetsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_assets', function (Blueprint $table) {
            if (! Schema::hasColumn('oa_assets', 'department_id')) {
                $table->integer('department_id')->nullable()->comment('部门id');
            }
            if (! Schema::hasColumn('oa_assets', 'second_category_id')) {
                $table->integer('second_category_id')->nullable()->comment('二级分类id');
            }

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
