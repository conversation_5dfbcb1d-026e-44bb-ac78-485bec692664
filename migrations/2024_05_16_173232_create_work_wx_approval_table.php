<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateWorkWxApprovalTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('work_wx_approval')) {
            Schema::create('work_wx_approval', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->char('sp_no', 32)->comment('审批编号');
                $table->string('sp_name', 32)->comment('审批模板名称');
                //  申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
                $table->smallInteger('sp_status')->comment('申请单状态');
                $table->char('template_id', 64)->comment('审批模板id');
                $table->json('applyer')->nullable()->comment('申请人信息');
                $table->bigInteger('applyer_id')->default(0)->comment('申请人ID');
                $table->string('applyer_name', 16)->nullable()->comment('申请人名称');
                $table->json('apply_data')->nullable()->comment('审批的数据');
                $table->integer('apply_time')->comment('提交时间unix');
                $table->dateTime('apply_time_date')->comment('提交时间');
                $table->json('sp_record')->nullable()->comment('审批流程信息');
                $table->json('notifyer')->nullable()->comment('节点抄送人userid');
                $table->json('comments')->nullable()->comment('审批申请备注信息');
                $table->json('process_list')->nullable()->comment('审批流程列表');
                // 审批申请状态变化类型：1-提单；2-同意；3-驳回；4-转审；5-催办；6-撤销；8-通过后撤销；10-添加备注；11-回退给指定审批人；12-添加审批人；13-加签并同意； 14-已办理； 15-已转交
                $table->smallInteger('statu_change_event')->default(0)->comment('审批申请状态变化类型');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
                // $table->timestamps();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_wx_approval');
    }
}
