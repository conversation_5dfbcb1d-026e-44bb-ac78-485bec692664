<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateSnCodeTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('sn_code')) {
            Schema::create('sn_code', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('sn_code')->default('')->comment('Sn码');
                $table->string('used_no', 32)->nullable(true)->comment('订单号');
                $table->smallInteger('used_type')->default(0)->comment('订单类型1:(外委)生产订单');
                $table->integer('used_relate_id')->default(0)->comment('关联的id');
                $table->integer('used_user_id')->default(0)->comment('使用人ID');
                $table->text('used_product')->nullable(true)->comment('使用的产品');
                $table->text('used_client')->nullable(true)->comment('使用的客户');
                $table->dateTime('used_date')->nullable(true)->comment('申请使用的日期');
                $table->tinyInteger('is_used')->default(0)->comment('是否被占用{0:否,1:是}');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();

                $table->index('code');
                $table->index('used_no');
                $table->index(['used_type', 'used_relate_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sn_code', function (Blueprint $table) {
            //
        });
    }
}
