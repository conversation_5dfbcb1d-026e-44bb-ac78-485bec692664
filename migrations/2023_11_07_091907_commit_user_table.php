<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitUserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            //增加字段标记user所选的偏好设置的打开产品详情的模式
            if (! Schema::hasColumn('user', 'product_detail_opening_mode')) {
                $table->tinyInteger('product_detail_opening_mode')
                    ->nullable()
                    ->default(1)
                    ->comment('1：弹窗打开商品详情，2：新窗口打开商品详情');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user', function (Blueprint $table) {
            //
        });
    }
}
