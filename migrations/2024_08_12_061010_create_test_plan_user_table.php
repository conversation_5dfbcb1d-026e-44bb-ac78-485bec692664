<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestPlanUserTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('test_plan_user')) {
                Schema::create('test_plan_user', function (Blueprint $table) {
                    $table->bigIncrements('id');
                    $table->integer('test_plan_id')->default(0)->comment('计划ID');
                    $table->integer('user_id')->default(0)->comment('用户ID');
                    $table->dateTime('created_at')->nullable()->comment('创建时间');
                    $table->dateTime('updated_at')->nullable()->comment('更新时间');
                    $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                    //索引
                    $table->index('test_plan_id');
                });
            }
        }


        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_plan_user_relation');
        }
    }
