<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CommitProductionOrderTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (Schema::hasTable('production_order')) {
                Schema::table('production_order', function (Blueprint $table) {
                    if (!Schema::hasColumn('production_order', 'emmc_name')) {
                        $table->string('emmc_name')->default('')->comment('EMMC使用型号')->after('create_type');
                    }
                    if (!Schema::hasColumn('production_order', 'pcb_name')) {
                        $table->string('pcb_name')->default('')->comment('PCB型号')->after('create_type');
                    }
                    if (!Schema::hasColumn('production_order', 'ddr_name')) {
                        $table->string('ddr_name')->default('')->comment('DDR使用型号')->after('create_type');
                    }
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::table('production_order', function (Blueprint $table) {
                //
            });
        }
    }
