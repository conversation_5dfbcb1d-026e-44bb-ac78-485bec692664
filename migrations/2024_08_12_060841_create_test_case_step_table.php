<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestCaseStepTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_case_step', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('test_case_id')->default(0)->comment('用例ID');
                $table->integer('step_order')->default(0)->comment('步骤顺序');
                $table->text('step')->nullable()->comment('步骤描述（文本）');
                $table->text('expected_result')->nullable()->comment('预期结果（文本）');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_case_step');
        }
    }
