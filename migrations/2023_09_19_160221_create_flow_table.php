<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFlowTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('flow')) {
            Schema::create('flow', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('tpl_id')->default(0)->comment('模板ID');
                $table->string('name', 64)->comment('流程名称');
                $table->char('flow_type', 16)->comment('程流类型，issue,project');
                $table->integer('container_id')->comment('关联ID issue时issue_id,project时project_id');
                $table->json('edges')->nullable()->comment('节点之间连接的数据');
                $table->json('nodes')->nullable()->comment('节点数据');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process');
    }
}
