<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateWorkStatusLogTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('work_status_log')) {
            Schema::create('work_status_log', function (Blueprint $table) {
                $table->integerIncrements('id')->comment('id');
                $table->string('type')->default('')->comment('类型');
                $table->integer('relate_id')->default(0)->comment('关联id');
                $table->tinyInteger('old_status')->default(0)->comment('旧状态');
                $table->tinyInteger('new_status')->default(0)->comment('新状态');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                #建立索引
                $table->index(['type', 'relate_id']);;
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_status_log', function (Blueprint $table) {
            //
        });
    }
}
