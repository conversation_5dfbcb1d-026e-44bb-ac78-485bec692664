<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueAssignedTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('issue_assigned', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('issue_id')->index();
            $table->bigInteger('user_id')->index();
            $table->tinyInteger('leader')->default(0)->comment('是否主负责人');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->dateTime('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issue_assigned');
    }
}
