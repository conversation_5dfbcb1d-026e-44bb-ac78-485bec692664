<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateLevelConfigsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('level_configs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->tinyInteger('level')->comment('等级数值');
            $table->string('level_name', 50)->comment('等级名称');
            $table->string('level_title', 100)->nullable()->comment('等级称号');
            $table->integer('min_points')->comment('最低积分要求');
            $table->integer('max_points')->nullable()->comment('最高积分(NULL表示无上限)');
            $table->string('color', 20)->nullable()->comment('等级颜色(十六进制)');
            $table->string('icon', 255)->nullable()->comment('等级图标URL');
            $table->string('badge_image', 255)->nullable()->comment('等级徽章图片URL');
            $table->json('privileges')->nullable()->comment('等级特权配置');
            $table->integer('upgrade_reward')->default(0)->comment('升级奖励积分');
            $table->text('description')->nullable()->comment('等级描述');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();

            $table->unique('level', 'uk_level');
            $table->index('min_points', 'idx_min_points');
            $table->index('is_active', 'idx_is_active');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_level_configs` comment '积分系统-用户等级配置表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('level_configs');
    }
}