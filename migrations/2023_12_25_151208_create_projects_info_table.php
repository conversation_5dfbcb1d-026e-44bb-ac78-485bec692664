<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectsInfoTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('projects_info')) {
            Schema::create('projects_info', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('project_id')->comment('项目ID');
                $table->string('category', 16)->comment('产品分类');
                $table->string('platform', 16)->default('')->comment('产品平台');
                $table->smallInteger('product_status')->default(0)->comment('产品状态');
                $table->smallInteger('product_type')->default(0)->comment('产品类型');
                $table->char('os', 16)->default('')->comment('系统');
                $table->smallInteger('product_progress')->default(0)->comment('产品进度');
                $table->json('hard_handler_uid')->nullable()->comment('硬件负责人');
                $table->json('soft_handler_uid')->nullable()->comment('软件负责人');
                $table->integer('creator')->default(0)->comment('创建人ID');
                $table->string('img', 512)->nullable()->comment('产品图片');
                $table->integer('client_id')->default(0)->comment('客户ID');
                $table->string('client_name', 128)->default('')->comment('客户名称');
                $table->date('created_date')->comment('立项日期');
                $table->date('closed_date')->nullable()->comment('关闭日期');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_info');
    }
}
