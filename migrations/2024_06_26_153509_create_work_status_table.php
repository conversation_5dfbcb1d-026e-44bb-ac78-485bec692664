<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CreateWorkStatusTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('work_status')) {
            Schema::create('work_status', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->string('scene_type')->default('')->comment('场景类型');
                $table->string('name')->default('')->comment('状态名称');
                $table->string('key')->default('')->comment('标识');
                $table->integer('sort')->default(1000)->comment('排序');
                #创建时间
                $table->dateTime('created_at')->nullable();
                #更新时间
                $table->dateTime('updated_at')->nullable();
                #删除时间
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_status');
    }
}
