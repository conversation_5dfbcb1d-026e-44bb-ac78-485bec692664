<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CommitOaQcTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (Schema::hasTable('oa_qc')) {
                Schema::table('oa_qc', function (Blueprint $table) {
                    // 添加 from_no 列
                    if (!Schema::hasColumn('oa_qc', 'from_no')) {
                        $table->string('from_no')->after('real_examine_num')->nullable()->comment('来源编号');
                    }

                    // 添加 is_first 列
                    if (!Schema::hasColumn('oa_qc', 'is_first')) {
                        $table->char('is_first', 16)->nullable()->default(0)->comment('是否首次回货');
                    }

                    if (!Schema::hasColumn('oa_qc', 'status_change_at')) {
                        $table->timestamp('status_change_at')->after('status')->nullable()->comment('检验结果修改时间');
                    }
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::table('', function (Blueprint $table) {
                //
            });
        }
    }
