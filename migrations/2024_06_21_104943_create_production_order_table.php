<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductionOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('production_order')) {
            Schema::create('production_order', function (Blueprint $table) {
                $table->bigIncrements('id');
                #编号
                $table->string('code')->default('')->comment('生产订单编号');
                $table->string('wocode')->default('')->comment('编号');
                #产品名称
                $table->string('product_name')->default('')->comment('生产订单产品名称');
                #加工厂
                $table->string('factory_code')->default('')->comment('生产订单加工厂编号');
                $table->string('factory_name')->default('')->comment('生产订单加工厂名称');
                #料号
                $table->string('product_code')->default('')->comment('生产订单料号');
                #加工数量
                $table->bigInteger('woqty')->unsigned()->default(0)->comment('生产订单加工数量');
                #订单日期
                $table->date('delivery_date')->nullable()->comment('订单日期');
                #规格
                $table->string('product_spec')->default('')->comment('规格');
                #客人备注
                $table->text('customer_remark')->nullable()->comment('客人备注');
                #创建人
                $table->integer('create_user_id')->unsigned()->default(0)->comment('创建人');
                //创建类型
                $table->integer('create_type')->unsigned()->default(1)->comment('创建类型{0:无;1:手动新增,2:自动同步}');
                #创建时间
                $table->dateTime('created_at')->nullable();
                #更新时间
                $table->dateTime('updated_at')->nullable();
                #删除时间
                $table->dateTime('deleted_at')->nullable();
                #建立索引
                $table->index('code');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order');
    }
}
