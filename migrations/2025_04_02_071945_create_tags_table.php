<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTagsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('tags')) {
                Schema::create('tags', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('tag_id')->comment('标签ID');

                    // 字段
                    $table->string('tag_name', 50)->comment('标签名称');
                    $table->string('text_color', 50)->default('#666666')->comment('文本颜色');
                    $table->string('background_color', 50)->nullable()->comment('标签背景颜色');
                    $table->bigInteger('created_by')->comment('创建人ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('tag_name');
                    $table->index('created_by');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('tags');
        }
    }
