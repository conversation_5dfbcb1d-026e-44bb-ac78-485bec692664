<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreatePointEventsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('point_events', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('name', 100)->comment('活动名称');
            $table->text('description')->nullable()->comment('活动描述');
            $table->enum('event_type', ['multiplier', 'bonus', 'special'])->default('multiplier')->comment('活动类型');
            $table->json('action_types')->comment('影响的行为类型');
            $table->decimal('multiplier', 3, 2)->default(1.00)->comment('积分倍数');
            $table->integer('bonus_points')->default(0)->comment('额外奖励积分');
            $table->integer('max_participants')->nullable()->comment('最大参与人数');
            $table->timestamp('start_time')->comment('活动开始时间');
            $table->timestamp('end_time')->comment('活动结束时间');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用');
            $table->json('conditions')->nullable()->comment('参与条件');
            $table->json('rewards')->nullable()->comment('活动奖励配置');
            $table->integer('current_participants')->default(0)->comment('当前参与人数');
            $table->timestamps();

            $table->index(['start_time', 'end_time'], 'idx_time_range');
            $table->index('is_active', 'idx_is_active');
            $table->index('event_type', 'idx_event_type');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_point_events` comment '积分系统-积分活动表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('point_events');
    }
}