
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateNoticeCategoryTable extends Migration
{
    /**
     * 通知分类表
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('notice_category')) {
            Schema::create('notice_category', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('code', 64)->unique()->comment('分类编码');
                $table->string('name', 100)->comment('分类名称');
                $table->string('description', 255)->nullable()->comment('分类描述');
                $table->text('workwx_tpl')->nullable()->comment('企业微信模板');
                $table->text('email_tpl')->nullable()->comment('邮件模板');
                $table->text('website_tpl')->nullable()->comment('网站模板');
                $table->integer('sort')->default(0)->comment('排序');
                $table->tinyInteger('status')->default(1)->comment('状态：0=禁用,1=启用');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
            
            // 初始化固定分类
            $this->initCategories();
        }
    }
    
    /**
     * 初始化固定的通知分类
     */
    private function initCategories()
    {
        $categories = [
            [
                'code' => 'system',
                'name' => '系统消息',
                'description' => '系统级别的通知',
                'workwx_tpl' => '【系统消息】{$content}',
                'email_tpl' => '<h1>系统消息</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 100,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'code' => 'product',
                'name' => '产品消息',
                'description' => '产品相关的通知',
                'workwx_tpl' => '【产品消息】{$content}',
                'email_tpl' => '<h1>产品消息</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 90,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'code' => 'project',
                'name' => '项目消息',
                'description' => '项目相关的通知',
                'workwx_tpl' => '【项目消息】{$content}',
                'email_tpl' => '<h1>项目消息</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 80,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'code' => 'issue_journal',
                'name' => '事项消息',
                'description' => '事项相关的通知',
                'workwx_tpl' => '【事项消息】{$content}',
                'email_tpl' => '<h1>事项消息</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 70,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'code' => 'issue_at',
                'name' => '@我消息',
                'description' => '@我相关的通知',
                'workwx_tpl' => '【有人@了你】{$content}',
                'email_tpl' => '<h1>有人@了你</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 60,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'code' => 'production',
                'name' => '产线消息',
                'description' => '产线相关的通知',
                'workwx_tpl' => '【产线消息】{$content}',
                'email_tpl' => '<h1>产线消息</h1><p>{$content}</p>',
                'website_tpl' => '<div>{$content}</div>',
                'sort' => 50,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        // 使用DB类插入数据
        \Hyperf\DbConnection\Db::table('notice_category')->insert($categories);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notice_category');
    }
}