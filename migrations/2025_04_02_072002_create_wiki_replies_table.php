<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiRepliesTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_replies')) {
                Schema::create('wiki_replies', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('reply_id')->comment('回复唯一ID（主键）');

                    // 字段
                    $table->bigInteger('comment_id')->comment('关联的评论ID');
                    $table->bigInteger('user_id')->comment('回复用户ID');
                    $table->bigInteger('parent_id')->nullable()->comment('父回复ID（用于二级回复）');
                    $table->text('content')->comment('回复内容');
                    $table->tinyInteger('is_deleted')->default(0)->comment('是否删除（0:否 1:是)');
                    $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('comment_id');
                    $table->index('user_id');
                    $table->index('parent_id');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_replies');
        }
    }
