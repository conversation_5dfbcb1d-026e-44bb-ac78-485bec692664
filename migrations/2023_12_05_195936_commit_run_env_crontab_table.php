<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRunEnvCrontabTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('crontab')) {
            Schema::table('crontab', function (Blueprint $table) {
                if (!Schema::hasColumn('crontab', 'run_env')) {
                    // mysql 不允许json默认值非空
                    // $table->json('run_env')->default('["product"]')->comment('允许执行的环境');
                    $table->json('run_env')->nullable()->comment('允许执行的环境');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
