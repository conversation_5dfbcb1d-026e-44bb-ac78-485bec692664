<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateJuoralExtPostTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('journal_ext_post')) {
            Schema::create('journal_ext_post', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('journal_id')->comment('journal的id');
                $table->integer('project_id')->comment('产品或项目id');
                $table->bigInteger('pid')->comment('评论id');
                $table->bigInteger('rpid')->comment('点评评论id');
                $table->bigInteger('rpcid')->comment('点评点评id');
                $table->integer('authorid', )->comment('点评人id');
                $table->string('author', 32)->comment('点评人名字');
                $table->string('rauthor', 32)->comment('回复人名字');
                $table->string('avatar')->comment('回复人头像');
                $table->integer('rauthorid')->comment('回复人id');
                $table->text('notes_html')->comment('点评内容');
                $table->text('notes')->comment('点评内容');
                $table->json('rauthor_info')->nullable()->comment('被回复人的信息');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_comments');
    }
}
