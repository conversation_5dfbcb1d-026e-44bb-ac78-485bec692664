<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiSpacesTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_spaces')) {
                Schema::create('wiki_spaces', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('space_id')->comment('空间ID');

                    // 字段
                    $table->string('space_name', 100)->comment('空间名称');
                    $table->text('description')->nullable()->comment('空间描述');
                    $table->tinyInteger('is_public')->default(0)->comment('是否公开(0非公开/1公开)');
                    $table->integer('doc_count')->default(0)->comment('文档数量');
                    $table->bigInteger('created_by')->comment('创建人ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('space_name');
                    $table->index('created_by');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_spaces');
        }
    }
