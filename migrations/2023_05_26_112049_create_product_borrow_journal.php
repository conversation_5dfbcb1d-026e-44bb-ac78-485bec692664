<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductBorrowJournal extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(!Schema::hasTable('oa_product_borrow_journal')){
            Schema::create('oa_product_borrow_journal', function (Blueprint $table) {
                $table->bigIncrements('id'); // 递增主键
                $table->integer('user_id')->comment('操作人ID');
                $table->char('type', 8)->comment('master:主订单的ID,details:明细的ID');
                $table->bigInteger('b_id')->comment('订单主键');
                $table->json('old_value')->nullable()->comment('修改前的数据');
                $table->json('value')->nullable()->comment('修改后的数据');
                $table->dateTime('created_at'); // datetime
                $table->dateTime('updated_at'); // datetime
                $table->dateTime('deleted_at')->nullable(); // datetime
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
