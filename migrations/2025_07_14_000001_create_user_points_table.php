<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserPointsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_points', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联users表');
            $table->integer('current_points')->default(0)->comment('当前可用积分');
            $table->integer('total_points')->default(0)->comment('累计获得积分');
            $table->integer('consumed_points')->default(0)->comment('已消费积分');
            $table->tinyInteger('level')->default(1)->comment('用户等级：1青铜 2白银 3黄金 4钻石 5传奇');
            $table->string('level_name', 50)->default('青铜智者')->comment('等级名称');
            $table->timestamp('last_points_at')->nullable()->comment('最后获得积分时间');
            $table->timestamp('level_updated_at')->nullable()->comment('等级更新时间');
            $table->integer('version')->default(1)->comment('乐观锁版本号，防止并发问题');
            $table->integer('daily_points')->default(0)->comment('今日获得积分');
            $table->integer('weekly_points')->default(0)->comment('本周获得积分');
            $table->integer('monthly_points')->default(0)->comment('本月获得积分');
            $table->integer('year_points')->default(0)->comment('本年获得积分');
            $table->date('last_daily_reset')->nullable()->comment('日积分最后重置日期');
            $table->date('last_weekly_reset')->nullable()->comment('周积分最后重置日期');
            $table->date('last_monthly_reset')->nullable()->comment('月积分最后重置日期');
            $table->timestamps();

            $table->unique('user_id', 'uk_user_id');
            $table->index('current_points', 'idx_current_points');
            $table->index('total_points', 'idx_total_points');
            $table->index('level', 'idx_level');
            $table->index('daily_points', 'idx_daily_points');
            $table->index('last_points_at', 'idx_last_points_at');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_user_points` comment '积分系统-用户积分主表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_points');
    }
}