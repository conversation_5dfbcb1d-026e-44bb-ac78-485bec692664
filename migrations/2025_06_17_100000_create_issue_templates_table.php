<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueTemplatesTable extends Migration
{
      /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('issue_templates', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->integer('project_id')->comment('项目ID');
            $table->integer('tracker_id')->comment('事项类型ID (对应trackers表)');
            $table->string('name', 100)->comment('模板名称');
            $table->text('description')->nullable()->comment('模板描述');
            $table->json('checklist_items')->nullable()->comment('检查项JSON数据');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用 (1:启用, 0:禁用)');
            $table->integer('sort_order')->default(0)->comment('排序权重');
            $table->integer('created_by')->nullable()->comment('创建人ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间');
            
            $table->index(['project_id', 'tracker_id'], 'idx_project_tracker');
            $table->index('sort_order', 'idx_sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issue_templates');
    }
} 