<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreatePointRecordsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('point_records', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('point_type', 50)->comment('积分类型：publish_article, get_like, pin, delete_article, offline_training, special_reward等');
            $table->integer('point_change')->comment('积分变化量(正数增加，负数减少)');
            $table->integer('current_points')->comment('变化后的当前积分');
            $table->string('source_type', 50)->comment('来源类型：wiki_document, wiki_like, wiki_comment, training, manual等');
            $table->unsignedBigInteger('source_id')->nullable()->comment('来源对象ID');
            $table->string('source_title', 255)->nullable()->comment('来源对象标题');
            $table->string('description', 500)->comment('积分变化描述');
            $table->text('remark')->nullable()->comment('管理员备注(手动操作时)');
            $table->decimal('multiplier', 3, 2)->default(1.00)->comment('积分倍数(活动期间可能有倍数奖励)');
            $table->string('bonus_reason', 255)->nullable()->comment('额外奖励原因');
            $table->tinyInteger('is_daily_first')->default(0)->comment('是否为当日首次该类型操作');
            $table->tinyInteger('is_reversed')->default(0)->comment('是否已被撤销');
            $table->timestamp('reversed_at')->nullable()->comment('撤销时间');
            $table->string('reversed_reason', 255)->nullable()->comment('撤销原因');
            $table->unsignedBigInteger('created_by')->nullable()->comment('操作人(系统自动为NULL)');
            $table->string('ip_address', 45)->nullable()->comment('操作IP地址');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->timestamps();

            $table->index('user_id', 'idx_user_id');
            $table->index('point_type', 'idx_point_type');
            $table->index(['source_type', 'source_id'], 'idx_source');
            $table->index('created_at', 'idx_created_at');
            $table->index('point_change', 'idx_point_change');
            $table->index('is_reversed', 'idx_is_reversed');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_point_records` comment '积分系统-积分变动记录表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('point_records');
    }
}