<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestPlanTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_plan', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('title', 255)->comment('计划标题');
                $table->integer('project_id')->default(0)->comment('项目ID');
                $table->integer('version_id')->default(0)->comment('项目版本');
                $table->tinyInteger('status')->default(1)->comment('计划状态');
                $table->boolean('is_archived')->default(0)->comment('是否归档');
                $table->date('begin_date')->nullable()->comment('测试周期开始时间');
                $table->date('end_date')->nullable()->comment('测试周期截止时间');
                $table->text('description')->nullable()->comment('测试计划描述');
                $table->integer('created_by')->default(0)->comment('创建人');
                $table->integer('updated_by')->default(0)->comment('更新人');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间，默认当前时间，允许NULL');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间，允许NULL');
                //索引
                $table->index('project_id');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_plan');
        }
    }
