<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestCaseTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_case', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('library_id')->default(0)->comment('测试用例库ID');
                $table->integer('directory_id')->default(0)->comment('所在目录ID');
                $table->string('title', 255)->default('')->comment('用例标题');
                $table->integer('priority')->default(0)->comment('优先级');
                $table->integer('category_id')->nullable()->comment('模块（分类）');
                $table->text('precondition')->nullable()->comment('前置条件');
                $table->tinyInteger('desc_type')->default(0)->comment('文本描述或是步骤描述二选一，0-步骤描述，1-文本步骤描述，默认设置为0');
                $table->text('description')->nullable()->comment('文本描述（富文本）');
                $table->text('expected_result')->nullable()->comment('文本描述的预期结果（富文本）');
                $table->float('estimated_hours')->default(0)->comment('评估工时');
                $table->integer('created_by')->default(0)->comment('创建人ID');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_case');
        }
    }
