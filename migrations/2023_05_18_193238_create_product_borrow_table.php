<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductBorrowTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_product_borrow')) {
            Schema::create('oa_product_borrow', function (Blueprint $table) {
                $table->bigIncrements('id'); // 递增主键
                $table->integer('user_id')->comment('申请人ID');
                $table->char('workwx_userid', 32)->comment('申请人微信ID');
                $table->string('template_id', 64)->comment('审批模板ID');
                $table->json('approver')->nullable()->comment('审批人IDS');
                $table->json('approver_details')->nullable()->comment('审批人微信信息');
                $table->json('notifyer')->nullable()->comment('抄送人');
                $table->json('notifyer_details')->nullable()->comment('抄送人微信信息');
                $table->smallInteger('approver_attr')->nullable()->comment('审批方式 1-会签,2-或签');
                $table->string('prod_name', 100)->comment('产品名称');
                $table->string('prod_code', 200)->comment('产品料号');
                $table->string('prod_count', 100)->comment('产品数量');
                $table->dateTime('borrow_date')->comment('借用日期');
                $table->dateTime('return_date')->comment('归还日期');
                $table->string('sp_no', 16)->nullable()->comment('微信审批订单编号');
                $table->text('remark')->comment('微信审批用途字段');
                $table->tinyInteger('sp_status')->comment('申请状态(微信审批状态字段)');
                $table->tinyInteger('borrow_status')->comment('申请状态(整个单子的借用状态)');
                $table->dateTime('created_at'); // datetime
                $table->dateTime('updated_at'); // datetime
                $table->dateTime('deleted_at')->nullable(); // datetime
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_borrow', function (Blueprint $table) {
            //
        });
    }
}
