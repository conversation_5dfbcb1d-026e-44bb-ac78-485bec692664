<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateDocumentStatusTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wiki_document_status', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->integer('doc_id')->comment('文档ID');
            $table->string('status_type', 50)->comment('状态类型');
            $table->tinyInteger('status_value')->default(0)->comment('状态值 0:待审核/未通过 1:通过/启用 2:拒绝/禁用');
            $table->integer('created_by')->comment('操作人ID');
            $table->tinyInteger('added_points')->default(0)->comment('是否已添加积分');
            $table->dateTime('created_at')->nullable()->comment('创建时间');
            // $table->dateTime('expired_at')->nullable()->comment('过期时间');
            $table->dateTime('updated_at')->nullable()->comment('操作时间');
            $table->string('reason', 500)->nullable()->comment('操作原因/备注');
            
            // 创建索引
            $table->index(['doc_id', 'status_type'], 'idx_doc_status');
            $table->index('doc_id', 'idx_doc_id');
            $table->index('status_type', 'idx_status_type');
            $table->index('created_by', 'idx_created_by');
            // $table->index('expired_at', 'idx_expired_at');
            
            // 外键约束
            $table->foreign('status_type')->references('status_type')->on('wiki_document_status_types');
            
            $table->comment('文档状态记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wiki_document_status');
    }
}