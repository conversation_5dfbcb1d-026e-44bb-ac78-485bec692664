<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductParentTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_parent')) {
            Schema::create('product_parent', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->unsignedBigInteger('parent_id')->default(0)->comment('上级产品ID，记录的是projects.id');
                $table->unsignedBigInteger('child_id')->default(0)->comment('下级产品ID，记录的是projects.id');

                $table->unsignedInteger('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_parent', function (Blueprint $table) {
            //
        });
    }
}
