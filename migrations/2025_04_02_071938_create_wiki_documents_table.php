<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiDocumentsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_documents')) {
                Schema::create('wiki_documents', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('doc_id')->comment('文档ID');

                    // 字段
                    $table->bigInteger('space_id')->comment('所属空间ID');
                    $table->bigInteger('parent_id')->nullable()->comment('父文档 ID（用于文档层级关系）');
                    $table->string('path', 255)->default('')->comment('层级路径关系');
                    $table->string('title', 255)->comment('文档标题');
                    $table->longText('content_html')->nullable()->comment('HTML内容');
                    $table->longText('content_markdown')->nullable()->comment('Markdown内容');
                    $table->tinyInteger('current_editor_type')->comment('当前编辑器类型(1:HTML/2:MD)');
                    $table->bigInteger('created_by')->comment('创建人ID');
                    $table->bigInteger('updated_by')->comment('最后更新人ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');
                    $table->integer('view_count')->default(0)->comment('浏览次数');
                    $table->integer('like_count')->default(0)->comment('点赞数量');

                    // 索引
                    $table->index('space_id');
                    $table->index('parent_id');
                    $table->index('title');
                    $table->index('created_by');
                    $table->index('updated_by');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_documents');
        }
    }
