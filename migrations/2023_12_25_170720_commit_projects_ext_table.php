<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProjectsExtTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects_ext', function (Blueprint $table) {
            if (!Schema::hasColumn('projects_ext', 'module')) {
                $table->json('module')->nullable()->comment('数据模块');
            }
            if (!Schema::hasColumn('projects_ext', 'relation_project_id')) {
                $table->json('relation_project_id')->nullable()->comment('关联项目ID');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
