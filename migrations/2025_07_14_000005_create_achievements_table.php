<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAchievementsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('achievements', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('code', 50)->comment('成就代码(唯一标识)');
            $table->string('name', 100)->comment('成就名称');
            $table->text('description')->comment('成就描述');
            $table->string('icon', 255)->nullable()->comment('成就图标URL');
            $table->string('badge_image', 255)->nullable()->comment('成就徽章图片URL');
            $table->string('category', 50)->default('general')->comment('成就分类：article, interaction, training, special, milestone');
            $table->tinyInteger('difficulty')->default(1)->comment('难度等级：1简单 2普通 3困难 4史诗 5传说');
            $table->string('condition_type', 50)->comment('条件类型：article_count, like_count, total_views, consecutive_days等');
            $table->integer('condition_value')->comment('条件数值');
            $table->json('condition_config')->nullable()->comment('复杂条件配置');
            $table->integer('reward_points')->default(0)->comment('奖励积分');
            $table->json('reward_items')->nullable()->comment('奖励物品配置');
            $table->tinyInteger('is_hidden')->default(0)->comment('是否隐藏成就(用户看不到)');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用');
            $table->tinyInteger('is_repeatable')->default(0)->comment('是否可重复获得');
            $table->integer('display_order')->default(0)->comment('显示顺序');
            $table->tinyInteger('unlock_level')->default(1)->comment('解锁等级要求');
            $table->json('prerequisite_achievements')->nullable()->comment('前置成就要求');
            $table->timestamp('valid_from')->nullable()->comment('生效开始时间');
            $table->timestamp('valid_to')->nullable()->comment('生效结束时间');
            $table->string('tips', 500)->nullable()->comment('获得提示');
            $table->timestamps();

            $table->unique('code', 'uk_code');
            $table->index('category', 'idx_category');
            $table->index('difficulty', 'idx_difficulty');
            $table->index('condition_type', 'idx_condition_type');
            $table->index('is_active', 'idx_is_active');
            $table->index('is_hidden', 'idx_is_hidden');
            $table->index('display_order', 'idx_display_order');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_achievements` comment '积分系统-成就定义表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('achievements');
    }
}