<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;
    use Hyperf\DbConnection\Db;

    class ChangeOsProjectsInfoTable extends Migration
    {
        protected $connection = 'tchip_redmine';
        /**
         * Run the migrations.
         */
        public function up(): void
        {

            if (Schema::hasTable('projects_info')) {
                Schema::table('projects_info', function (Blueprint $table) {
                    $databaseConnection = 'tchip_redmine';

                    // 根据旧列的内容更新新列
                    Db::connection($databaseConnection)->table('projects_info')
                        ->where('os', 'Linux')
                        ->update(['new_os' => json_encode(['Linux'])]); // For Linux

                    Db::connection($databaseConnection)->table('projects_info')
                        ->where('os',  'Android')
                        ->update(['new_os' => json_encode(['Android'])]); // For Android


                    // 删除旧列
                Db::connection($databaseConnection)->statement('ALTER TABLE projects_info DROP COLUMN os');

                    // 将新列重命名为旧列的名称
                Db::connection($databaseConnection)->statement('ALTER TABLE projects_info CHANGE new_os os JSON DEFAULT NULL');
                });

            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::table('', function (Blueprint $table) {
                //
            });
        }
    }
