<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiCommentsTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_comments')) {
                Schema::create('wiki_comments', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('comment_id')->comment('评论唯一ID');

                    // 字段
                    $table->bigInteger('doc_id')->comment('关联的文档ID');
                    $table->bigInteger('user_id')->comment('评论用户ID');
                    $table->bigInteger('parent_id')->nullable()->comment('父评论ID(用于回复)');
                    $table->text('content')->comment('评论内容');
                    $table->tinyInteger('is_deleted')->default(0)->comment('是否删除(0:否 1:是)');
                    $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('doc_id');
                    $table->index('user_id');
                    $table->index('parent_id');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_comments');
        }
    }
