<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTestPlanCaseTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_plan_case', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('test_plan_id')->default(0)->comment('测试计划ID');
            $table->integer('test_case_id')->default(0)->comment('测试用例ID');
            $table->integer('assigned_user')->default(0)->comment('分配人');
            $table->tinyInteger('test_status')->default(1)->comment('测试状态');
            $table->integer('execution_count')->default(0)->comment('执行次数，默认0');
            $table->dateTime('created_at')->nullable()->comment('创建时间');
            $table->dateTime('updated_at')->nullable()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            //索引
            $table->index('test_plan_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_plan_case');
    }
}
