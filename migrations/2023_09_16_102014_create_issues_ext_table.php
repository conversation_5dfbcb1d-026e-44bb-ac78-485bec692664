<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssuesExtTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('issues_ext')) {
            Schema::create('issues_ext', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('issue_id')->comment('事项ID');
                $table->integer('release_version_id')->comment('发行版本ID');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issues_ext');
    }
}
