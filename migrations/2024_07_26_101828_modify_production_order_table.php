<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ModifyProductionOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('production_order')) {
            Schema::table('production_order', function (Blueprint $table) {
                if (!Schema::hasColumn('production_order', 'factory_id')) {
                    $table->integer('factory_id')->default(0)->comment('工厂id')->after('product_name')   ;
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_order', function (Blueprint $table) {
            //
        });
    }
}
