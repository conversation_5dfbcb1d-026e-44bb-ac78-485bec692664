<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateMarketingBrandTable extends Migration
{
    /**
     * Run the migrations.
     * 品牌销售推广汇总表
     */
    public function up(): void
    {
        if (!Schema::hasTable('marketing_brand_month_report')) {
            Schema::create('marketing_brand_month_report', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('brand_name', 32);
                $table->char('brand_code', 16)->comment('品牌的code');
                $table->date('date')->comment('日期');
                $table->decimal('sale_amount_quota', 12, 2)->default(0)->comment('销售额目标');
                $table->integer('sale_volume_quota')->default(0)->comment('销售量目标');
                $table->longText('expect_description')->nullable(true)->comment('预计推广效果描述');
                $table->longText('practical_description')->nullable(true)->comment('实际效果描述');

                $table->decimal('amount', 12, 2)->default(0)->comment('当月销售额');
                $table->integer('volume')->default(0)->comment('当月销售量');
                $table->integer('users_count')->default(0)->comment('总用户数');
                $table->integer('interact_count')->default(0)->comment('总互动数');
                $table->integer('exposure_count')->default(0)->comment('总曝光量');
                $table->decimal('click_rate', 6, 2)->default(0.00)->comment('总点击率(平均值)');
                $table->decimal('click_conversion_rate', 6, 2)->default(0.00)->comment('总点击国转化率(平均值)');
                $table->decimal('invest_return_rate', 6, 2)->default(0.00)->comment('总投资回报率(平均值)');
                $table->decimal('expect_amount', 12, 2)->default(0.00)->comment('总预计金额(计划)');
                $table->decimal('practical_amount', 12, 2)->default(0.00)->comment('总实际金额(计划)');
                $table->integer('expect_display')->default(0)->comment('预计展现量(计划)');
                $table->integer('practical_display')->default(0)->comment('实际展现量(计划)');
                $table->integer('expect_click')->default(0)->comment('预计点击量(计划)');
                $table->integer('practical_click')->default(0)->comment('实际点击量(计划)');
                $table->decimal('expect_ctr', 6, 2)->default(0.00)->comment('预计点击率%(计划)');
                $table->decimal('practical_ctr', 6, 2)->default(0.00)->comment('总实点击率%(计划)');
                $table->decimal('expect_relay', 6, 2)->default(0.00)->comment('预计转发率%(计划)');
                $table->decimal('practical_relay', 6, 2)->default(0.00)->comment('实际转发率%(计划)');
                $table->decimal('expect_invest_return', 6, 2)->default(0.00)->comment('预计投资回报率%(计划)');
                $table->decimal('practical_invest_return', 6, 2)->default(0.00)->comment('实际投资回报率%(计划)');
                $table->integer('expect_users')->default(0)->comment('预计用户量(计划)');
                $table->integer('practical_users')->default(0)->comment('实际用户量(计划)');
                $table->integer('expect_interact')->default(0)->comment('预计互动数(计划)');
                $table->integer('practical_interact')->default(0)->comment('实际互动数(计划)');
                $table->integer('expect_exposure')->default(0)->comment('预计曝光度(计划)');
                $table->integer('practical_exposure')->default(0)->comment('实际曝光度(计划)');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_brand_month_report');
    }
}
