<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductExtTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_ext')) {
            Schema::create('product_ext', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('product_id')->comment('产品ID');
                $table->tinyInteger('sale_client_id')->default(0)->comment('客户ID');
                $table->tinyInteger('urgency')->nullable()->comment('紧急程度');
                $table->bigInteger('shipment_predicted')->default(0)->comment('出货预测');
                $table->bigInteger('amount')->default(0)->comment('产量数量');
                $table->char('curr', 16)->default('')->comment('币种');
                // $table->smallInteger('customized_status')->nullable()->comment('定制状态');
                $table->decimal('cost', 10)->default(0)->comment('费用');
                $table->string('project_industry', 128)->nullable()->comment('项目行业');
                $table->string('company_website', 256)->nullable()->comment('公司网址');
                $table->text('company_background')->nullable()->comment('公司背景');
                // $table->longText('note')->nullable()->comment('备注');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_ext');
    }
}
