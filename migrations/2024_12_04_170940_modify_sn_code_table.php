<?php

use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class ModifySnCodeTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        if (Schema::hasTable('sn_code')) {
            Schema::table('sn_code', function (Blueprint $table) {
                if (!Schema::hasColumn('sn_code', 'origin_type')) {
                    $table->tinyInteger('origin_type')->default(1)->comment('类型,{1:天启2:客户}')->after('id');
                }
                if (!Schema::hasColumn('sn_code', 'sort')) {
                    $table->bigInteger('sort')->default(0)->comment('排序')->after('is_used');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sn_code', function (Blueprint $table) {
            //
        });
    }
}
