<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestDirectoryTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_directory', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('project_id')->default(0)->comment('项目id');
                $table->integer('parent_id')->default(0)->comment('父目录ID');
                $table->string('dir_type', 255)->default('')->comment('用以区分用例目录或是测试计划目录或其他类型');
                $table->integer('library_id')->default(0)->comment('用例库id（如果为用例目录）,默认0');
                $table->string('title', 255)->default('')->comment('目录标题');
                $table->text('description')->nullable()->comment('对目录的描述');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_directory');
        }
    }
