<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitAssembleOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order')) {
            Schema::table('assemble_order', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order', 'customer_remark')) {
                    $table->text('customer_remark')->nullable()->comment('客人备注')->after('checked_time');
                }
                if (!Schema::hasColumn('assemble_order', 'delivery_date')) {
                    $table->date('delivery_date')->nullable()->comment('交货日期')->after('checked_time');
                }
                if (!Schema::hasColumn('assemble_order', 'stock_order_code')) {
                    $table->string('stock_order_code')->default('')->comment('备货单号')->after('assemble_address');
                }
                if (!Schema::hasColumn('assemble_order', 'first_back_date')) {
                    $table->date('first_back_date')->nullable()->comment('首次回货日期')->after('delivery_date');
                }


            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order', function (Blueprint $table) {
            //
        });
    }
}
