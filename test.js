const puppeteer = require('puppeteer');

(async () => {
    const browser = await puppeteer.launch({
        executablePath: '/usr/bin/chromium-browser',
        args: ['--no-sandbox', '--headless', '--disable-gpu']
    });
    const page = await browser.newPage();
    // let url = 'https://www.baidu.com'
    let url = 'https://www.toutiao.com/c/user/token/MS4wLjABAAAAAlnxzkWa8sp-XpFWYzEXd368hmpR6k2qwKvoYkK1kS1UoPPj42CRUTm-nQHfXjNF/?source=mine_home&wid=1660026950650'
    await page.goto(url,
        {waitUntil: 'networkidle2'});

    await page.screenshot({path: 'example.png'});

    await browser.close();
})();