#!/bin/bash

# 解析命令行参数
SCAN_PATH=""
while [[ $# -gt 0 ]]; do
    case $1 in
        --path)
            SCAN_PATH="$2"
            shift 2
            ;;
        --path=*)
            SCAN_PATH="${1#*=}"
            shift
            ;;
        -h|--help)
            echo "用法: $0 [--path <路径>]"
            echo ""
            echo "选项:"
            echo "  --path <路径>    指定要扫描的路径（相对于项目根目录）"
            echo "  -h, --help       显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                           # 扫描所有Controller"
            echo "  $0 --path app/Controller/Api # 只扫描Api目录"
            echo "  $0 --path=app/Controller/User# 只扫描User控制器"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 创建scripts目录（如果不存在）
mkdir -p scripts

# 创建swagger输出目录
mkdir -p storage/swagger

# 生成OpenAPI 3.0文档
echo "正在生成 OpenAPI 3.0 文档..."
if [ -n "$SCAN_PATH" ]; then
    php scripts/generate_swagger.php --path="$SCAN_PATH"
else
    php scripts/generate_swagger.php
fi

# 检查OpenAPI 3.0生成结果
if [ -f "storage/swagger/openapi3.json" ]; then
    echo "✅ OpenAPI 3.0 文档生成成功！"
    
    # 转换为Swagger 2.0
    echo "正在转换为 Swagger 2.0 格式..."
    if command -v api-spec-converter &> /dev/null; then
        api-spec-converter --from=openapi_3 --to=swagger_2 --syntax=json storage/swagger/openapi3.json > storage/swagger/swagger.json
        
        if [ -f "storage/swagger/swagger.json" ]; then
            echo "✅ Swagger 2.0 转换成功！"
        else
            echo "❌ Swagger 2.0 转换失败"
            exit 1
        fi
    else
        echo "⚠️  api-spec-converter 未安装，跳过转换步骤"
        echo "请运行: npm install -g api-spec-converter"
        # 复制OpenAPI 3.0文件作为fallback
        cp storage/swagger/openapi3.json storage/swagger/swagger.json
    fi
    
    echo ""
    echo "📄 生成的文件："
    echo "  - OpenAPI 3.0: storage/swagger/openapi3.json"
    echo "  - Swagger 2.0: storage/swagger/swagger.json"
    echo "🌐 HTML 查看器: storage/swagger/index.html"
    echo ""
    echo "📌 如何使用："
    echo "1. 可以直接在浏览器中打开 storage/swagger/index.html 查看 API 文档"
    echo "2. 或者使用任何 HTTP 服务器启动该目录，例如："
    echo "   cd storage/swagger && python3 -m http.server 8080"
    echo "   然后访问 http://localhost:8080"
    echo "3. apipost选择swagger文件导入"
    echo ""
    echo "🔄 如需重新生成文档，直接运行："
    echo "   bash scripts/generate-swagger.sh [--path <路径>]"
else
    echo "❌ OpenAPI 文档生成失败"
    exit 1
fi 