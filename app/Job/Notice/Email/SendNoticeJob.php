<?php

declare(strict_types=1);

namespace App\Job\Notice\Email;

use App\Core\Services\Project\IssueService;
use App\Core\Utils\Log;
use App\Model\TchipBi\UserModel;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\Context;

class SendNoticeJob extends Job
{
    public $params;

    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected $maxAttempts = 2;

    /**
     * @param $params [user_id, workwx_tpl, email_tpl, website_tpl, workwx_tpl_params, email_tpl_params, website_tpl_params]
     */
    public function __construct($params)
    {
        // 这里最好是普通数据，不要使用携带 IO 的对象，比如 PDO 对象
        $this->params = $params;
    }

    /**
     * @return true
     */
    public function handle()
    {
        $user = UserModel::query()->where('id', $this->params['user_id'])->where('status', 1)->first();
        if ($user && $user->workwx_userid) {
            $tpl = $this->params['tpl'];

            if (!empty($this->params['method']) && method_exists($this, $this->params['method']) && !empty($this->params['params']['other_data'])) {
                $otherParams = call_user_func([$this, $this->params['method']], $this->params['params']['other_data']);
                // $this->params['params']['body_content'] = call_user_func([$this, $this->params['method']], $this->params['params']['other_data']);
                $this->params['params'] = array_merge($this->params['params'], $otherParams);
            }

            foreach ($this->params['params'] as $key => $val) {
                if (is_array($val)) continue;
                $tpl = str_replace('{$'.$key.'}', $val, $tpl);
            }

            // 优先使用传递的email_title作为邮件标题
            $title = $this->params['params']['email_title'] ?? $this->params['notice_title'] ?? '';
            if (empty($title) && !empty($this->params['notice_type'])) {
                switch ($this->params['notice_type']) {
                    case 'issue_assigned':
                    case 'issue_at':
                    case 'issue_subscribe':
                    case 'issue_journal':
                        $title = '事项通知';
                        break;
                    case 'system':
                        $title = '系统通知';
                        break;
                }
            }
            $title = $title ?? '数字天启通知';
            
            // 获取动态发件人名称
            $senderName = $this->params['params']['operator_name'] ?? '';
            if (empty($senderName)) {
                $senderName = '数字天启系统通知';
            }
            
//            Log::get('system', 'system')->info($tpl);
            // $mail = make(NoticeMail::class, [['tpl' => $tpl, 'title' => $title]]);
            // Mail::to('<EMAIL>')->send($mail);
            // $email = $user->biz_mail;
            $result = sendEmail($title, $tpl, $user->biz_mail, $senderName);
            if ($result) {
                Log::get('system', 'system')->info("{$user->biz_mail} 下发成功");
            }
            $saveData = $this->params;
            $saveData['status'] = $result ? 1 : 0;
            $saveData['content'] = $tpl;
            make(\App\Core\Services\Notice\UserNoticePushService::class)->doEdit(0, $saveData);
        }
        return true;
    }

    protected function everydayWorkRemind($redmineUserId)
    {
        $issueService= make(IssueService::class);
        Context::set('getIssueList', 'task');
        $issueList  = $issueService->lists(['isSendEmail' => true, 'assigned_to_id' => $redmineUserId, 'status_id' => implode(',', $issueService->getToDoStatusIds())], ['status_id' => 'IN'], 'id', 'DESC', 100);
        $issueList  = $issueList ?? [];
        $tpl = '';
        // 紧急图标
        // https://download.stationpc.cn/manager/uploads/20230907/c1fe8bfec3d58db7519312998c36e9b3.png
        // 高图标
        // https://download.stationpc.cn/manager/uploads/20230907/e67b538c55f3df0cf60261961a6a4224.png
        // 一般图标
        // https://download.stationpc.cn/manager/uploads/20230907/25cb6bddc6ad5cb0cf1679f1a0bbb20d.png
        // 低图标
        // https://download.stationpc.cn/manager/uploads/20230907/91c403bd243aaf70d36a99389128d99f.png

        if ($issueList) {
            $tpl = '<td class="block" style="padding-bottom: 24px;">
  <table>
      <thead>
      <tr class="table-header" style="padding: 12px 0;
          font-size: 12px;">
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">事项标题
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">优先级
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">状态
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">所属项目
          </th>
      </tr>
      </thead>
      <tbody>';
            foreach ($issueList as $item) {
                $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');

                // 优先级
                $priorityText = '';
                if (!empty($item['priority_id'])) {
                    switch ((int) $item['priority_id']) {
                        case 2 :
                            $priorityIcon = 'https://download.stationpc.cn/manager/uploads/20230907/91c403bd243aaf70d36a99389128d99f.png';
                            break;
                        case 4 :
                            $priorityIcon = 'https://download.stationpc.cn/manager/uploads/20230907/e67b538c55f3df0cf60261961a6a4224.png';
                            break;
                        case 5 :
                            $priorityIcon = 'https://download.stationpc.cn/manager/uploads/20230907/c1fe8bfec3d58db7519312998c36e9b3.png';
                            break;
                        default:
                            $priorityIcon = 'https://download.stationpc.cn/manager/uploads/20230907/25cb6bddc6ad5cb0cf1679f1a0bbb20d.png';
                    }
                    $item['priority_text'] = $item['priority_text'] ?? '';
                    $priorityText = <<<PRI
<div class="table-menuItem-priority" style="min-width: 62px;overflow: hidden;">
    <div class="table-menuItem-icon" style="float: left;margin-right: 4px;">
        <img src="{$priorityIcon}" style="float:left; width: 16px;height: 16px;" title="icon" class="preview-hover">
    </div>
    <div class="table-menuItem-priority-name" style="float: left;font-size: 13px;color: #455166;line-height: 16px;">{$item['priority_text']}</div>
</div>
PRI;
                }

                // 状态
                // 新建,处理中 = 蓝
                // 待验证  = 橙
                // 已关闭,挂起,已解决 = 绿
                switch ((int)$item['status_id']) {
                    case 3:
                    case 4:
                    case 5:
                        $color = 'color:#FE9900; background: #fff1d6';
                        break;
                    case 7:
                    case 8:
                    case 9:
                        $color = 'color:#18BE6A; background: #e5fcf4';
                    break;
                    default:
                        $color = 'color:#3977F3; background: #e8f4ff';
                }
                $item['issue_status']['name'] = $item['issue_status']['name'] ?? ' - ';
                $statusText = "<div class=\"table-menuItem-status table-menuItem-status_todo\" style=\"width: 48px;height: 20px;margin-right: 16px;text-align: center;
                               line-height: 20px;border-radius: 3px;font-size: 12px;$color\">{$item['issue_status']['name']}</div>";

                $overdue = '';
                if (!empty($item['due_date'])) {
                    $dueTimestamp = strtotime($item['due_date']);
                    $nowTimestamp = strtotime(date('Y-m-d 00:00:00'));

                    if ($dueTimestamp < $nowTimestamp) {
                        $due = '已超期';
                        $daysDifference = abs(floor(($dueTimestamp - $nowTimestamp) / (24 * 60 * 60)));
                        $overdue = "<div class=\"table-menuItem-title-deadline table-menuItem-title-deadline_due\" style=\"float: right;
                               padding: 0 4px;
                               height: 18px;
                               line-height: 18px;
                               border-radius: 3px;
                               font-size: 12px;
                               background:   #f24c3d ;
                               color:  #fff ;\">" . $due . " {$daysDifference} 天</div>";
                    } else {
                        $due = '剩余';
                        $daysDifference = abs(floor(($dueTimestamp - $nowTimestamp) / (24 * 60 * 60)));
                        $overdue = "<div class=\"table-menuItem-title-deadline table-menuItem-title-deadline_due\" style=\"float: right;
                               padding: 0 4px;
                               height: 18px;
                               line-height: 18px;
                               border-radius: 3px;
                               font-size: 12px;
                               color:  #999999 ;\">" . $due . " {$daysDifference} 天</div>";
                    }

                }

                $item['project_text']['name'] = $item['project_text']['name'] ?? ' - ';
                $tpl .= <<<EOT
<tr class="table-body" style="padding: 12px 0;width: 100%;">
    <td class="table-col table-col-1" style="border-bottom: 1px solid #f2f4f6;padding: 12px 0;">
          <div class="table-menuItem-title" style="width: 450px;
          padding-right: 16px;
          overflow: hidden;">
              <div class="table-menuItem-icon" style="float: left;
              margin-top: 2px;
              margin-right: 4px;
             ">
                  <img src="https://help-assets.codehub.cn/images/issue-type/requirement.svg" style="float:left; width: 16px;height: 16px;" title="icon" class="preview-hover">
              </div>
              <div class="table-menuItem-title-name" style="float: left;
              width: 300px;
              white-space: nowrap;
              word-break: break-all;
              text-overflow: ellipsis;
              font-size: 14px;
              line-height: 20px;
              height: 20px;
              color: #202d40;
              overflow: hidden;"><a target="_blank" href="{$host}/#/project/detail?project_id={$item['project_id']}&issue_id={$item['id']}" style="color: inherit;text-decoration: none;" title="{$item['subject']}">{$item['subject']}</a>
              </div>
              {$overdue}
          </div>
    </td>
    <td class="table-col table-col-2" style="border-bottom: 1px solid #f2f4f6;padding: 12px 0;">
          {$priorityText}
      </td>
      <td class="table-col tale-col-3" style="border-bottom: 1px solid #f2f4f6;padding: 12px 0;">
          {$statusText}
      </td>
      <td class="table-col table-col-4" style="border-bottom: 1px solid #f2f4f6;padding: 12px 0;">
          <div class="table-menuItem-belong" style="width: 140px;
          white-space: nowrap;
          word-break: break-all;
          text-overflow: ellipsis;
          font-size: 13px;
          color: #8590a6;
          overflow: hidden;">{$item['project_text']['name']}</div>
      </td>
</tr>
EOT;
            }

            $tpl .= '</tbody></table></td>';
        }
        return [
            'body_content' => $tpl,
            'todo_count'    => $issueService->toDoCount($redmineUserId, true),
            'overdue_count' => $issueService->overdueCount($redmineUserId),
        ];
    }
}