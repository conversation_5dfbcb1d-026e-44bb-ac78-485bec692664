<?php

declare(strict_types=1);

namespace App\Middleware;

use Hyperf\Context\Context;
use Hyperf\Snowflake\IdGeneratorInterface;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

class RequestMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @var ServerRequestInterface
     */
    protected $request;

    public function __construct(ContainerInterface $container, ServerRequestInterface $request)
    {
        $this->container = $container;
        $this->request = $request;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 为每一个请求增加一个qid
        $request = \Hyperf\Context\Context::override(ServerRequestInterface::class, function (ServerRequestInterface $request) {

            $request = $request->withAddedHeader('qid', $this->getRequestId());

            // 追加参数，由于前端分页与框架分页参数命名不同
            $queryParams = $request->getQueryParams();
            if(isset($queryParams['pageNo'])){
                $queryParams['page'] = $queryParams['pageNo'];
            }
            if(isset($queryParams['pageSize'])){
                $queryParams['limit'] = $queryParams['pageSize'];
            }
            $request = $request->withQueryParams($queryParams);
            return $request;
        });

        Context::set('http_request_flag', true);

        // 利用协程上下文存储请求开始的时间，用来计算程序执行时间
        Context::set('request_start_time', microtime(true));

        return $handler->handle($request);
    }

    protected function getRequestId()
    {
        $tmp = $this->request->getServerParams();
        $name = strtoupper(substr(md5(gethostname()), 12, 8));
        $remote = strtoupper(substr(md5($tmp['remote_addr']), 12, 8));
        $ip = strtoupper(substr(md5(getServerLocalIp()), 14, 4));
        return uniqid() . '-' . $remote . '-' . $ip . '-' . $name;
    }
}