<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Constants\StatusCode;
use App\Core\Services\AuthService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Qbhy\HyperfAuth\AuthManager;
use Qbhy\HyperfAuth\Exception\AuthException;
use Qbhy\SimpleJwt\Exceptions\JWTException;

class MenuMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    public function __construct(ContainerInterface $container, AuthManager $auth)
    {
        $this->container = $container;
        $this->auth = $auth;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 校验接口权限
        if (!$this->authService->isSuper()) {
            if (!$this->authService->check($request->getUri()->getPath())) {
                throw new AppException(StatusCode::ERR_FORBIDDEN, '你没有权限访问');
            }
        }
        return $handler->handle($request);
    }
}