<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Model\TchipBi\UserModel;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Qbhy\HyperfAuth\AuthManager;
use App\Core\Services\TchipBbs\UserService;

class BbsUserMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $user = UserModel::query()->find($this->auth->user()->getId());
        if ($user) {
            make(UserService::class)->initUser($user->id, $user->name, $user->biz_mail, $user->thumb_avatar);
        }
        return $handler->handle($request);
    }
}