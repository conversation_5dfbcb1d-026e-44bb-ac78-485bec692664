<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Constants\ProductionCode;
use App\Core\Services\Production\Log\ProductionOperationLogService;
use App\Core\Utils\Log;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Throwable;

class ProductionLogMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {

//        Context::set('production_log_qid', $qid);
        try{
            $response =  $handler->handle($request);
            $request->getQueryParams();
        }catch (Throwable $e){
            //记录操作日志
            // 重新抛出异常
            throw $e;
        }

        $this->processLog($request, $response);
        return  $response;
    }

    protected function processLog($request, $response)
    {
        try {
            $data = $this->getRequestInfo($request);
            $data['response_data'] = $response->getBody()->getContents();
            $data['response_data'] = json_decode($data['response_data'], true);
            make(ProductionOperationLogService::class)->recordLog($data);
        } catch (Throwable $e) {
            //记录报错日志
            Log::get('system', 'system')->info("日志报错：" . $e->getMessage());
            return;
        }
    }

    protected function getRequestInfo($request): array
    {
        $requestHeader = $request->getHeaders();
        $qid = $requestHeader['qid'][0] ?? uniqid();
        // 获取完整 URL（包含 query 参数）
        $url = $request->getUri()->__toString();

        // 获取请求方法
        $method = $request->getMethod();

        // 获取所有请求数据（包含 query 参数和 body）
        $queryParams = $request->getQueryParams();
        $bodyParams = $request->getParsedBody() ?? [];

        // 合并请求数据（注意：可能会有参数覆盖）
        $requestData = array_merge($queryParams, (array)$bodyParams);

        return [
            'type'         => ProductionCode::LOG_TYPE_HTTP_REQUEST,
            'qid'          => $qid,
            'url'          => $url,
            'method'       => strtoupper($method),
            'request_data' => $requestData,
        ];
    }
}