<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Constants\AutoUserCountCode;
use App\Core\Services\WorkWx\WorkWxBaseService;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * 企业微信中间件-通讯录access_token鉴权
 */
class WorkWxContactMiddleware implements MiddlewareInterface
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var WorkWxBaseService
     */
    protected $workWxBaseService;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if(!getCache(AutoUserCountCode::REDISKEY_WORKWX_CONTACT_TOKEN)){
            $this->workWxBaseService->getContactToken();
        }
        return $handler->handle($request);
    }
}