<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/10 上午10:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Listener;

use App\Core\Utils\Log;
use Hyperf\Crontab\Event\FailToExecute;
use Hyperf\Event\Annotation\Listener;

/**
 * @Listener()
 */
class FailToExecuteCrontabListener implements \Hyperf\Event\Contract\ListenerInterface
{

    /**
     * @inheritDoc
     */
    public function listen(): array
    {
        return [
            FailToExecute::class,
        ];
    }

    /**
     * @inheritDoc
     */
    public function process(object $event)
    {
        Log::get()->info($event->crontab->getName());
        Log::get()->info($event->throwable->getMessage());
    }
}