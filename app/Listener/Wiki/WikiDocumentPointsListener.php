<?php

declare(strict_types=1);

namespace App\Listener\Wiki;

use App\Core\Services\Points\PointService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Utils\PointTypeManager;
use App\Event\Wiki\WikiDocumentCreatedEvent;
use App\Event\Wiki\WikiDocumentDeletedEvent;
use App\Event\Wiki\WikiDocumentPinnedEvent;
use App\Event\Wiki\WikiDocumentLikedEvent;
use App\Event\Wiki\WikiDocumentStatusChangedEvent;
use App\Model\TchipBi\WikiDocumentStatusModel;
use App\Model\TchipBi\WikiDocumentStatusTypeModel;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * Wiki文档积分事件监听器
 * 处理文档相关的积分变动逻辑
 * @Listener
 */
class WikiDocumentPointsListener implements ListenerInterface
{
    private PointService $pointService;
    private LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->pointService = $container->get(PointService::class);
        $this->logger = $container->get(LoggerFactory::class)->get('points');
    }

    public function listen(): array
    {
        return [
            // 20250801 需要审核不允许自动增加积分
//             WikiDocumentCreatedEvent::class,
            WikiDocumentDeletedEvent::class,
            // WikiDocumentPinnedEvent::class,
            WikiDocumentLikedEvent::class,
            WikiDocumentStatusChangedEvent::class,
        ];
    }

    public function process(object $event): void
    {
        try {
            switch (get_class($event)) {
                case WikiDocumentCreatedEvent::class:
                    $this->handleDocumentCreated($event);
                    break;
                case WikiDocumentDeletedEvent::class:
                    $this->handleDocumentDeleted($event);
                    break;
                case WikiDocumentPinnedEvent::class:
                    $this->handleDocumentPinned($event);
                    break;
                case WikiDocumentLikedEvent::class:
                    $this->handleDocumentLiked($event);
                    break;
                case WikiDocumentStatusChangedEvent::class:
                    $this->handleDocumentStatusChanged($event);
                    break;
            }
        } catch (\Exception $e) {
            $this->logger->error('Wiki文档积分事件处理失败', [
                'event_class' => get_class($event),
                'event_data' => $event,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理文档创建事件
     */
    private function handleDocumentCreated(WikiDocumentCreatedEvent $event): void
    {
        $this->pointService->addPoints(
            $event->userId,
            'publish_article',
            'wiki_document',
            $event->documentId,
            '发布知识库文档：' . $event->title,
            [
                'source_title' => $event->title,
                'operator_id' => null
            ]
        );

        $this->logger->info('文档创建积分奖励成功', [
            'doc_id' => $event->documentId,
            'user_id' => $event->userId,
            'title' => $event->title
        ]);
    }

    /**
     * 处理文档删除事件
     */
    private function handleDocumentDeleted(WikiDocumentDeletedEvent $event): void
    {
        // $pointsToDeduct = PointTypeManager::getPointTypePoints('delete_article');
        // $this->pointService->deductPoints(
        //     $event->authorId,
        //     $pointsToDeduct,
        //     '文档被删除：' . $event->title,
        //     'wiki_document',
        //     $event->documentId,
        //     $event->operatorId,
        //     'delete_article',
        //     true, // 启用源验证
        //     'wiki_document', // 原始积分的source_type
        //     $event->documentId // 原始积分的source_id
        // );

        // 20250807 文档获取的积分通过审核后发放而非自动发放需要在删除时进行回收
        $pointsToDeduct = PointTypeManager::getPointTypePoints('publish_article');
        $this->pointService->deductPoints(
            $event->authorId,
            $pointsToDeduct,
            '文档被删除，回收审核通过所发放的知识豆：' . $event->title,
            'wiki_document_audit',
            $event->documentId,
            $event->operatorId,
            'publish_article',
            true, // 启用源验证
            'wiki_document_audit', // 原始积分的source_type
            $event->documentId // 原始积分的source_id
        );

        // 同时扣除培训认证积分
        $pointsToDeduct = PointTypeManager::getPointTypePoints('offline_training');
        $this->pointService->deductPoints(
            $event->authorId,
            $pointsToDeduct,
            '文档被删除，回收线下培训认证通过发放的知识豆文档：' . $event->title,
            'wiki_document_training',
            $event->documentId,
            $event->operatorId,
            'offline_training',
            true, // 启用源验证
            'wiki_document_training', // 原始积分的source_type
            $event->documentId // 原始积分的source_id
        );

        // 同时扣除精华认证积分
        $pointsToDeduct = PointTypeManager::getPointTypePoints('unmark_premium');
        $this->pointService->deductPoints(
            $event->authorId,
            $pointsToDeduct,
            '文档被删除，回收精华认证通过发放的知识豆文档：' . $event->title,
            'wiki_document_premium',
            $event->documentId,
            $event->operatorId,
            'unmark_premium',
            true, // 启用源验证
            'wiki_document_premium', // 原始积分的source_type
            $event->documentId // 原始积分的source_id
        );

        $this->logger->info('文档删除积分扣除成功', [
            'doc_id' => $event->documentId,
            'author_id' => $event->authorId,
            'operator_id' => $event->operatorId,
            'title' => $event->title,
            'points_deducted' => $pointsToDeduct
        ]);
    }

    /**
     * 处理文档置顶事件
     */
    private function handleDocumentPinned(WikiDocumentPinnedEvent $event): void
    {
        if ($event->isPinned) {
            // 置顶时：检查是否曾经被置顶过，如果没有则发放积分
            $everPinned = $event->extraData['ever_pinned'] ?? 0;
            
            if ($everPinned == 0) {
                // 首次置顶，发放积分
                $this->pointService->addPoints(
                    $event->authorId,
                    'pin',
                    'wiki_document',
                    $event->documentId,
                    '文档被置顶：' . $event->title,
                    [
                        'source_title' => $event->title,
                        'operator_id' => $event->operatorId
                    ]
                );

                $this->logger->info('文档首次置顶积分奖励成功', [
                    'doc_id' => $event->documentId,
                    'author_id' => $event->authorId,
                    'operator_id' => $event->operatorId,
                    'title' => $event->title,
                    'ever_pinned' => $everPinned
                ]);
            } else {
                $this->logger->info('文档重复置顶，跳过积分奖励', [
                    'doc_id' => $event->documentId,
                    'author_id' => $event->authorId,
                    'operator_id' => $event->operatorId,
                    'title' => $event->title,
                    'ever_pinned' => $everPinned
                ]);
            }
        } else {
            // 取消置顶时：检查撤销截止时间，如果已过期则不扣除积分
            $everPinned = $event->extraData['ever_pinned'] ?? 0;;

            // 被标记为置顶过，不扣除所得积分
            if ($everPinned == 1) {
                $this->logger->info('文档取消置顶，跳过积分扣除（已置顶超过一天)', [
                    'doc_id' => $event->documentId,
                    'author_id' => $event->authorId,
                    'operator_id' => $event->operatorId,
                    'title' => $event->title
                ]);
            } else {
                // 在截止时间内取消置顶，扣除积分
                $pointsToDeduct = PointTypeManager::getPointTypePoints('pin');
                
                $this->pointService->deductPoints(
                    $event->authorId,
                    $pointsToDeduct,
                    '文档取消置顶：' . $event->title,
                    'wiki_document',
                    $event->documentId,
                    $event->operatorId,
                    'unpin',
                    true, // 启用源验证
                    'wiki_document', // 原始积分的source_type
                    $event->documentId // 原始积分的source_id
                );

                $this->logger->info('文档取消置顶积分扣除成功', [
                    'doc_id' => $event->documentId,
                    'author_id' => $event->authorId,
                    'operator_id' => $event->operatorId,
                    'title' => $event->title,
                    'points_deducted' => $pointsToDeduct,
                ]);
            }
        }
    }

    /**
     * 处理文档点赞事件
     */
    private function handleDocumentLiked(WikiDocumentLikedEvent $event): void
    {
        if ($event->isLiked) {
            // 点赞奖励积分
            $this->pointService->addPoints(
                $event->authorId,
                'get_like',
                'wiki_document',
                $event->documentId,
                '文档被点赞：' . $event->title,
                [
                    'source_title' => $event->title,
                    'operator_id' => $event->likerId
                ]
            );

            $this->logger->info('文档点赞积分奖励成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'liker_id' => $event->likerId,
                'title' => $event->title
            ]);
        } else {
            // 取消点赞扣除积分
            $pointsToDeduct = PointTypeManager::getPointTypePoints('cancel_like');
            
            $this->pointService->deductPoints(
                $event->authorId,
                $pointsToDeduct,
                '文档取消点赞：' . $event->title,
                'wiki_document',
                $event->documentId,
                $event->likerId,
                'cancel_like',
                true, // 启用源验证
                'wiki_document', // 原始积分的source_type
                $event->documentId // 原始积分的source_id
            );

            $this->logger->info('文档取消点赞积分扣除成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'liker_id' => $event->likerId,
                'title' => $event->title,
                'points_deducted' => $pointsToDeduct
            ]);
        }
    }

    /**
     * 处理文档状态变更事件
     */
    private function handleDocumentStatusChanged(WikiDocumentStatusChangedEvent $event): void
    {
        switch ($event->statusType) {
            case WikiDocumentStatusTypeModel::TYPE_AUDIT:
                $this->handleAuditStatusChange($event);
                break;
            case WikiDocumentStatusTypeModel::TYPE_PREMIUM:
                $this->handlePremiumStatusChange($event);
                break;
            case WikiDocumentStatusTypeModel::TYPE_TRAINING:
                $this->handleTrainingStatusChange($event);
                break;
        }
    }

    /**
     * 处理审核状态变更
     */
    private function handleAuditStatusChange(WikiDocumentStatusChangedEvent $event): void
    {
        // 审核通过时给予积分奖励
        if ($event->newStatusValue === WikiDocumentStatusModel::STATUS_APPROVED  && $event->addPoint) {

            // 重复发放校验
            if ($this->pointService->isPointAdded('publish_article', 'wiki_document_audit', $event->documentId)) {
                $this->logger->info('文档审核通过积分奖励已发放，跳过', [
                    'doc_id' => $event->documentId,
                    'author_id' => $event->authorId,
                    'operator_id' => $event->operatorId,
                    'title' => $event->title,
                    'points_added' => $event->addedPoints
                ]);
                return;
            }

            // 发布文档积分奖励（审核通过后才能真正发布）
            $this->pointService->addPoints(
                $event->authorId,
                'publish_article',
                'wiki_document_audit',
                $event->documentId,
                '经由文档审核添加：' . $event->title,
                [
                    'source_title' => $event->title,
                    'operator_id' => $event->operatorId
                ]
            );

            $this->logger->info('文档审核通过积分奖励成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title
            ]);
        }
        // 审核状态恢复到未审核时或审核状态下取消勾选发放积分选项 扣除积分
        elseif (($event->newStatusValue === WikiDocumentStatusModel::STATUS_PENDING &&
                 $event->oldStatusValue === WikiDocumentStatusModel::STATUS_APPROVED)
            || (!$event->addPoint && $event->newStatusValue !== WikiDocumentStatusModel::STATUS_REJECTED) // 20250807 拒绝状态不扣除积分
        ) {

            // 获取发布文档的积分配置
            $pointsToDeduct = PointTypeManager::getPointTypePoints('publish_article');

            $this->pointService->deductPoints(
                $event->authorId,
                $pointsToDeduct,
                '经由文档审核扣除：' . $event->title,
                'wiki_document_audit',
                $event->documentId,
                $event->operatorId,
                'publish_article',
                true, // 启用源验证
                'wiki_document_audit', // 原始积分的source_type
                $event->documentId // 原始积分的source_id
            );

            $this->logger->info('经由文档审核扣除', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title,
                'points_deducted' => $pointsToDeduct
            ]);
        }
        // 审核被拒绝时发送通知
        elseif ($event->newStatusValue === WikiDocumentStatusModel::STATUS_REJECTED) {
            // 发送文档审核拒绝通知
            $notificationData = [
                'doc_id' => $event->documentId,
                'title' => $event->title,
                'operator_id' => $event->operatorId,
                'reason' => $event->reason,
            ];

            // 使用 make() 函数避免循环依赖
            $noticeService = make(NoticeService::class);
            $noticeService->wikiDocumentReject($event->authorId, $notificationData);

            $this->logger->info('文档审核拒绝通知发送成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title,
                'reason' => $event->reason
            ]);
        }
    }

    /**
     * 处理精华认证状态变更
     */
    private function handlePremiumStatusChange(WikiDocumentStatusChangedEvent $event): void
    {
        if ($event->newStatusValue === WikiDocumentStatusModel::STATUS_APPROVED &&
            $event->oldStatusValue !== WikiDocumentStatusModel::STATUS_APPROVED) {
            
            // 精华认证积分奖励
            $this->pointService->addPoints(
                $event->authorId,
                'mark_premium',
                'wiki_document_premium',
                $event->documentId,
                '文档被认证为精华：' . $event->title,
                [
                    'source_title' => $event->title,
                    'operator_id' => $event->operatorId
                ]
            );

            $this->logger->info('文档精华认证积分奖励成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title
            ]);
        } elseif ($event->newStatusValue !== WikiDocumentStatusModel::STATUS_APPROVED &&
                  $event->oldStatusValue === WikiDocumentStatusModel::STATUS_APPROVED) {
            
            // 取消精华认证，扣除积分
            $pointsToDeduct = PointTypeManager::getPointTypePoints('unmark_premium');
            
            $this->pointService->deductPoints(
                $event->authorId,
                $pointsToDeduct,
                '文档取消精华认证：' . $event->title,
                'wiki_document_premium',
                $event->documentId,
                $event->operatorId,
                'unmark_premium',
                true, // 启用源验证
                'wiki_document_premium', // 原始积分的source_type
                $event->documentId // 原始积分的source_id
            );

            $this->logger->info('文档取消精华认证积分扣除成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title,
                'points_deducted' => $pointsToDeduct
            ]);
        }
    }

    /**
     * 处理培训认证状态变更
     */
    private function handleTrainingStatusChange(WikiDocumentStatusChangedEvent $event): void
    {
        if ($event->newStatusValue === WikiDocumentStatusModel::STATUS_APPROVED &&
            $event->oldStatusValue !== WikiDocumentStatusModel::STATUS_APPROVED) {
            
            // 培训认证积分奖励
            $this->pointService->addPoints(
                $event->authorId,
                'offline_training',
                'wiki_document_training',
                $event->documentId,
                '文档被认证为线下培训认证：' . $event->title,
                [
                    'source_title' => $event->title,
                    'operator_id' => $event->operatorId
                ]
            );

            $this->logger->info('文档线下培训认证积分奖励成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title
            ]);
        } elseif ($event->newStatusValue !== WikiDocumentStatusModel::STATUS_APPROVED &&
                  $event->oldStatusValue === WikiDocumentStatusModel::STATUS_APPROVED) {
            
            // 取消培训认证，扣除积分
            $pointsToDeduct = PointTypeManager::getPointTypePoints('offline_training');
            
            $this->pointService->deductPoints(
                $event->authorId,
                $pointsToDeduct,
                '文档取消线下培训认证：' . $event->title,
                'wiki_document_training',
                $event->documentId,
                $event->operatorId,
                'offline_training',
                true, // 启用源验证
                'wiki_document_training', // 原始积分的source_type
                $event->documentId // 原始积分的source_id
            );

            $this->logger->info('文档取消线下培训认证积分扣除成功', [
                'doc_id' => $event->documentId,
                'author_id' => $event->authorId,
                'operator_id' => $event->operatorId,
                'title' => $event->title,
                'points_deducted' => $pointsToDeduct
            ]);
        }
    }
}