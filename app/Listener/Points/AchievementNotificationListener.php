<?php

declare(strict_types=1);

namespace App\Listener\Points;

use App\Event\Points\AchievementUnlockedEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * 成就解锁通知监听器（未启用）
 * 处理成就解锁后的通知逻辑
 * @Listener
 */
class AchievementNotificationListener implements ListenerInterface
{
    private LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->logger = $container->get(LoggerFactory::class)->get('achievement');
    }

    public function listen(): array
    {
        return [
            AchievementUnlockedEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof AchievementUnlockedEvent) {
            return;
        }

        try {
            // 记录成就解锁
            $this->logger->info('用户获得新成就', [
                'user_id' => $event->userId,
                'achievement_id' => $event->achievementId,
                'achievement_code' => $event->achievementCode,
                'achievement_name' => $event->achievementName,
                'reward_points' => $event->rewardPoints,
                'unlocked_at' => $event->unlockedAt
            ]);

            // todos:这里可以添加更多通知逻辑，比如：
            // 1. 发送系统通知
            // 2. 发送邮件通知
            // 3. 推送到前端
            // 4. 记录到用户动态
            // 5. 触发其他业务逻辑

            // 示例：可以在这里集成系统通知
            $this->sendSystemNotification($event);

        } catch (\Exception $e) {
            $this->logger->error('成就解锁通知处理失败', [
                'user_id' => $event->userId,
                'achievement_id' => $event->achievementId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 发送系统通知
     */
    private function sendSystemNotification(AchievementUnlockedEvent $event): void
    {
        // todo:这里可以集成系统的通知服务
        // 例如：NotificationService::send($event->userId, '成就解锁', $message);
        
        $this->logger->info('成就解锁系统通知已发送', [
            'user_id' => $event->userId,
            'achievement_name' => $event->achievementName
        ]);
    }
}