<?php

declare(strict_types=1);

namespace App\Listener\Points;

use App\Core\Services\Points\LevelService;
use App\Event\Points\PointsChangedEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * 等级升级事件监听器
 * 监听积分变化事件，自动检查并处理用户等级升级
 * @Listener
 */
class LevelUpgradeListener implements ListenerInterface
{
    private LevelService $levelService;
    private LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->levelService = $container->get(LevelService::class);
        $this->logger = $container->get(LoggerFactory::class)->get('level');
    }

    public function listen(): array
    {
        return [
            PointsChangedEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof PointsChangedEvent) {
            return;
        }

        try {
            // 检查用户等级升级
            $upgradeResult = $this->levelService->checkAndUpgradeLevel($event->userId);

            if ($upgradeResult['upgraded']) {
                $this->logger->info('用户等级升级成功', [
                    'user_id' => $event->userId,
                    'trigger_action' => $event->actionType,
                    'trigger_points' => $event->pointChange,
                    'old_level' => $upgradeResult['old_level'],
                    'old_level_name' => $upgradeResult['old_level_name'],
                    'new_level' => $upgradeResult['new_level'],
                    'new_level_name' => $upgradeResult['new_level_name'],
                    'upgrade_reward' => $upgradeResult['upgrade_reward'] ?? 0,
                    'current_points' => $upgradeResult['current_points'] ?? 0
                ]);
            } else {
                $this->logger->info('用户等级检查完成，无需升级', [
                    'user_id' => $event->userId,
                    'trigger_action' => $event->actionType,
                    'trigger_points' => $event->pointChange,
                    'current_level' => $upgradeResult['current_level'] ?? null,
                    'message' => $upgradeResult['message'] ?? ''
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('等级升级检查失败', [
                'user_id' => $event->userId,
                'trigger_action' => $event->actionType,
                'trigger_points' => $event->pointChange,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}