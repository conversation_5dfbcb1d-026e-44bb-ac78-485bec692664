<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/2/22 下午5:56
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Exception;

use App\Constants\StatusCode;
use Throwable;

class AppException extends \Hyperf\Server\Exception\ServerException
{
    public function __construct(int $code = 0, string $message = null, Throwable $previous = null)
    {
        if (is_null($message)) {
            $message = StatusCode::getMessage($code);
        }

        parent::__construct($message, $code, $previous);
    }
}