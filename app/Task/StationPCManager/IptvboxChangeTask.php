<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/2/13 下午4:43
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Task\StationPCManager;

use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;
use App\Core\Services\StationPCManager\IptvBoxFileService;

/**
 * 检测iptvbox文件源是否变化如果存在变化并通知相关人员进行操作,每周一至周五早上10点30分运行
 * @Crontab(name="IptvBoxChange", rule="20 10 * * 1-5", memo="每天早上10点半检测iptvbox文件", enable=false)
 */
class IptvboxChangeTask
{
    /**
     * @return void
     */
    public function execute()
    {
        if(env('STATIONPC_IPTVBOX_CHANGE', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始检测iptvbox文件');
            make(IptvBoxFileService::class)->checkFileChange();
            Log::get('system', 'system')->info("检测完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("检测iptvbox文件变化任务未开启");
        }

    }
}