<?php

namespace App\Task\TchipSale;

/**
 * 定时任务
 * 销售系统订单类型定时任务
 */
class OrderTask extends \App\Task\AbstractBaseTask
{
    /**
     * 推送订单信息到人员
     * (name="OrderMsgToUser", rule="00 09 * * 1-5", callback="execute", memo="每天发送订单信息给销售员", enable=false)
     * @return void
     */
    public function orderMsgToUser()
    {
        make(\App\Core\Services\TchipSale\OrderService::class)->orderMsgToUser();
    }
}