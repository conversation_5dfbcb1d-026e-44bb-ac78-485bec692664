<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/6 下午4:43
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Task\User;

use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\UserService;
use App\Core\Services\WorkWx\WorkWxUserService;

/**
 * 用户相关定时任务
 * @Crontab(name="SyncWorkWxUser", rule="41 10 * * *", memo="用户相关定时任务", enable=false)
 */
class UserTask
{
    /**
     * @WorkWxTokenAnnotation(type="contact")
     * @return void
     */
    public function initUsersPassword()
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始初始化用户密码');
        make(\App\Core\Services\UserService::class)->initUsersPassword();
        Log::get('system', 'system')->info("开始工作完成");
        Log::get('system', 'system')->info('==============================================================================');
    }
}