<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/6 下午4:43
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Task\User;

use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\UserService;
use App\Core\Services\WorkWx\WorkWxUserService;

/**
 * 同步企微信息到bi再到redmine,每天09:00运行一次
 * @Crontab(name="SyncWorkWxUser", rule="41 10 * * *", memo="每天09：00同步企微信息到bi再到redmine", enable=false)
 */
class SyncWorkWxUserTask
{
    /**
     * @WorkWxTokenAnnotation(type="contact")
     * @return void
     */
    public function execute()
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始同步企业微信用户信息到系统');
        // 1.先同步部门信息
        make(\App\Core\Services\WorkWx\WorkWxDepartmentService::class)->syncDepartmentList();
        // 2.同步企微信息到bi
        make(WorkWxUserService::class)->syncUserList();
        // 3.bi同步到redmine
        make(UserService::class)->syncThirdByRedmine();
        Log::get('system', 'system')->info("开始工作完成");
        Log::get('system', 'system')->info('==============================================================================');
    }
}