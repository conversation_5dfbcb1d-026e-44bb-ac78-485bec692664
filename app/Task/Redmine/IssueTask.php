<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/2/13 下午4:43
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Task\Redmine;

use App\Constants\IssueCode;
use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;

/**
 * redmine事项相关任务
 */
class IssueTask
{
    /**
     *
     * @return void
     */
    public function syncIssueAssigned()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始同步处理人到处理表');
            make(\App\Core\Services\Project\IssueService::class)->syncIssueAssigned();
            Log::get('system', 'system')->info("同步完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }
}