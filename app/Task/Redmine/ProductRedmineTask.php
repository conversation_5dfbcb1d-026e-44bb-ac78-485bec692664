<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/2/13 下午4:43
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Task\Redmine;

use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;

/**
 * redmine模块中产品相关任务
 */
class ProductRedmineTask
{
    /**
     *
     * @return void
     */
    public function migrateHandleId()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始迁移处理人数据到新字段');
            make(\App\Core\Services\Product\ProductService::class)->migrateHandleId();
            Log::get('system', 'system')->info("迁移完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }

    }

    public function saleFollowMigrateProduct()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始迁移销售系统中定制项目数据到REDMINE产品中');
            make(\App\Core\Services\Product\ProductService::class)->saleFollowMigrateProduct();
            Log::get('system', 'system')->info("迁移完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function uploadProductDescValue()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始更新product_desc_value数据表内容');
            make(\App\Core\Services\Product\ProductService::class)->uploadProductDescValue();
            Log::get('system', 'system')->info("更新完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function addDefaultMember()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始对产品加入默认成员');
            make(\App\Core\Services\Product\ProductMembersService::class)->addDefaultMember();
            Log::get('system', 'system')->info("加入完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function productMigrateProject()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始迁移产品数据到项目');
            make(\App\Core\Services\Product\ProductService::class)->productMigrateProject();
            Log::get('system', 'system')->info("迁移完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function migrateSaleFollowDetailsToProjectProgress()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('开始迁移销售系统定制项目跟进信息到数字天启产品项目中');
            make(\App\Core\Services\Project\ProjectsProgressService::class)->migrateSaleFollowDetailsToProjectProgress();
            Log::get('system', 'system')->info("迁移完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }

    public function swapRelationPorjectAndProductId()
    {
        if(env('CRONTAB_ONOFF', false) === true){
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('将关联产品的值所在字段relation_projects_id改为relation_product_id');
            make(\App\Core\Services\Project\ProjectsProgressService::class)->swapRelationPorjectAndProductId();
            Log::get('system', 'system')->info("完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("定时任务未开启");
        }
    }
}