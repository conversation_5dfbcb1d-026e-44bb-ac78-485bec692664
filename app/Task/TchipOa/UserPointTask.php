<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/6 下午4:43
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Task\TchipOa;

use App\Core\Services\Points\PointService;
use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;
use App\Core\Services\TchipOa\OaReportService;

/**
 * 初始化所有人积分信息
 * @Crontab(name="CRONTAB_ONOFF", rule="0 0 31 2 0", memo="初始化所有人积分信息", enable=false)
 */
class UserPointTask
{
    /**
     * @return void
     */
    public function initAllUserPoint()
    {
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info('初始化所有人积分信息');
            make(Pointservice::class)->initAllUserPoint();
            Log::get('system', 'system')->info("积分初始化完成");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info('初始化所有人积分信息未开启.');
        }
    }
}