<?php

namespace App\Task\Production;


use App\Core\Services\AssembleOrder\AssembleOrderAttachmentService;
use App\Core\Services\AssembleOrder\AssembleOrderService;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\Product\ProductChangeRecordService;
use App\Core\Services\ProductionOrder\ProductionOrderInfoService;
use App\Core\Services\SnCode\SnCodeService;
use App\Core\Utils\Log;


class OrderTask
{
    /**
     * @return void
     */
    public function updateOrderFlow()
    {
        $taskName = '更新订单工作流';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            make(ProductionOrderInfoService::class)->updateOrderFlow();
            Log::get('system', 'system')->info("执行结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }

    /**
     * 生产管理用来添加数据或者修复数据专用任务
     * @return void
     */
    public function updateProduction(){
        $taskName = '修正生产数据';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
//            make(AssembleOrderAttachmentService::class)->formatAttachmentData();
//            make(AssembleOrderService::class)->updateWorkFlow();
//            make(AssembleOrderService::class)->addWorkFlow();
//            make(MacAddressService::class)->fixRelateOrder();
//            make(ProductionOrderInfoService::class)->fixProductionOrderWorkStatus();
//            make(MacAddressService::class)->addHistoryProductCode();
//            make(SnCodeService::class)->addHistoryProductCode();
//            make(ProductionOrderInfoService::class)->updateHistoryLayoutUser();
            // make(ProductChangeRecordService::class)->dealHistoryProductChangeNotice();
            // 修复错误的SN号数据（基于sn_no_range生成的前导零丢失问题）
            Log::get('system', 'system')->info("开始修复错误的SN号数据");
            $result = make(SnCodeService::class)->fixIncorrectSnData(false);
            Log::get('system', 'system')->info("修复错误的SN号数据结果: " . json_encode($result));
            Log::get('system', 'system')->info("执行结束");
            // 更新齐料状态
            Log::get('system', 'system')->info("开始更新齐料状态");
            make(AssembleOrderService::class)->addTagFormula();
            Log::get('system', 'system')->info("更新齐料状态结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }
}