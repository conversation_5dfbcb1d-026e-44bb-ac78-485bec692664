<?php

namespace App\Task\Production;

use App\Core\Services\AssembleOrder\AssembleOrderService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\Production\Log\ProductionOperationLogService;
use App\Core\Services\ProductionOrder\ProductionOrderService;
use App\Core\Services\Product\ProductChangeRecordService;
use App\Core\Utils\Log;
use Hyperf\Crontab\Annotation\Crontab;
use Exception;
use Throwable;

class OrderRemindTask
{
    /**
     * @return void
     */
    public function remindEditPredictTime()
    {
        $taskName = '生产订单上线日期未填通知';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            make(NoticeService::class)->remindEditOrderPredictTime();
            Log::get('system', 'system')->info("开始执行");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }

    public function overPredictOnlineTime()
    {
        $taskName = '生产订单上线时间逾期通知';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            make(NoticeService::class)->overPredictOnlineTimeNotice();
            Log::get('system', 'system')->info("开始执行");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }

    public function syncProductionOrder()
    {
        $taskName = '生产订单自动同步创建';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            if(!make(ProductionOrderService::class)->syncStart(true)){
                Log::get('system', 'system')->info("有同步进行中，终止任务,任务结束");
                return;
            };
            try {
                $qid = uniqid();
                $result = make(ProductionOrderService::class)->syncProductionOrder();
            } catch (Throwable $e) {
                Log::get('system', 'system')->info('同步失败', ['error' => $e->getMessage()]);
                $result = [
                    'status' => 2,
                    'result' => $e->getMessage()
                ];
            } finally {
                make(ProductionOrderService::class)->syncEnd();
                try {
                    $class = get_class($this);
                    $method = __FUNCTION__;
                    $result['qid'] = $qid;
                    $result['class'] = $class;
                    $result['method'] = $method;
                    $result['order_type'] = 1;
                    make(ProductionOperationLogService::class)->recordTaskLog($result);
                } catch (Throwable $e) {
                    Log::get('system', 'system')->info("[{$qid}]同步失败", ['error' => $e->getMessage()]);
                }
            }

            Log::get('system', 'system')->info("任务结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }

    /**
     * @throws Throwable
     */
    public function syncAssembleOrder()
    {
        $taskName = '组装订单自动同步创建';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            if(!make(AssembleOrderService::class)->syncStart(true)){
                Log::get('system', 'system')->info("有同步进行中，终止任务,任务结束");
                return;
            };
            try {
                $qid = uniqid();
                $result = make(AssembleOrderService::class)->syncAssembleOrder();
            } catch (Throwable $e) {
                Log::get('system', 'system')->info('同步失败', ['error' => $e->getMessage()]);
                $result = [
                    'status' => 2,
                    'result' => $e->getMessage()
                ];
            } finally {
                make(AssembleOrderService::class)->syncEnd();
                try {
                    $class = get_class($this);
                    $method = __FUNCTION__;
                    $result['qid'] = $qid;
                    $result['class'] = $class;
                    $result['method'] = $method;
                    $result['order_type'] = 2;
                    make(ProductionOperationLogService::class)->recordTaskLog($result);
                } catch (Throwable $e) {
                    Log::get('system', 'system')->info("[{$qid}]同步失败", ['error' => $e->getMessage()]);
                }

            }

            Log::get('system', 'system')->info("任务结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }

    public function updateAssembleOrder()
    {
        $taskName = '组装订单同步更新';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            try {
                $qid = uniqid();
                $result = make(AssembleOrderService::class)->updateAssembleOrder();
            } catch (Throwable $e) {
                Log::get('system', 'system')->info("[{$qid}]同步失败", ['error' => $e->getMessage()]);
                $result = [
                    'status'         => 2,
                    'crontab_result' => $e->getMessage()
                ];
            } finally {
                try {
                    $class = get_class($this);
                    $method = __FUNCTION__;
                    $result['qid'] = $qid;
                    $result['class'] = $class;
                    $result['method'] = $method;
                    $result['order_type'] = 2;
                    make(ProductionOperationLogService::class)->recordTaskLog($result);
                } catch (Throwable $e) {
                    Log::get('system', 'system')->info("[{$qid}]同步失败", ['error' => $e->getMessage()]);
                }
            }

            Log::get('system', 'system')->info("任务结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }
}