<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/04
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Task\TongjiBd;

use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\Crontab\Annotation\Crontab;
use Hyperf\Di\Annotation\Inject;
use App\Model\TongjiSite\TongjiSiteModel;
use App\Core\Services\TongjiBd\TongjiBdReportService;

/**
 * 定时任务，百度网站统计-每天1点运行一次
 * @Crontab(name="Report", rule="01 01 * * *", callback="execute", memo="百度统计定时任务")
 */
class ReportTask
{
    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    /**
     * @Inject()
     * @var \App\Core\Services\TongjiBd\TongjiBdReportService
     */
    protected $tongjiBdReportService;

    public function execute()
    {
        // var_dump(date('Y-m-d H:i:s') . '-定时任务执行');
        // $this->logger->info(date('Y-m-d H:i:s', time()));
        $this->rptSite();
    }

    protected function rptSite()
    {
        $rows = TongjiSiteModel::query()->where('status', 1)->get();
        $dayTime = strtotime('-1 day', time());
        foreach ($rows as $row){
            // $this->tongjiBdReportService->reportDayTrend($row->site_id, $dayTime);
            // 受访，登陆页面统计
            $this->tongjiBdReportService->reportCommonTrack($row->site_id, $dayTime);
            // 来源数据-按类型-pc
            $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_type, 'pc');
            // 来源数据-按类型-mobile
            $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_type,'mobile');
            // 来源数据-按来源-pc
            $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_site, 'pc');
            // 来源数据-按来源-mobile
            $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_site,'mobile');
            // 地区数据-按省
            $this->tongjiBdReportService->visitAreaRpt($row->site_id, $dayTime);
            // 地区数据-国家
            $this->tongjiBdReportService->visitAreaRpt($row->site_id, $dayTime, 'world');
        }
    }
}
