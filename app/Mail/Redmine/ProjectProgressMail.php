<?php

namespace App\Mail\Redmine;

use HyperfExt\Mail\Mailable;

class ProjectProgressMail extends Mailable
{

    public $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * 构造消息。
     *
     * @return $this
     */
    public function build()
    {
        if (!empty($this->service->progress['product']['name'])) {
            $subject = '产品跟进通知';
            $str = $this->service->progress['product']['name']. ' 关联项目 ' . $this->service->progress['project_text']['name'] ?? '-';
            $url = biFrontendHost().'/project/productDetailsIndex?product_id='.$this->service->progress['product']['product_id'];
        } else {
            $subject = '项目跟进通知';
            $str = $this->service->progress['project_text']['name'] ?? '-';
            $url = biFrontendHost().'/project/pendingPlan?project_id='. $this->service->progress['project_text']['project_id'];
        }
        $html1 = <<<ht
    <p> Hi {$this->service->name}， </p>
    <p>{$str} 有新的跟进信息:</p>
    <br/>
    <div>{$this->service->progress['description']}</div>
    <br/>
    <br/>
    <p>点击查看详情 <a href="{$url}">http://bi.t-firefly.com:2101/</a></p>
    <p>以上信息由系统发出，如有疑问请联系管理员。</p>
    
ht;
        return $this
            ->subject($subject)
            ->htmlBody($html1);

    }
}