<?php

namespace App\Mail\Redmine;

use App\Core\Services\Product\ProductProgressService;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProductProgressModel;
use HyperfExt\Mail\Mailable;

class ProductEditMail extends Mailable
{

    public $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * 构造消息。
     *
     * @return $this
     */
    public function build()
    {
        $url   = biFrontendHost().'/project/productDetailsIndex?product_id='.$this->service->product_id;
        $product = ProductModel::query()->find($this->service->product_id);
        $html1 = <<<ht
<p> Hi, {$this->service->name} </p>
<p>{$this->service->product_name} 上线信息更新了，请及时<a href="{$url}">查看</a></p>
<p>修改内容如下</p>
ht;
        if (!empty($this->service->product_progress_id)) {
            $service = make(ProductProgressService::class);
            $row = $service->getOverView($this->service->product_progress_id);
            if (!empty($row['details'])) {
                $html1 .= '<table style="width: 100%;border-top: 1px solid #999;border-left: 1px solid #999;border-spacing: 0;"><tr><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">修改属性</td><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">原来的值</td><td style="border-bottom: 1px solid #999;border-right: 1px solid #999;">最新的值</td></tr>';
                foreach ($row['details'] as $detail) {
                    switch ($detail['property']) {
                        case 'json' :
                            if (!empty($detail['diff_value_text'][0]) && is_array($detail['diff_value_text'][0])) {
                                foreach ($detail['diff_value_text'][0] as $dkey => &$dval) {
                                    if ($dval != $detail['diff_value_text'][1][$dkey]) {
                                        $html1 .= "<tr><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$dval['name']}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'> <a href='{$dval['url']}' style='color:{$dval['ext']['color']}'>{$dval['text']}</a> </td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'> <a href='{$detail['diff_value_text'][1][$dkey]['url']}' style='color:{$detail['diff_value_text'][1][$dkey]['ext']['color']}'>{$detail['diff_value_text'][1][$dkey]['text']}</a> </td></tr>";
                                    }
                                }
                            }
                            break;
                        default:
                            // $valText = $detail['value_text'];
                            // if (strpos($detail['prop_key'], '_link') > 0) {
                            //     $valText = "<a href='{$detail['value_text']}'>{$detail['value_text']}</a>";
                            // }
                            $html1 .= "<tr><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['prop_name']}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['diff_value_text'][0]}</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>{$detail['diff_value_text'][1]}</td></tr>";
                    }


                    // 如果是带属性地址的属性需要添加上
                    // $propKey = $detail['prop_key'] . '_link';
                    // if (!empty($product->$propKey)) {
                    //     $link = strpos($product->$propKey, 'http') === false ? 'http://'.$product->$propKey : $product->$propKey;
                    //     $html1 .= "<tr><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;'>相关地址</td><td style='border-bottom: 1px solid #999;border-right: 1px solid #999;' colspan='2'><a href='{$link}'>{$product->$propKey}</a></td></tr>";
                    // }
                }
                $html1 .= '</table>';
            }
        }
        return $this
            ->subject('数字天启产品变更通知')
            ->htmlBody($html1);

    }
}