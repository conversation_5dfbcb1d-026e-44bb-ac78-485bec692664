<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 下午2:44
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services;

use App\Constants\StatusCode;
use App\Exception\AppException;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;

/**
 * 业务基础服务类
 */
abstract class BusinessService extends BaseService
{
    /**
     * @var Model
     */
    protected $model;

    /* 是否关联 */
    public $isRelation = false;

    public function getOverView($id)
    {
        return $this->model::query()->find($id);
    }

    public function detail(array $filter = [], array $op = [])
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, '', '', '');
        return $query->first();
    }

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->orderBy($sort, $order)->paginate($limit);
    }

    /**
     * 获取所有数据列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return mixed
     */
    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        return $query->orderBy($sort, $order)->get();
    }

    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $result = $row->update($values);
        } else {
            $result = $this->model::query()->create($values);
        }

        return $result;
    }

    /**
     * 删除
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {
        $ids = explode(',', $ids);
        return $this->model::destroy($ids);
    }

    /**
     * 批量更新
     * @param $ids
     * @param $params
     * @return bool
     */
    public function doMulti($ids, $params): bool
    {
        if ($ids) {
            parse_str($params, $values);
            if (!$values) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
            }

            $count = 0;
            Db::beginTransaction();
            try {
                $items = $this->model::query()->whereIn('id', explode(',', $ids))->get();

                foreach ($items as $item) {
                    $count += $item->update($values);
                }
                Db::commit();
            } catch (\Throwable $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
            if ($count > 0) {
                return true;
            } else {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
            }
        }
        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
    }

    /**
     * 构建查询所需条件、排序方式
     *
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @param $query
     * @return array
     */
    public function buildparams($filter, $op, $sort, $order, $limit, $query = null): array
    {

        if (!$query) {
            $query = $this->model::query();
        }

        if ($filter) {
            foreach ($filter as $k => $v) {
                if (is_array($v) && ($op[$k] ?? '') != 'JSONCONTAINS') {
                    $query = $query->whereHas($k, function ($query) use ($v, $op, $k) {
                        foreach ($v as $whereHasK => $whereHasV) {
                            if (isset($whereHasV) && !empty($whereHasV) && !empty($op[$k][$whereHasK])) {
                                // $sym = $op[$k][$whereHasK] ?? '=';
                                $query = $this->buildWhere($query, $whereHasK, $op[$k][$whereHasK], $whereHasV);
                            }
                        }
                    });
                } else {
                    if (isset($v)) {
                        if (($v === '' || $v === 'null') && !isset($op[$k])) {
                            continue;
                        }

                        // if(empty($v) && empty($op[$k])){
                        //     continue;
                        // }
                        $sym = $op[$k] ?? '=';
                        // 加入关联查询join表名前缀
                        if ($this->isRelation) {
                            $check = explode('.', $k);
                            if (count($check) == 1) {
                                $table = $this->model->getTable();
                                $k = $table.'.'.$k;
                            }
                        }
                        $query = $this->buildWhere($query, $k, $sym, $v);
                    }
                }
            }
        }

        return [$query, $limit, $sort, $order];
    }

    /**
     * 构造查询语句
     * @var $query Model
     * @param $query
     * @param $k
     * @param $sym
     * @param $v
     * @return mixed
     */
    public function buildWhere($query, $k, $sym, $v)
    {
        $v = strtoupper($v);
        switch ($v) {
            case 'IS NULL':
                $sym = 'IS NULL';
                break;
            case 'NOT EXISTS':
                $sym = 'NOT EXISTS';
                break;
            default:
        }
        $sym = strtoupper($sym);
        switch ($sym) {
            case '=':
            case '>':
            case '<':
            case '>=':
            case '<=':
            case '<>':
                $query = $query->where($k, $sym, $v);
                break;
            case 'OR':
                $query = $query->orWhere($k, $v);
                break;
            case 'OR LIKE':
            case 'OR NOT LIKE':
            case 'OR LIKE %...%':
            case 'OR NOT LIKE %...%':
                if (!empty($v)) {
                    $sym = str_replace('OR ', '', $sym);
                    $query = $query->orWhere($k, $sym, "%{$v}%");
                }
                break;
            case 'LIKE':
            case 'NOT LIKE':
            case 'LIKE %...%':
            case 'NOT LIKE %...%':
                if (!empty($v)) {
                    $query = $query->where($k, $sym, "%{$v}%");
                }
                break;
            case 'BETWEEN':
            case 'NOT BETWEEN':
                $v = array_slice(explode('-', $v), 0, 2);
                if ($k == 'dated_at' || $k == 'updated_at' || $k == 'created_at') {
                    foreach ($v as &$item) {
                        $item = date('Y-m-d', $item);
                    }
                }
                $query = $query->whereBetween($k, $v);
                break;
            case 'DATE':
                //日期条件
                if (strpos($v, '-') === 0) {
                    $v = array_slice(explode('- ', $v), 0, 2);
                } else if(strpos($v, '-') === strlen($v) - 1) {
                    $v = array_slice(explode(' -', $v), 0, 2);
                } else {
                    $v = array_slice(explode(' - ', $v), 0, 2);
                }
                if (count($v) == 1 || $v[1] == '') {
                    $query = $query->where($k, '>=', $v[0]);
                } else if(count($v) ==  2 && $v[0] == '') {
                    $query = $query->where($k, '<=', $v[1]);
                } else {
                    $query = $query->whereBetween($k, $v);
                }
                break;
            case 'DATETIME':
                // 日期带时间条件
                if (strpos($v, '-') === 0) {
                    $v = array_slice(explode('- ', $v), 0, 2);
                } else if(strpos($v, '-') === strlen($v) - 1) {
                    $v = array_slice(explode(' -', $v), 0, 2);
                } else {
                    $v = array_slice(explode(' - ', $v), 0, 2);
                }

                foreach ($v as &$item) {
                    if (is_numeric($item)) {
                        $item = date('Y-m-d H:i:s', $item);
                    }
                }
                if (strpos($v[0], ':') === false) {
                    $v[0] = date('Y-m-d 00:00:00', strtotime($v[0]));
                }
                if (!empty($v[1]) && strpos($v[1], ':') === false) {
                    $v[1] = date('Y-m-d 23:59:59', strtotime($v[1]));
                }
                if (count($v) == 1 || $v[1] == '') {
                    $query = $query->where($k, '>=', $v[0]);
                } else if(count($v) ==  2 && $v[0] == '') {
                    $query = $query->where($k, '<=', $v[1]);
                } else {
                    $query = $query->whereBetween($k, $v);
                }
                break;
            case 'IN':
                if (!empty($v)) {
                    $query = $query->whereIn($k, explode(',', $v));
                }
                break;
            case 'NOT IN':
                if (!empty($v)) {
                    $query = $query->whereNotIn($k, explode(',', $v));
                }
                break;
            case 'IS NULL':
                $query = $query->whereNull($k);
                break;
            case 'IS NOT NULL':
                $query = $query->whereNotNull($k);
                break;
            case 'NOT EXISTS':
                $query = $query->whereDoesntHave($k);
                break;
            case 'JSONCONTAINS':
                if(!empty($v)){
                    if (is_numeric($v)) {
                        $v = (int) $v;
                    }

                    $query = $query->whereJsonContains($k,$v);
                }
                break;
            case 'JSONCONTAINS ARR':
                // JSONCONTAINS 或者条件，json数组中或者条件
                if(!empty($v)){
                    $v = explode(',', $v);
                    if ($v) {
                        $query->where(function ($query) use ($k, $v) {
                            foreach ($v as $condition) {
                                $query = $query->orWhereJsonContains($k,(int)$condition);
                            }
                        });
                    }
                }
                break;
            case 'RAW':
                $query = $query->whereRaw($v);
                break;
            default:
                $query = $query->where($k, $v);
                break;
        }
        return $query;
    }
}