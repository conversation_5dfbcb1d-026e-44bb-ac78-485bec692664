<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/05/06
     * <AUTHOR>
     * @Description 文档导出服务
     */

    namespace App\Core\Services\TchipWiki;

    use App\Constants\StatusCode;
    use App\Exception\AppException;
    use App\Model\TchipBi\WikiDocumentModel;
    use App\Model\TchipBi\WikiDocumentStatisticModel;
    use Carbon\Carbon;
    use Exception;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;
    use ZipStream\ZipStream;

    class WikiExportService
    {
        /**
         * @Inject
         * @var WikiDocumentModel
         */
        protected WikiDocumentModel $wikiDocumentModel;

        /**
         * @Inject
         * @var WikiDocumentStatisticModel
         */
        protected WikiDocumentStatisticModel $wikiDocumentStatisticModel;

        /**
         * 导出文档为PDF
         * 
         * @param int $doc_id 文档ID
         * @param int $userId 当前用户ID
         * @param string $userName 当前用户名称
         * @param array $accessibleSpaceIds 用户可访问的空间ID列表
         * @return array 包含文件路径和文件名的数组
         * @throws AppException
         */
        public function exportToPdf(int $doc_id, int $userId, string $userName, array $accessibleSpaceIds = []): array
        {
            // 获取文档信息
            $document = $this->wikiDocumentModel::query()
                ->with(['creator', 'updater'])
                ->find($doc_id);
            
            if (!$document) {
                throw new AppException(StatusCode::ERR_SERVER, '文档不存在');
            }
            
            // 检查权限
            if (!empty($accessibleSpaceIds) && !in_array($document->space_id, $accessibleSpaceIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '您没有权限导出此文档');
            }

            // 文档内容处理 - 确保UTF-8编码
            $content = $document->content_html;
            if (!mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', mb_detect_encoding($content));
            }
            
            $title = $document->title;
            if (!mb_check_encoding($title, 'UTF-8')) {
                $title = mb_convert_encoding($title, 'UTF-8', mb_detect_encoding($title));
            }
            
            // 获取作者和最后更新者信息
            $authorName = $document->creator->name ?? '未知作者';
            $updaterName = $document->updater->name ?? '未知更新者';
            $createTime = $document->created_at;
            $updateTime = $document->updated_at;
            
            try {
                // 实例化mPDF并设置配置
                $mpdf = $this->initializeMpdf();
                
                // 设置文档信息
                $mpdf->SetTitle($title);
                $mpdf->SetAuthor($authorName);
                $mpdf->SetCreator('天启智能科技');
                
                // 设置水印
                $mpdf->SetWatermarkText('天启智能科技');
                $mpdf->showWatermarkText = true;
                $mpdf->watermarkTextAlpha = 0.1;
                
                //// 构建封面页(可选)
                //$coverPage = $this->buildCoverPage($title, $authorName, $createTime);
                //$mpdf->WriteHTML($coverPage);
                //$mpdf->AddPage();
                
                // 构建PDF内容模板
                $htmlTemplate = $this->buildPdfTemplate(
                    $title,
                    $content,
                    $authorName,
                    $updaterName,
                    $createTime,
                    $updateTime,
                    $userName
                );

                // 写入HTML内容
                $mpdf->WriteHTML($htmlTemplate);
                
                // 生成并保存PDF文件
                $result = $this->savePdfFile($mpdf, $doc_id, $title);
                
                // 记录导出操作
                $this->logExportAction($doc_id, $userId);
                
                return $result;
                
            } catch (Exception $e) {
                throw new AppException(StatusCode::ERR_SERVER, '导出PDF失败: ' . $e->getMessage());
            }
        }

        /**
         * 初始化mPDF实例
         * 
         * @return \Mpdf\Mpdf
         * @throws AppException
         */
        private function initializeMpdf(): \Mpdf\Mpdf
        {
            // 定义字体目录
            $fontDir = BASE_PATH . '/public/fonts/';
            
            // 验证必要的字体文件
            $requiredFonts = [
                'HarmonyOS_Sans_SC_Regular.ttf',
                'HarmonyOS_Sans_SC_Bold.ttf',
                'HarmonyOS_Sans_SC_Light.ttf',
                'HarmonyOS_Sans_SC_Medium.ttf',
                'HarmonyOS_Sans_SC_Thin.ttf',
                'HarmonyOS_Sans_SC_Black.ttf'
            ];
            
            foreach ($requiredFonts as $font) {
                if (!file_exists($fontDir . $font)) {
                    throw new AppException(StatusCode::ERR_SERVER, "字体文件不存在: {$font}");
                }
            }
            
            // 使用mPDF库生成PDF，配置完整的HarmonyOS Sans SC字体系列
            return new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9,
                'tempDir' => sys_get_temp_dir(),
                // 指定HarmonyOS Sans SC Regular为默认字体
                'default_font' => 'harmonyossanssc',
                // 指定字体目录
                'fontDir' => [
                    $fontDir,
                    sys_get_temp_dir(),
                ],
                'fontdata' => [
                    // 配置常规字体作为主要字体
                    'harmonyossanssc' => [
                        'R' => 'HarmonyOS_Sans_SC_Regular.ttf',
                        'B' => 'HarmonyOS_Sans_SC_Bold.ttf',
                        'I' => 'HarmonyOS_Sans_SC_Light.ttf',
                        'BI' => 'HarmonyOS_Sans_SC_Medium.ttf',
                    ],
                    // 配置其他字重作为单独字体，以便在CSS中使用
                    'harmonyossansthin' => [
                        'R' => 'HarmonyOS_Sans_SC_Thin.ttf',
                    ],
                    'harmonyossanslight' => [
                        'R' => 'HarmonyOS_Sans_SC_Light.ttf',
                    ],
                    'harmonyossansmedium' => [
                        'R' => 'HarmonyOS_Sans_SC_Medium.ttf',
                    ],
                    'harmonyossansbold' => [
                        'R' => 'HarmonyOS_Sans_SC_Bold.ttf',
                    ],
                    'harmonyossansblack' => [
                        'R' => 'HarmonyOS_Sans_SC_Black.ttf',
                    ],
                ],
                'autoLangToFont' => true,
                'autoScriptToLang' => true,
            ]);
        }

        /**
         * 构建封面页
         */
        private function buildCoverPage(string $title, string $authorName, string $createTime): string
        {
            return <<<HTML
<div style="text-align: center; padding-top: 40%;">
    <h1 style="font-family: harmonyossansblack; font-size: 32px; color: #333; margin-bottom: 50px;">
        {$title}
    </h1>
    <p style="font-family: harmonyossanslight; font-size: 16px; color: #666; margin-bottom: 10px;">
        作者: {$authorName}
    </p>
    <p style="font-family: harmonyossanslight; font-size: 16px; color: #666;">
        创建时间: {$createTime}
    </p>
</div>
HTML;
        }

        /**
         * 构建PDF内容模板
         */
        private function buildPdfTemplate(
            string $title,
            string $content,
            string $authorName,
            string $updaterName,
            string $createTime,
            string $updateTime,
            string $currentUserName
        ): string {
            // 获取当前日期时间
            $exportDateTime = date('Y-m-d H:i:s');
            
            // 构建HTML模板 - 利用多种字重的HarmonyOS Sans SC字体
            return <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>{$title}</title>
    <style>
        /* 基本样式 */
        body {
            font-family: harmonyossanssc, sans-serif;
            margin: 30px;
            position: relative;
            line-height: 1.6;
        }
        
        /* 页眉样式 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
        }
        
        /* 使用Black字重的标题 */
        .title {
            font-family: harmonyossansblack, sans-serif;
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }
        
        /* 使用Light字重的元数据 */
        .meta {
            font-family: harmonyossanslight, sans-serif;
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        /* 内容区域样式 */
        .content {
            font-size: 14px;
            line-height: 1.6;
        }
        
        /* 页脚使用Thin字重 */
        .footer {
            position: fixed;
            bottom: 10px;
            right: 10px;
            font-family: harmonyossansthin, sans-serif;
            font-size: 9px;
            color: #888;
            text-align: right;
        }
        
        /* 标题样式优化 */
        .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
            padding-top: 0px;
            margin: 16px 0 0px;
            margin-block-start: 9px;
            margin-block-end: 18px;
            line-height: 1.1;
        }
        
        .content h1 {
            font-family: harmonyossansbold, sans-serif;
            font-size: 2em;
            color: #333;
        }
        
        .content h2 {
            font-family: harmonyossansmedium, sans-serif;
            font-size: 1.5em;
            color: #444;
        }
        
        .content h3 {
            font-family: harmonyossansmedium, sans-serif;
            font-size: 1.17em;
            color: #555;
        }
        
        .content h4 {
            font-size: 1em;
        }
        
        .content h5 {
            font-size: 0.83em;
        }
        
        .content h6 {
            font-size: 0.67em;
        }
        
        /* 段落样式 */
        .content p {
            margin-bottom: 12px;
            text-align: justify;
        }
        
        /* 列表样式 */
        .content ul, .content ol {
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
        }
        
        .content ul {
            padding: 0 1.22rem;
        }
        
        .content ol {
            padding: 0 1.12rem;
        }
        
        /* 表格样式 */
        .content table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .content table, .content th, .content td {
            border: 1px solid #ddd;
        }
        
        .content th {
            font-family: harmonyossansmedium, sans-serif;
            background-color: #f2f2f2;
            padding: 8px;
            text-align: left;
        }
        
        .content td {
            padding: 8px;
        }
        
        /* 代码块样式 */
        .content pre, .content code {
            font-family: Consolas, Monaco, monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        /* 链接样式 */
        .content a {
            color: #0066cc;
            text-decoration: none;
        }
        
        /* 引用样式 */
        .content blockquote {
            margin-left: 0;
            padding-left: 15px;
            border-left: 3px solid #ddd;
            color: #666;
            font-family: harmonyossanslight, sans-serif;
        }
        
        /* 分割线样式 */
        .content hr {
            margin: 2rem 0;
            border: none;
            border-top: 2px solid #ced4da;
        }
        
        /* 表格包装器样式 */
        .content .tableWrapper {
            padding: 0px;
        }
        
        @page {
            margin: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">{$title}</div>
        <div class="meta">作者: {$authorName} | 创建时间: {$createTime}</div>
        <div class="meta">更新者: {$updaterName} | 更新时间: {$updateTime}</div>
    </div>
    
    <div class="content">
        {$content}
    </div>
    
    <div class="footer">
        <div>文档导出: {$currentUserName}</div>
        <div>导出时间: {$exportDateTime}</div>
    </div>
</body>
</html>
HTML;
        }

        /**
         * 保存PDF文件
         * 
         * @param \Mpdf\Mpdf $mpdf mPDF实例
         * @param int $doc_id 文档ID
         * @param string $title 文档标题
         * @return array 包含文件路径和文件名的数组
         */
        private function savePdfFile(\Mpdf\Mpdf $mpdf, int $doc_id, string $title): array
        {
            // 生成文件名
            $fileName = 'wiki_' . $doc_id . '_' . date('YmdHis') . '.pdf';
            $filePath = BASE_PATH . '/runtime/temp/export/wiki_export_pdf/' . $fileName;
            
            // 确保目录存在
            $directory = dirname($filePath);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            // 保存PDF文件
            $mpdf->Output($filePath, \Mpdf\Output\Destination::FILE);
            
            // 返回文件下载链接或路径
            return [
                'file_path' => $filePath,
                'file_name' => $title . '.pdf',
            ];
        }

        /**
         * 记录文档导出操作日志
         * 
         * @param int $doc_id 文档ID
         * @param int $user_id 操作用户ID
         * @return void
         */
        private function logExportAction(int $doc_id, int $user_id): void
        {
            // 获取当前日期
            $today = Carbon::now()->toDateString();
            
            // 更新文档全局统计信息
            $this->wikiDocumentStatisticModel::query()
                ->updateOrCreate(
                    ['doc_id' => $doc_id],
                    [
                        'export_count' => DB::raw('export_count + 1'),
                        'last_export_at' => Carbon::now(),
                        'last_export_by' => $user_id,
                        'updated_at' => Carbon::now()
                    ]
                );
            
            // 更新或创建当日的统计记录
            $dailyStatistic = $this->wikiDocumentStatisticModel::query()
                ->where('doc_id', $doc_id)
                ->where('date', $today)
                ->first();
            
            if ($dailyStatistic) {
                // 如果当天已有记录，更新导出次数
                $dailyStatistic->increment('export_count');
                $dailyStatistic->updated_at = Carbon::now();
                $dailyStatistic->save();
            } else {
                // 如果当天没有记录，创建新记录
                $this->wikiDocumentStatisticModel::create([
                    'doc_id' => $doc_id,
                    'date' => $today,
                    'view_count' => 0,
                    'like_count' => 0,
                    'comment_count' => 0,
                    'export_count' => 1,
                    'last_export_at' => Carbon::now(),
                    'last_export_by' => $user_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            }
        }
        

        /**
         * 根据目录结构导出文档为ZIP
         * 
         * @param array $docIds 所有文档ID
         * @param int $userId 当前用户ID
         * @param string $userName 当前用户名称
         * @param array $accessibleSpaceIds 用户可访问的空间ID列表
         * @param array $catalogStructure 目录结构信息
         * @param bool $preserveStructure 是否在ZIP中保留目录结构
         * @param string $zipName ZIP文件名
         * @return array 包含导出信息的数组
         * @throws AppException
         */
        public function exportToPdfWithStructure(
            array $docIds, 
            int $currentUserId,
            string $currentUserName,
            array $accessibleSpaceIds = [],
            array $catalogStructure = [],
            bool $preserveStructure = true,
            string $zipName = '文档导出'
        ): array
        {
            if (empty($docIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '没有可导出的文档');
            }
            
            if (count($docIds) > 100) {
                throw new AppException(StatusCode::ERR_SERVER, '当前服务器限制一次最多只能导出100个文档');
            }
            
            // 创建临时目录
            $tempDir = $this->getTempDir();
            $zipDir = $tempDir . '/zip_content';
            if (!is_dir($zipDir)) {
                mkdir($zipDir, 0755, true);
            }
            
            // 获取所有文档
            $query = $this->wikiDocumentModel::query()->whereIn('doc_id', $docIds);
            
            // 检查权限限制
            if (!empty($accessibleSpaceIds)) {
                $query->whereIn('space_id', $accessibleSpaceIds);
            }
            
            $documents = $query->get();
            
            // 检查是否有权限导出
            if ($documents->count() < count($docIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '部分文档无权访问');
            }
            
            // 创建文档ID到目录路径的映射
            $docPathMapping = [];
            if ($preserveStructure && !empty($catalogStructure)) {
                // 构建目录树
                $catalogTree = $this->buildCatalogTree($catalogStructure);
                
                // 创建目录结构
                $this->createDirectoryStructure($zipDir, $catalogTree);
                
                // 为每个文档创建映射到ZIP内的路径
                foreach ($documents as $document) {
                    $catalogId = $document->catalog_id;
                    $catalogPath = $this->getCatalogPathInZip($catalogId, $catalogStructure);
                    
                    // 确保目录存在
                    $fullDirPath = $zipDir . '/' . $catalogPath;
                    if (!is_dir($fullDirPath)) {
                        mkdir($fullDirPath, 0755, true);
                    }
                    
                    // 记录文档路径映射
                    $safeName = $this->getSafeFileName($document->title, $document->doc_id);
                    $docPathMapping[$document->doc_id] = $catalogPath . '/' . $safeName . '.pdf';
                }
            }
            
            // 生成PDF文件
            foreach ($documents as $document) {
                try {
                    $docId = $document->doc_id;
                    $html = $this->processContentForExport($document, $currentUserName);
                    
                    // 确定PDF保存路径
                    if ($preserveStructure && isset($docPathMapping[$docId])) {
                        $pdfPath = $zipDir . '/' . $docPathMapping[$docId];
                    } else {
                        $safeName = $this->getSafeFileName($document->title, $docId);
                        $pdfPath = $zipDir . '/' . $safeName . '.pdf';
                    }
                    
                    // 生成PDF
                    $this->generatePdfFromHtml($html, $pdfPath);
                    
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他文档
                    error_log("导出文档 ID: {$docId} 失败: " . $e->getMessage());
                }
            }
            
            // 创建ZIP文件
            $zipFileName = $zipName . '_' . date('YmdHis') . '.zip';
            $zipPath = $tempDir . '/' . $zipFileName;
            
            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
                $this->addDirToZip($zip, $zipDir, '');
                $zip->close();
            } else {
                throw new AppException(StatusCode::ERR_SERVER, 'ZIP文件创建失败');
            }
            
            // 返回ZIP文件信息
            return [
                'file_path' => $zipPath,
                'file_name' => $zipFileName,
                'document_count' => $documents->count()
            ];
        }

        /**
         * 构建目录树结构
         */
        private function buildCatalogTree(array $catalogStructure): array
        {
            $tree = [];
            $tempTree = [];
            
            // 首先构建临时结构
            foreach ($catalogStructure as $id => $catalog) {
                $tempTree[$id] = [
                    'id' => $id,
                    'name' => $catalog['name'],
                    'pid' => $catalog['pid'],
                    'children' => [],
                    'documents' => $catalog['documents'] ?? []
                ];
            }
            
            // 构建树结构
            foreach ($tempTree as $id => $item) {
                $pid = $item['pid'];
                if (isset($tempTree[$pid])) {
                    $tempTree[$pid]['children'][$id] = &$tempTree[$id];
                } else {
                    $tree[$id] = &$tempTree[$id];
                }
            }
            
            return $tree;
        }

        /**
         * 递归创建目录结构
         */
        private function createDirectoryStructure(string $baseDir, array $tree, string $currentPath = ''): void
        {
            foreach ($tree as $catalog) {
                $dirPath = $currentPath . '/' . $this->getSafeDirName($catalog['name'], $catalog['id']);
                $fullPath = $baseDir . $dirPath;
                
                if (!is_dir($fullPath)) {
                    mkdir($fullPath, 0755, true);
                }
                
                if (!empty($catalog['children'])) {
                    $this->createDirectoryStructure($baseDir, $catalog['children'], $dirPath);
                }
            }
        }

        /**
         * 获取目录在ZIP中的相对路径
         */
        private function getCatalogPathInZip(int $catalogId, array $catalogStructure): string
        {
            if (!isset($catalogStructure[$catalogId])) {
                return '';
            }
            
            $paths = [];
            $currentId = $catalogId;
            
            while ($currentId && isset($catalogStructure[$currentId])) {
                $catalog = $catalogStructure[$currentId];
                $paths[] = $this->getSafeDirName($catalog['name'], $currentId);
                $currentId = $catalog['pid'];
                
                // 防止循环引用
                if (in_array($currentId, array_keys($paths))) {
                    break;
                }
            }
            
            // 反转路径以获得正确的顺序
            $paths = array_reverse($paths);
            return implode('/', $paths);
        }

        /**
         * 获取安全的目录名
         */
        private function getSafeDirName(string $name, int $id): string
        {
            // 移除不安全的字符
            $name = preg_replace('/[<>:"\\\|?*]/', '_', $name);
            return $name;
        }

        /**
         * 获取安全的文件名
         */
        private function getSafeFileName(string $name, int $id): string
        {
            // 移除不安全的字符
            $name = preg_replace('/[<>:"\\\|?*]/', '_', $name);
            return $name;
        }

        /**
         * 递归添加目录到ZIP
         */
        private function addDirToZip(\ZipArchive $zip, string $dir, string $zipDir): void
        {
            $handle = opendir($dir);
            while (false !== ($entry = readdir($handle))) {
                if ($entry == '.' || $entry == '..') {
                    continue;
                }
                
                $path = $dir . '/' . $entry;
                $zipPath = $zipDir ? $zipDir . '/' . $entry : $entry;
                
                if (is_dir($path)) {
                    $zip->addEmptyDir($zipPath);
                    $this->addDirToZip($zip, $path, $zipPath);
                } else {
                    $zip->addFile($path, $zipPath);
                }
            }
            closedir($handle);
        }
        
        /**
         * 获取临时目录路径
         * 
         * @return string 临时目录的完整路径
         */
        private function getTempDir(): string
        {
            $tempDir = BASE_PATH . '/runtime/temp/export/' . uniqid('wiki_export_');
            
            // 确保目录存在
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            
            return $tempDir;
        }
        
        /**
         * 处理文档内容以准备导出
         * 
         * @param WikiDocumentModel $document 文档模型
         * @param string $currentUserName 当前用户名称
         * @return string 处理后的HTML内容
         */
        private function processContentForExport(WikiDocumentModel $document, string $currentUserName = '数字天启'): string
        {
            // 获取文档信息
            $title = $document->title;
            $content = $document->content_html ?? ''; // 添加空字符串作为默认值
            $authorName = $document->creator->name ?? '未知作者';
            $updaterName = $document->updater->name ?? '未知更新者';
            $createTime = $document->created_at;
            $updateTime = $document->updated_at;
            
            // 确保内容是UTF-8编码
            if (!empty($content) && !mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', mb_detect_encoding($content));
            }
            
            if (!mb_check_encoding($title, 'UTF-8')) {
                $title = mb_convert_encoding($title, 'UTF-8', mb_detect_encoding($title));
            }
            
            // 构建HTML模板
            return $this->buildPdfTemplate(
                $title,
                $content, // 现在这里始终会传递字符串，即使是空字符串
                $authorName,
                $updaterName,
                $createTime,
                $updateTime,
                $currentUserName
            );
        }

        /**
         * 生成PDF文件，并处理内容为空的情况
         * 
         * @param string $html HTML内容
         * @param string $outputPath 输出PDF的路径
         * @return bool 是否成功生成PDF
         * @throws \Exception 生成过程中出现的异常
         */
        private function generatePdfFromHtml(string $html, string $outputPath): bool
        {
            try {
                // 确保输出目录存在
                $directory = dirname($outputPath);
                if (!is_dir($directory)) {
                    mkdir($directory, 0755, true);
                }
                
                // 实例化mPDF
                $mpdf = $this->initializeMpdf();
                
                // 设置文档信息
                $mpdf->SetTitle(basename($outputPath, '.pdf'));
                $mpdf->SetCreator('天启智能科技');
                
                // 设置水印
                $mpdf->SetWatermarkText('天启智能科技');
                $mpdf->showWatermarkText = true;
                $mpdf->watermarkTextAlpha = 0.1;
                
                // 如果内容为空，添加一个提示信息
                if (trim($html) === '') {
                    $html = '<div style="text-align:center; padding:50px; color:#888;">
                        <h2>此文档内容为空</h2>
                        <p>可能文档尚未添加内容或内容已被删除</p>
                    </div>';
                }
                
                // 写入HTML内容
                $mpdf->WriteHTML($html);
                
                // 输出到文件，使用相同的目标类型常量
                $mpdf->Output($outputPath, \Mpdf\Output\Destination::FILE);
                
                return file_exists($outputPath);
            } catch (\Exception $e) {
                throw new \Exception('生成PDF失败: ' . $e->getMessage(), 0, $e);
            }
        }
        
        /**
         * 导出文档为PDF并直接返回文件流
         * 
         * @param int $doc_id 文档ID
         * @param int $userId 当前用户ID
         * @param string $userName 当前用户名称
         * @param array $accessibleSpaceIds 用户可访问的空间ID列表
         * @return array 包含文件流和文件名的数组
         * @throws AppException
         */
        public function exportToPdfStream(int $doc_id, int $userId, string $userName, array $accessibleSpaceIds = []): array
        {
            // 获取文档信息
            $document = $this->wikiDocumentModel::query()
                ->with(['creator', 'updater'])
                ->find($doc_id);
            
            if (!$document) {
                throw new AppException(StatusCode::ERR_SERVER, '文档不存在');
            }
            
            // 检查权限
            if (!empty($accessibleSpaceIds) && !in_array($document->space_id, $accessibleSpaceIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '您没有权限导出此文档');
            }

            try {
                // 处理文档内容
                $html = $this->processContentForExport($document, $userName);
                
                // 实例化mPDF
                $mpdf = $this->initializeMpdf();
                
                // 设置文档信息
                $mpdf->SetTitle($document->title);
                $mpdf->SetAuthor($document->creator->name ?? '未知作者');
                $mpdf->SetCreator('天启智能科技');
                
                // 设置水印
                $mpdf->SetWatermarkText('天启智能科技');
                $mpdf->showWatermarkText = true;
                $mpdf->watermarkTextAlpha = 0.1;
                
                // 如果内容为空，添加一个提示信息
                if (trim($html) === '') {
                    $html = '<div style="text-align:center; padding:50px; color:#888;">
                        <h2>此文档内容为空</h2>
                        <p>可能文档尚未添加内容或内容已被删除</p>
                    </div>';
                }
                
                // 写入HTML内容
                $mpdf->WriteHTML($html);
                
                // 直接获取PDF流
                $pdfContent = $mpdf->Output('', \Mpdf\Output\Destination::STRING_RETURN);
                
                // 记录导出操作
                $this->logExportAction($doc_id, $userId);
                
                return [
                    'file_content' => $pdfContent,
                    'file_name' => $document->title . '.pdf',
                    'mime_type' => 'application/pdf'
                ];
                
            } catch (Exception $e) {
                throw new AppException(StatusCode::ERR_SERVER, '导出PDF失败: ' . $e->getMessage());
            }
        }

        /**
         * 根据目录结构导出文档为ZIP并返回文件流
         * 
         * @param array $docIds 所有文档ID
         * @param int $userId 当前用户ID
         * @param string $userName 当前用户名称
         * @param array $accessibleSpaceIds 用户可访问的空间ID列表
         * @param array $catalogStructure 目录结构信息
         * @param bool $preserveStructure 是否在ZIP中保留目录结构
         * @param string $zipName ZIP文件名
         * @return array 包含文件流和文件名的数组
         * @throws AppException
         */
        public function exportToPdfWithStructureStream(
            array $docIds, 
            int $currentUserId,
            string $currentUserName,
            array $accessibleSpaceIds = [],
            array $catalogStructure = [],
            bool $preserveStructure = true,
            string $zipName = '文档导出'
        ): array
        {
            if (empty($docIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '没有可导出的文档');
            }
            
            if (count($docIds) > 100) {
                throw new AppException(StatusCode::ERR_SERVER, '当前服务器限制一次最多只能导出100个文档');
            }
            
            // 创建临时目录
            $tempDir = $this->getTempDir();
            $zipDir = $tempDir . '/zip_content';
            if (!is_dir($zipDir)) {
                mkdir($zipDir, 0755, true);
            }
            
            // 获取所有文档
            $query = $this->wikiDocumentModel::query()->whereIn('doc_id', $docIds);
            
            // 检查权限限制
            if (!empty($accessibleSpaceIds)) {
                $query->whereIn('space_id', $accessibleSpaceIds);
            }
            
            $documents = $query->get();
            
            // 检查是否有权限导出
            if ($documents->count() < count($docIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '部分文档无权访问');
            }
            
            // 创建文档ID到目录路径的映射
            $docPathMapping = [];
            if ($preserveStructure && !empty($catalogStructure)) {
                // 构建目录树
                $catalogTree = $this->buildCatalogTree($catalogStructure);
                
                // 创建目录结构
                $this->createDirectoryStructure($zipDir, $catalogTree);
                
                // 为每个文档创建映射到ZIP内的路径
                foreach ($documents as $document) {
                    $catalogId = $document->catalog_id;
                    $catalogPath = $this->getCatalogPathInZip($catalogId, $catalogStructure);
                    
                    // 确保目录存在
                    $fullDirPath = $zipDir . '/' . $catalogPath;
                    if (!is_dir($fullDirPath)) {
                        mkdir($fullDirPath, 0755, true);
                    }
                    
                    // 记录文档路径映射
                    $safeName = $this->getSafeFileName($document->title, $document->doc_id);
                    $docPathMapping[$document->doc_id] = $catalogPath . '/' . $safeName . '.pdf';
                }
            }
            
            // 生成PDF文件
            foreach ($documents as $document) {
                try {
                    $docId = $document->doc_id;
                    $html = $this->processContentForExport($document, $currentUserName);
                    
                    // 确定PDF保存路径
                    if ($preserveStructure && isset($docPathMapping[$docId])) {
                        $pdfPath = $zipDir . '/' . $docPathMapping[$docId];
                    } else {
                        $safeName = $this->getSafeFileName($document->title, $docId);
                        $pdfPath = $zipDir . '/' . $safeName . '.pdf';
                    }
                    
                    // 生成PDF
                    $this->generatePdfFromHtml($html, $pdfPath);
                    
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他文档
                    error_log("导出文档 ID: {$docId} 失败: " . $e->getMessage());
                }
            }
            
            // 使用普通ZipArchive创建ZIP文件
            $zipFileName = $zipName . '_' . date('YmdHis') . '.zip';
            $zipPath = $tempDir . '/' . $zipFileName;
            
            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
                $this->addDirToZip($zip, $zipDir, '');
                $zip->close();
            } else {
                throw new AppException(StatusCode::ERR_SERVER, 'ZIP文件创建失败');
            }
            
            // 读取ZIP文件内容
            $zipContent = file_get_contents($zipPath);
            
            if ($zipContent === false) {
                throw new AppException(StatusCode::ERR_SERVER, '无法读取ZIP文件');
            }
            
            // 清理临时目录
            $this->cleanupTempDir($tempDir);
            
            // 返回ZIP文件流
            return [
                'file_content' => $zipContent,
                'file_name' => $zipFileName,
                'mime_type' => 'application/zip',
                'document_count' => $documents->count()
            ];
        }

        /**
         * 清理临时目录
         * 
         * @param string $dir 要清理的目录
         */
        private function cleanupTempDir(string $dir): void
        {
            if (!is_dir($dir)) {
                return;
            }
            
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($files as $file) {
                if ($file->isDir()) {
                    rmdir($file->getRealPath());
                } else {
                    unlink($file->getRealPath());
                }
            }
            
            rmdir($dir);
        }
        
        /**
         * 或可以扩展为其他格式的导出功能
         * 例如：exportToWord(), exportToHtml(), exportToMarkdown()等
         */
    } 