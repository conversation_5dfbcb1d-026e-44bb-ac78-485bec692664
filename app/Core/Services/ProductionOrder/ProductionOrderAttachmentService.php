<?php

namespace App\Core\Services\ProductionOrder;

use App\Constants\ProductionOrderCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\UserService;
use App\Core\Utils\Tree;
use App\Exception\AppException;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\ProductionOrderAttachmentModel;
use App\Model\TchipBi\ProductionOrderInfoModel;
use App\Model\TchipBi\ProductionOrderModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkStatusModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;
use Throwable;

class ProductionOrderAttachmentService extends BusinessService
{
    /**
     * @Inject()
     * @var ProductionOrderAttachmentModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var Tree
     */
    protected $treeService;

    public function getList(array $filter = [], array $op = [], string $sort = 'sort', string $order = 'DESC', int $limit = 100)
    {
        if (empty($filter['production_order_id'])) {
            return [];
        }
        $attachmentType = empty($filter['attachment_category']) ? null : trim($filter['attachment_category']);
        unset($filter['attachment_category']);
        //根据上传类型获取分类
        if ($attachmentType) {
            $categoryPid = CategoryModel::where('keywords', $attachmentType)->value('id');
            $filter['category_pid'] = $categoryPid;
        }
        if (!isset($filter['is_extra'])) {
            $filter['is_extra'] = 0;
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $query = $query->with(['attachment'])->orderBy($sort, $order)->paginate($limit);
        $result = $query->toArray();
        //订单信息
        $info = ProductionOrderInfoModel::query()->where('production_order_id', $filter['production_order_id'])->first();
        $userIds = [
            'hardware_user'           => $info['hardware_user_id'],
            'layout_user'           => $info['layout_user_id'],
            'software_user'           => $info['software_user_id'],
            'structure_user'          => $info['structure_user_id'],
            'order_user'              => $info['order_user_id'],
            'production_user'         => $info['production_user_id'],
            'test_user'               => $info['test_user_id'],
            'attachments_upload_user' => $info['attachments_upload_user_id']
        ];
        $userIdsArr = array_unique(array_filter(array_values(($userIds))));

        $user_data = $userIdsArr?UserModel::query()->whereIn('id', $userIdsArr)->pluck('name', 'id')->toArray():[];

        //获取子类文件列表
        $childData = [];
        foreach ($result['data'] as &$item) {
            //根据分类的标识来获取对应的审批人员
            if ($attachmentType == ProductionOrderCode::ATTACH_TYPE_BASE) {
                $item['assign_user_id'] = $info[$item['file_key'] . '_user_id'] ?? 0;
            } elseif ($attachmentType == ProductionOrderCode::ATTACH_TYPE_FIRST) {
                $item['assign_user_id'] = $info['hardware_user_id'];
            } else {
                $item['assign_user_id'] = 0;
            }
            $item['assign_user_name'] = $user_data[$item['assign_user_id']] ?? '';
            $dataTemp = $this->model::query()->with('attachment')->where('category_pid', $item['category_id'])
                ->where('production_order_id', $filter['production_order_id'])
                ->where('is_extra', 0)
                ->select('*')->selectRaw($item['assign_user_id'] . " as assign_user_id")
                ->selectRaw("'{$item['assign_user_name']}'"." as assign_user_name")
                ->get();
            if ($dataTemp) {
                $dataTemp = $dataTemp->toArray();
                //硬件负责人变为原理图负责人，新增layout负责人
                if($attachmentType == ProductionOrderCode::ATTACH_TYPE_BASE){
                    foreach ($dataTemp as &$cItem){
                        if(in_array($cItem['file_key'],ProductionOrderCode::ATTACH_TYPE_LAYOUT_ARR)){
                            $cItem['assign_user_id'] = $info['layout_user_id'];
                            $cItem['assign_user_name'] = $user_data[$cItem['assign_user_id']] ?? '';
                        }
                    }
                }
                $childData = array_merge($childData, $dataTemp);
            }
        }
        $result['data'] = array_merge($result['data'], $childData);
        //获取人员信息
        $userIds = array_filter(array_merge(array_column($result['data'], 'upload_user_id'), array_column($result['data'], 'audit_user_id')));
        $userList = $userIds ? UserModel::query()->whereIn('id', $userIds)->pluck('name', 'id')->toArray() : [];

        //满足以下条件才返回url
        //  超管 || 生产角色 || 生产文件上传角色 || 工厂角色 || 订单负责人 || 生产主管 || 测试主管 || 硬件负责人 || layout负责人 || 软件负责人 || 结构负责人
        $attachmentPermission = $this->checkAttachmentPermission($filter['production_order_id'],$info);

        foreach ($result['data'] as &$item) {
            // 如果没有权限，则清除附件的url和full_url
            if (!$attachmentPermission && !empty($item['attachment'])) {
                if (isset($item['attachment']['url'])) {
                    $item['attachment']['url'] = null;
                }
                if (isset($item['attachment']['full_url'])) {
                    $item['attachment']['full_url'] = null;
                }
            }

            $item['upload_user_name'] = $userList[$item['upload_user_id']] ?? null;
            $item['audit_user_name'] = $userList[$item['audit_user_id']] ?? null;
        }
        //整理树型结构
        $minId = ($result && $result['data'] ? min(array_column($result['data'], 'category_id')) : 0) ?? 0;
        $result['data'] = make(Tree::class)->getTreeListV3($result['data'], $minId, 'sort', 'DESC');

        return $result;
    }

    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        if (empty($filter['production_order_id'])) {
            return [];
        }
        $attachmentType = empty($filter['attachment_category']) ? null : trim($filter['attachment_category']);
        unset($filter['attachment_category']);
        //根据上传类型获取分类
        if ($attachmentType) {
            $categoryPid = CategoryModel::where('keywords', $attachmentType)->value('id');
            $filter['category_pid'] = $categoryPid;
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        $result = $query->with('attachment')->orderBy($sort, $order)->get()->toArray();
        $userIds = array_unique(array_filter(array_column($result, 'upload_user_id')));
        $userList = $userIds ? UserModel::query()->whereIn('id', $userIds)->pluck('name', 'id')->toArray() : [];
        //校验权限
        //满足以下条件才返回url
        //  超管 || 生产角色 || 生产文件上传角色 || 工厂角色 || 订单负责人 || 生产主管 || 测试主管 || 硬件负责人 || 软件负责人 || 结构负责人
        $attachmentPermission = $this->checkAttachmentPermission($filter['production_order_id']);

        foreach ($result as &$item) {
            // 如果没有权限，则清除附件的url和full_url
            if (!$attachmentPermission && !empty($item['attachment'])) {
                if (isset($item['attachment']['url'])) {
                    $item['attachment']['url'] = null;
                }
                if (isset($item['attachment']['full_url'])) {
                    $item['attachment']['full_url'] = null;
                }
            }
            $item['upload_user_name'] = $userList[$item['upload_user_id']] ?? null;
        }
        return $result;
    }


    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            if ($id === -2) {
                //分类管理增加了新的分类
                //需要批量插入
                $orderIds = ProductionOrderModel::query()->pluck('id')->toArray();
                $insertData = [];
                //新增的文件分类，默认为不用上传
                foreach ($orderIds as $orderId) {
                    $insertData[] = array_merge($values, ['production_order_id' => $orderId,'is_required'=>0]);
                }
                $result = $this->model::query()->insert($insertData);

            } elseif ($id === -3) {
                // 分类配置里进行了修改，需要批量更新
                $result = $this->model->where('category_id', $values['category_id'])->update($values);
            } elseif ($id > 0) {
                $hasChangeAudit = false;
                $sendWxNoticeData = [];
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, 'No_results_were_found');
                }
                $info = ProductionOrderInfoModel::query()->where('production_order_id', $row['production_order_id'])->first();
                $infoWorkStatusKey = WorkStatusModel::query()->where('id', $info['work_status_id'] ?? 0)->value('key') ?: '';
                //重新上传后审核状态改为未审核
                if (!empty($values['attachment_id']) && $values['attachment_id'] != $row->attachment_id) {
                    if ($row->audit_status == 1) {
                        throw new AppException(StatusCode::ERR_EXCEPTION, '文件已审核，不允许重新上传');
                    }
                    $values['audit_status'] = 0;
                    $values['audit_time'] = null;
                }
                //处理审核时间和拒绝理由
                if (isset($values['audit_status']) && $values['audit_status'] != $row->audit_status) {
                    $values['audit_user_id'] = $values['audit_status'] == 0 ? 0 : auth()->id();
                    $values['audit_time'] = $values['audit_status'] == 0 ? null : date('Y-m-d H:i:s');
                    if ($values['audit_status'] == 1) {
                        $values['reject_reason'] = '';
                    } else {
                        $fileName = AttachmentModel::query()->where('id', $values['attachment_id'])->value('filename') . (!empty($values['reject_reason']) ? "({$values['reject_reason']})" : '');
                        $sendWxNoticeData = [
                            'file_name' => $fileName
                        ];
                    }
                    $hasChangeAudit = true;
                }
                $result = $row->update($values);

                $attachmentCategory = empty($values['attachment_category']) ? ProductionOrderCode::ATTACH_TYPE_TOP : trim($values['attachment_category']);
                if ($attachmentCategory == ProductionOrderCode::ATTACH_TYPE_BASE) {
                    //全部审核完后有驳回的文件则回滚状态且通知
//                    if ($hasChangeAudit && $infoWorkStatusKey == 'data_to_audit') {
//                        $this->rejectAttachment($attachmentCategory, $row['production_order_id']);
//                    }
                    if ($sendWxNoticeData) {
                        make(NoticeService::class)->rejectProductionOrder($row->upload_user_id, $row->production_order_id, $sendWxNoticeData);
                    }
                } elseif ($attachmentCategory == ProductionOrderCode::ATTACH_TYPE_FIRST) {
                    //首件上传完，写上实际上线时间
                    $fileStatus = $this->getFileStatusByCategory($attachmentCategory, $row->production_order_id);
                    $info = ProductionOrderInfoModel::query()->where('production_order_id', $row->production_order_id)->first();
                    if ($fileStatus['upload_status'] == 1 && empty($info['actual_online_time'])) {
                        make(ProductionOrderInfoService::class)->doEdit(0, [
                            'production_order_id' => $row->production_order_id,
                            'actual_online_time'  => date('Y-m-d')
                        ]);

                    } elseif ($fileStatus['upload_status'] == 0 && !empty($info['actual_online_time'])) {
                        make(ProductionOrderInfoService::class)->doEdit(0, [
                            'production_order_id' => $row->production_order_id,
                            'actual_online_time'  => null
                        ]);
                    }
                }
            } else {
                if (!empty($values['category_id']) && empty($values['category_pid'])) {
                    $category = CategoryModel::find($values['category_id']);
                    $values = array_merge($values, [
                        'category_pid' => $category['pid'] ?? 0,
                        'sort'         => $category['sort'] ?? 1000,
                        'file_type'    => $category['name'] ?? '',
                        'file_key'     => $category['keywords'] ?? '',
                    ]);
                }
                $result = $this->model::query()->create($values);
            }
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 修改文件分类下的所有文件上传和审核状态
     * @param $categoryPid
     * @param $productionOrderId
     * @return void
     */
    public function changeInfoAttachmentStatus($categoryKeywords, $productionOrderId)
    {
        $fileStatus = $this->getFileStatusByCategory($categoryKeywords, $productionOrderId);
        Logger()->info('----------------file_status---------------------', $fileStatus);
        $updateOrderInfo = [];
        if ($categoryKeywords == ProductionOrderCode::ATTACH_TYPE_BASE) {
            $updateOrderInfo['base_file_upload_status'] = $fileStatus['upload_status'];
            $updateOrderInfo['base_file_audit_status'] = $fileStatus['audit_status'];
        } elseif ($categoryKeywords == ProductionOrderCode::ATTACH_TYPE_FIRST) {
            $updateOrderInfo['first_file_upload_status'] = $fileStatus['upload_status'];
            $updateOrderInfo['first_file_audit_status'] = $fileStatus['audit_status'];
        }
        if ($updateOrderInfo) {
            $updateOrderInfo['production_order_id'] = $productionOrderId;
            make(ProductionOrderInfoService::class)->doEdit(0, $updateOrderInfo);
        }
    }

    /**
     * 文件审批后的后续操作
     * @param $categoryKeywords
     * @param $productionOrderId
     * @return void
     */
    public function rejectAttachment($categoryKeywords, $productionOrderId)
    {
        $rejectAuditStatus = ProductionOrderCode::ATTACH_AUDIT_STATUS_REJECT;
        //获取分类下的文件
        $categoryPid = CategoryModel::query()->where('keywords', $categoryKeywords)->where('type', ProductionOrderCode::ATTACH_TYPE_TOP)->value('id') ?: 0;
        $filesAttachment = $this->getUnderAttachByCategory($productionOrderId, $categoryPid);
        $auditStatusArr = [];
        $sendWxUserData = [];
        //汇总审核状态和驳回数据
        foreach ($filesAttachment as $attach) {
            //不需要上传的默认为已审核
            if (!$attach['is_required']) {
                $auditStatusArr[] = ProductionOrderCode::ATTACH_AUDIT_STATUS_PASS;
            } else {
                $auditStatusArr[] = $attach['audit_status'];
                if ($attach['audit_status'] == $rejectAuditStatus) {
                    $fileName = AttachmentModel::query()->where('id', $attach['attachment_id'])->value('filename') ?: '';
                    $sendWxUserData[$attach['upload_user_id']][] = $fileName . ($attach['reject_reason'] ? "({$attach['reject_reason']})" : '');
                }
            }
        }
        //文件的审批状态已全部审核且有不通过的，则回滚状态和发送通知
        if (!in_array(ProductionOrderCode::ATTACH_AUDIT_STATUS_NOT, $auditStatusArr) && $sendWxUserData) {
            //回滚工作状态
            $updateOrderInfo = [];
            if ($categoryKeywords == ProductionOrderCode::ATTACH_TYPE_BASE) {
                $updateOrderInfo['base_file_audit_status'] = $rejectAuditStatus;
                $updateOrderInfo['base_file_upload_status'] =  0;
            } elseif ($categoryKeywords == ProductionOrderCode::ATTACH_TYPE_FIRST) {
                $updateOrderInfo['first_file_audit_status'] = $rejectAuditStatus;
            }
            if ($updateOrderInfo) {
                $updateOrderInfo['production_order_id'] = $productionOrderId;
                make(ProductionOrderInfoService::class)->doEdit(0, $updateOrderInfo);
            }
            //通知未通过的文件的上传人
            foreach ($sendWxUserData as $userId => $fileData) {
                $fileName = implode("\n", $fileData);
                $noticeParams = [
                    'file_name' => $fileName
                ];
                make(NoticeService::class)->rejectProductionOrder($userId, $productionOrderId, $noticeParams);
            }
        }
    }

    /**
     * 获取一个分类下的文件总的上传状态和审核状态
     * @param $keywords
     * @param $productionOrderId
     * @return int[]
     */
    public function getFileStatusByCategory($keywords, $productionOrderId)
    {
        $uploadStatus = 1;
        $auditStatus = 1;
        //硬件资料区分成了原理图和layout审核
        if($keywords == 'schematic' || $keywords == 'layout'){
            $cateKeyword = ProductionOrderCode::ATTACH_TYPE_HARD;
        }else{
            $cateKeyword = $keywords;
        }
        $categoryPid = CategoryModel::query()->where('keywords', $cateKeyword)->where('type', ProductionOrderCode::ATTACH_TYPE_TOP)->value('id') ?: 0;
        $filesAttachment = $this->getUnderAttachByCategory($productionOrderId, $categoryPid);
        $auditStatusArr = [];
        //从硬件资料过滤出原理图内容或者layout
        if($keywords == 'schematic'){
            $filesAttachment = array_filter($filesAttachment,function($hItem){
                return !in_array($hItem['file_key'],ProductionOrderCode::ATTACH_TYPE_LAYOUT_ARR);
            });
        }elseif ($keywords == 'layout'){
            $filesAttachment = array_filter($filesAttachment,function($hItem){
                return in_array($hItem['file_key'],ProductionOrderCode::ATTACH_TYPE_LAYOUT_ARR);
            });
        }
        foreach ($filesAttachment as $value) {
            //需要上传但未上传
            //未通过审核的也默认未上传
            if ($value['is_required'] && empty($value['attachment_id'])) {
                $uploadStatus = 0;
            } elseif ($value['is_required'] && !empty($value['attachment_id']) && $value['audit_status'] == 2) {
                $uploadStatus = 0;
            }
            //不需要上传的默认为已审核
            if (!$value['is_required']) {
                $auditStatusArr[] = 1;
            } else {
                $auditStatusArr[] = $value['audit_status'];
            }
        }

        if (in_array(2, $auditStatusArr)) {
            $auditStatus = 2;
        } elseif (in_array(0, $auditStatusArr)) {
            $auditStatus = 0;
        }
        return [
            'upload_status' => $uploadStatus,
            'audit_status'  => $auditStatus
        ];
    }

    /**
     * 按分类生成文件
     * @param $productionOrderId
     * @return void
     */
    public function initProductionOrderFile($productionOrderId)
    {
        Db::beginTransaction();
        try {
            if ($this->model::query()->where('production_order_id', $productionOrderId)->exists()) {
                return;
            }
            $categoryData = make(CategoryModel::class)::query()
                ->where('type', '=', ProductionOrderCode::ATTACH_TYPE_TOP)
                ->where('pid', '!=', 0)->get()->toArray();
            $insertData = [];
            foreach ($categoryData as $value) {
                //是否必须上传
                $isRequired = in_array($value['keywords'],ProductionOrderCode::ATTACH_TYPE_NOT_REQUIRE_ARR)? 0 : 1;
                //高温老化要求默认文本为【-20 ~ 60℃， 时长8小时】
                $attachmentRemark = $value['keywords'] == ProductionOrderCode::ATTACH_TYPE_HIGH_TEMP ? '-20 ~ 60℃， 时长8小时' : '';
                $data = [
                    'production_order_id' => $productionOrderId,
                    'category_id'         => $value['id'],
                    'category_pid'        => $value['pid'],
                    'sort'                => $value['sort'],
                    'file_type'           => $value['name'],
                    'file_key'            => $value['keywords'],
                    'is_required'       => $isRequired,
                    'attachment_remark' => $attachmentRemark,
                ];
                $insertData[] = $data;
            }
            $this->model->insert($insertData);
            Db::commit();
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 批量编辑
     * @param $ids
     * @param $params
     * @return bool
     */
    public function doMulti($ids, $params): bool
    {
        if (empty($ids) || empty($params)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }
        if (!is_array($params)) {
            parse_str($params, $params);
        }
        $count = 0;
        Db::beginTransaction();
        try {
            $hasChangeAudit = false;
            $sendWxUserData = [];
            $items = $this->model::query()->whereIn('id', $ids)->get();
            $productionOrderId = $items[0]->production_order_id;
            $info = ProductionOrderInfoModel::query()->where('production_order_id', $productionOrderId)->first();
            $infoWorkStatusKey = WorkStatusModel::query()->where('id', $info['work_status_id'] ?? 0)->value('key') ?: '';
            foreach ($items as $item) {
                $values = $params;
                //批量修改审核状态的只有状态不相同的才会修改
                if (isset($values['audit_status']) && $values['audit_status'] != $item->audit_status) {
                    $values['audit_user_id'] = $values['audit_status'] == 0 ? 0 : auth()->id();
                    $values['audit_time'] = $values['audit_status'] == 0 ? null : date('Y-m-d H:i:s');
                    if ($values['audit_status'] != 2) {
                        $values['reject_reason'] = '';
                    } else {
                        $fileName = AttachmentModel::query()->where('id', $item->attachment_id)->value('filename') . ($values['reject_reason'] ? "({$values['reject_reason']})" : '');
                        $sendWxUserData[$item->upload_user_id][] = $fileName;
                    }
                    $result = $item->update($values);
                    $result && $hasChangeAudit = true;
                    $count += $result;
                } else {
                    $count += $item->update($values);
                }
            }

            //审核阶段，检查是否全部已经审核，全部审核后有驳回的发送推送并回滚状态
            //录入阶段，驳回立即通知上传人
            //现录入阶段也可以审核文件，故添加状态限制
//            if ($hasChangeAudit && $infoWorkStatusKey == 'data_to_audit') {
//                $attachmentCategory = empty($values['attachment_category']) ? ProductionOrderCode::ATTACH_TYPE_TOP : trim($values['attachment_category']);
//                $this->rejectAttachment($attachmentCategory, $productionOrderId);
//            }
            if ($sendWxUserData) {
                foreach ($sendWxUserData as $userId => $fileData) {
                    $fileName = implode("\n", $fileData);
                    $noticeParams = [
                        'file_name' => $fileName,
                    ];
                    make(NoticeService::class)->rejectProductionOrder($userId, $productionOrderId, $noticeParams);
                }
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        if ($count > 0) {
            return true;
        } else {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
        }
    }

    /**
     * 获取生产订单某个类型的文件
     * @param $orderId
     * @param $attachmentType
     * @return array|mixed[]
     */
    public function getAllByTypeAndOrder($orderId, $attachmentType)
    {
        $categoryPid = CategoryModel::where('keywords', $attachmentType)->value('id');
        $result = $this->model::query()->where('category_pid', $categoryPid)->where('production_order_id', $orderId)->get();
        return $result ? $result->toArray() : [];
    }

    /**
     * 获取最底层分类的文件
     * @param $productionOrderId
     * @param $categoryPid
     * @return array
     */
    public function getUnderAttachByCategory($productionOrderId, $categoryPid): array
    {
        $filesAttachment = $this->model::query()
            ->where([
                'production_order_id' => $productionOrderId,
                'is_extra' =>0
            ])
            ->get()->toArray();
        $data = [];
        $treeData = $this->treeService->getChildV3($filesAttachment, $categoryPid, 'sort');
        // 使用递归函数获取最底层分类数据
        $this->getUnderData($treeData, $data);
        return $data;
    }

    //获取底层等级的数据
    public function getUnderData($data, &$result)
    {
        foreach ($data as $item) {
            if (empty($item['children'])) {
                $result[] = $item;
            } else {
                $this->getUnderData($item['children'], $result);
            }
        }
    }


    /**
     * 更新文件的是否需上传状态
     * @param $keywords string 文件类别标识
     * @param $productionOrderId
     * @param $isRequired
     * @return void
     */
    public function changeIsRequired($keywords, $productionOrderId, $isRequired)
    {
        $categoryPid = CategoryModel::query()->where('keywords', $keywords)->where('type', ProductionOrderCode::ATTACH_TYPE_TOP)->value('id') ?: 0;
        $filesAttachment = $this->model::query()->where('production_order_id',$productionOrderId)
        ->where('category_pid',$categoryPid)->get()->toArray();
        $attachmentId = array_filter(array_column($filesAttachment, 'attachment_id'));
        //已经上传不再改变
        if ($attachmentId) return;
        $ids = array_column($filesAttachment, 'id');
        $ids && $this->model::query()->whereIn('id', $ids)->update(['is_required' => $isRequired]);
    }

    /**
     * 检查文件权限
     * @param $orderId
     * @param mixed $orderInfo
     * @return bool
     */
    public function checkAttachmentPermission($orderId,  $orderInfo = [])
    {
        //校验权限
        $userId = $this->auth->user()->getId();
        $userInfo = make(UserService::class)->getUserInfo($userId, false);
        if (!$orderInfo && $orderId) {
            $orderInfo = ProductionOrderInfoModel::query()->where('production_order_id', $orderId)->first();
        }
        //满足以下条件才有权限
        //  超管 || 生产角色 || 生产文件上传角色 || 工厂角色 || 订单负责人 || 生产主管 || 测试主管 || 硬件负责人 || 软件负责人 || 结构负责人
        return array_intersect([
                'Admin',
                'Production',
                'ProductionFileUploader',
                'Factory'
            ], $userInfo['roles']) || in_array($userId, [
                $orderInfo['order_user_id'] ?? 0,
                $orderInfo['production_user_id'] ?? 0,
                $orderInfo['test_user_id'] ?? 0,
                $orderInfo['hardware_user_id'] ?? 0,
                $orderInfo['software_user_id'] ?? 0,
                $orderInfo['structure_user_id'] ?? 0,
                $orderInfo['layout_user_id'] ?? 0,
            ]);
    }

    public function uploadForRepair($params)
    {

        if (empty($params['production_order_id']) || empty($params['attachment_ids']) || empty($params['attachment_category'])) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        if (!is_array($params['attachment_ids'])) {
            $params['attachment_ids'] = explode(',', $params['attachment_ids']);
        }
        Db::beginTransaction();
        try {
            $category = CategoryModel::query()->where('keywords', $params['attachment_category'])->first();
            foreach ($params['attachment_ids'] as $attachmentId) {
                $values = [
                    'production_order_id' => $params['production_order_id'],
                    'attachment_id'       => $attachmentId,
                    'upload_user_id'      => Auth()->id(),
                    'category_pid'        => $category['id'] ?? 0,
                ];
                $this->doEdit(0, $values);
            }
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

}