<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/21 下午8:22
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services;

use App\Core\Services\Queue\QueueService;
use App\Exception\AppException;
use App\Model\TchipBi\CrontabModel;
use Hyperf\Cache\Listener\DeleteListenerEvent;
use Hyperf\Di\Annotation\Inject;
use Poliander\Cron\CronExpression;
use Psr\EventDispatcher\EventDispatcherInterface;

class SystemService
{
    /**
     * @Inject()
     * @var EventDispatcherInterface
     */
    protected $dispatcher;

    /**
     * @Inject()
     * @var QueueService
     */
    protected $queueService;

    public function flushCache(): bool
    {
        $this->dispatcher->dispatch(new DeleteListenerEvent('marketing-topcard-user-month-update', []));
        return true;
    }

    public function doNowTask($id)
    {

        $param = ['class' => \App\Job\TaskJob::class, 'params'=>['id' => $id]];
        $this->queueService->push($param);
        // $crond = \App\Model\TchipBi\CrontabModel::query()->find($id);
        // if ($crond) {
        //     $crond = $crond->toArray();
        //     $dt = new \DateTime(date('Y-m-d H:i', time()));
        //     if ($crond['class'] && class_exists($crond['class'])) {
        //         if (method_exists($crond['class'], $crond['method'])) {
        //             $class = $crond['class'];
        //             $method = $crond['method'];
        //             $schedule = $crond['schedule'];
        //             $crondId = $crond['id'];
        //             $services = make($class);
        //             $services->$method();
        //             CrontabModel::where('id', $crondId)->update(['executetime' => date('Y-m-d H:i:s')]);
        //         }
        //     }
        //     return true;
        // }
        // return false;
        return true;
    }
}