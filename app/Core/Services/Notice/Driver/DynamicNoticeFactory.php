<?php

namespace App\Core\Services\Notice\Driver;

use Hyperf\Utils\ApplicationContext;
use App\Exception\AppException;

class DynamicNoticeFactory
{
    protected const BASE_NAMESPACE = 'App\\Core\\Services\\Notice\\Driver\\';

    /**
     * 动态调用通知处理类的 handle 方法
     *
     * @param string $type 类名（不带命名空间）
     * @param mixed ...$arguments 参数列表
     * @return mixed
     */
    public static function call(string $type, ...$arguments)
    {
        $className = self::BASE_NAMESPACE . $type;

        if (!class_exists($className)) {
            throw new AppException("找不到指定的通知处理器类：{$className}");
        }

        //获取容器实例
        $instance = make($className); 
        // $instance = ApplicationContext::getContainer()->get($className);// 也可以这样获取

        // 确保实现了 handle 方法
        if (!method_exists($instance, 'handle')) {
            throw new AppException("类 {$className} 中未定义 handle() 方法");
        }

        return $instance->handle(...$arguments);
    }
}