<?php

namespace App\Core\Services\Notice;

use App\Constants\StatusCode;
use App\Exception\AppException;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class UserNoticeSubscribeService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var \App\Model\TchipBi\UserNoticeSubscribeModel
     */
    protected $model;

    public function doMultiSave(array $params)
    {
        Db::beginTransaction();
        try {
            foreach ($params as $param) {
                $this->model::updateOrCreate(['user_id' => $param['user_id'], 'notice_id' => $param['notice_id']], $param);
            }
            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

    }

}