<?php

namespace App\Core\Services\Notice;

use App\Constants\DepartmentCode;
use App\Constants\OaQcErpCode;
use App\Constants\ProductionOrderCode;
use App\Constants\ProjectCode;
use App\Constants\StatusCode;
use App\Core\Services\ProductionOrder\ProductionOrderService;
use App\Core\Services\ProductionOrder\ProductionOuthelpRecordService;
use App\Core\Services\Queue\QueueService;
use App\Core\Services\UserService;
use App\Core\Services\Setting\DepartmentManagermentService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\ProjectModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\NoticeModel;
use App\Model\TchipBi\ProductionOrderModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserNoticeSubscribeModel;
use App\Model\TchipBi\AuthGroupAccessModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use League\HTMLToMarkdown\HtmlConverter;
use Parsedown;
use Qbhy\HyperfAuth\AuthManager;
use function vierbergenlars\SemVer\Internal\validRange;

class NoticeService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var NoticeModel
     */
    protected $model;

    /**
     * @Inject()
     * @var UserNoticeSubscribeModel
     */
    protected $userSubscribeModel;

    /**
     * @Inject()
     * @var QueueService
     */
    protected $queueService;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * 获取通知目标用户ID列表（包含手动选择、部门、角色）
     * 2025.05.14 新增可以订阅所有用户
     * @param int $noticeId 通知ID
     * @return array 合并去重后的目标用户ID数组
     */
    public function getTargetUserIds($noticeId)
    {
        $targetUserIds = [];
        
        // 获取通知信息
        $notice = $this->model::query()->where('id', $noticeId)->where('status', 1)->first();
        if (!$notice) {
            Log::get('system', 'system')->info("通知ID: {$noticeId} 不存在或未启用");
            return [];
        }

        // 2025.05.14 新增可以订阅所有用户
        if ($notice->subscribe_all_users == 1) {
            $targetUserIds = UserModel::query()->where('status', 1)->pluck('id')->toArray();
        }else{
            // 1. 添加手动选择的用户
            $manualUserIds = !empty($notice->users) ? 
            (!is_array($notice->users) ? json_decode($notice->users, true) : $notice->users) : [];
        
            if (!empty($manualUserIds)) {
                $targetUserIds = array_merge($targetUserIds, array_map('intval', $manualUserIds));
            }
        
            // 2. 添加部门中的所有用户
            $departmentIds = !empty($notice->departments) ? 
                (!is_array($notice->departments) ? json_decode($notice->departments, true) : $notice->departments) : [];
            
            if (!empty($departmentIds)) {
                $departmentService = make(DepartmentManagermentService::class);
                $departmentUserIds = [];
                foreach ($departmentIds as $deptId) {
                    $deptUsers = $departmentService->departmentsUsers($deptId);
                    if (!empty($deptUsers)) {
                        $departmentUserIds = array_merge(
                            $departmentUserIds, 
                            array_column($deptUsers, 'id')
                        );
                    }
                }
                $targetUserIds = array_merge($targetUserIds, $departmentUserIds);
            }
        
            // 3. 添加角色中的所有用户
            $roleIds = !empty($notice->roles) ? 
                (!is_array($notice->roles) ? json_decode($notice->roles, true) : $notice->roles) : [];
            
            if (!empty($roleIds)) {
                // 使用AuthGroupAccessModel直接查询角色用户
                $roleUsers = AuthGroupAccessModel::query()
                    ->whereIn('group_id', $roleIds)
                    ->pluck('user_id')
                    ->toArray();
                    
                if (!empty($roleUsers)) {
                    $targetUserIds = array_merge($targetUserIds, $roleUsers);
                }
            }
        }
        
        // 去重并过滤掉可能的空值
        $targetUserIds = array_filter(array_unique($targetUserIds));
        
        $userCount = count($targetUserIds);
        Log::get('system', 'system')->info("通知ID: {$noticeId} 汇总用户列表共 {$userCount} 个用户");
        
        return $targetUserIds;
    }

    /**
     * 处理通知发送，判断用户是否在通知目标范围内，相较旧版消息推送逻辑移除了用户订阅设置
     * @param string $method 通知方法名
     * @param int $userId 用户ID
     * @param array $params 通知参数
     * @return bool
     */
    public function handleNotice($method, $userId, $params)
    {
        // 获取通知配置
        $notice = $this->model::query()->where('method', $method)->where('status', 1)->first();
        if (!$notice) {
            Log::get('system', 'system')->info("通知方法 '{$method}' 未配置或未启用");
            return false;
        }

        $noticeId = $notice->id;
        
        // 检查用户是否应该接收通知
        // $shouldReceive = false;
        
        // 如果该通知是全体人员，直接允许接收
        if ($notice->subscribe_all_users == 1) {
            $shouldReceive = true;
        } else {
            // 如果不是全体人员通知，检查用户是否在目标列表中，getTargetUserIds方法获取所有目标用户（包括单独选择人员、整个部门和整个角色），如果不在订阅范围，则不推送
            $targetUserIds = $this->getTargetUserIds($noticeId);
            $shouldReceive = in_array($userId, $targetUserIds);
        }
        
        // 不应该接收通知，记录日志并返回
        if (!$shouldReceive) {
            Log::get('system', 'system')->info("用户ID: {$userId} 不在通知 '{$notice->name}' 的目标范围内");
            return true;
        }
        
        // 2.2消息自身订阅
        $subscribeMode = !empty($subscribeMode) ? $subscribeMode : $notice->default_subscribe;

        // 3.1没有订阅渠道使用系统定义的
        if (empty($subscribeMode)) {
            switch ($method) {
                case 'everydayWorkRemind':
                    $subscribeMode = ['email'];
            }
        }

        if ($subscribeMode) {
            foreach ($subscribeMode as $item) {
                $tplKey = $item . '_tpl';
                $template = $notice->$tplKey; // 获取通知本身的模板

                // 如果通知本身没有设置模板，则尝试从关联的分类中获取默认模板
                if (empty($template) && $notice->category) {
                    $template = trim($notice->category->$tplKey); // 获取分类的默认模板 在NoticeModel中定义了关联
                }

                if (empty($template)) {
                    Log::get('system', 'system')->info("通知ID {$noticeId} 未定义模板，在消息类型模板{$notice->notice_type}也未找到{$item}的默认模板");
                    continue;
                }

                $queueData = [
                    'class'  => '\App\Job\Notice\\' . ucwords($item) . '\SendNoticeJob',
                    'params' => [
                        'notice_title' => $notice->name,
                        'method'       => $method,
                        'user_id'      => $userId,
                        'params'       => $params,
                        'tpl'          => $template,
                        'notice_type'  => $notice->notice_type,
                        'notice_mode'  => $item,
                        'notice_id'    => $noticeId
                    ],
                ];
                Log::get('system', 'system')->info("以下消息内容压入队列...", $queueData);
                $this->queueService->push($queueData);
            }
        } else {
            Log::get('system', 'system')->info("消息 {$notice->name} 没有默认的推送模板，请到后台设置");
        }

        return true;
    }

    /**
     * 旧版消息推送逻辑 包含检查用户订阅设置
     */
    // public function handleNotice($method, $userId, $params)
    // {
    //     // 1.1查询用户是否存在个性化订阅
    //     // 2.1存在时判断订阅开关是否进行推送
    //     // 2.2不存在时默认为消息自带的订阅方式进行推送
    //     // 3.1使用程序定义的
    //     $notice = $this->model::query()->where('method', $method)->where('status', 1)->first();
    //     if ($notice) {
    //         $noticeId      = $notice->id;
    //         $noticeType    = $notice->notice_type;
    //         $subscribeMode = [];

    //         // 强制订阅
    //         $isForceSubscribe = $notice->is_force_subscribe;

    //         if (!$isForceSubscribe) {
    //             // 只有非强制订阅才检查用户的订阅设置
    //             $userSubscribe = $this->userSubscribeModel::query()
    //                 ->where('user_id', $userId)->where(function ($query) use($noticeId) {
    //                     return $query->where('notice_id', $noticeId)->where('subscribe_type', 'id');
    //                 })->orWhere(function ($query) use($noticeType) {
    //                     return $query->where('notice_type', $noticeType)->where('subscribe_type', 'type');
    //                 })->first();
    //             // 1.1, 2.1
    //             if ($userSubscribe) {
    //                 if ( $userSubscribe->is_subscribe != 1) {
    //                     Log::get('system', 'system')->info("用户ID: {$userId} . 关闭了 {$notice->name} 的订阅. ");
    //                     return true;
    //                 }
    //                 // 暂不使用用户订阅方式
    //                 // else if (!empty($userSubscribe->subscribe_mode)) {
    //                 //     // 用户订阅
    //                 //     $subscribeMode = $userSubscribe->subscribe_mode;
    //                 // }
    //             } else {
    //                 // $userIds = !empty($notice->users) ? (!is_array($notice->users) ? json_decode($notice->users, true) : $notice->users) : [];
    //                 // if (!in_array($userId, $userIds)) {
    //                 //     Log::get('system', 'system')->info("用户ID: {$userId} . 不在 {$notice->name} 的订阅范围. ");
    //                 //     return true;
    //                 // }

    //                 // 使用getTargetUserIds方法获取所有目标用户（包括手动添加、部门和角色），如果不在订阅范围，则不推送
    //                 $targetUserIds = $this->getTargetUserIds($notice->id);
    //                 if (!in_array($userId, $targetUserIds)) {
    //                     Log::get('system', 'system')->info("用户ID: {$userId} . 不在 {$notice->name} 的订阅范围. ");
    //                     return true;
    //                 }
    //             }
    //         }else{
    //             // 强制订阅
    //             $targetUserIds = $this->getTargetUserIds($notice->id);
    //             if (!in_array($userId, $targetUserIds)) {
    //                 Log::get('system', 'system')->info("用户ID: {$userId} . 不在 {$notice->name} 的订阅范围. ");
    //                 return true;
    //             }
    //         }

    //         // 2.2消息自身订阅
    //         $subscribeMode = !empty($subscribeMode) ? $subscribeMode : $notice->default_subscribe;

    //         // 3.1没有订阅渠道使用系统定义的
    //         if (empty($subscribeMode)) {
    //             switch ($method) {
    //                 case 'everydayWorkRemind':
    //                     $subscribeMode = ['email'];
    //             }
    //         }

    //         if ($subscribeMode) {
    //             foreach ($subscribeMode as $item) {
    //                 $tplKey = $item . '_tpl';
    //                 $template = $notice->$tplKey; // 获取通知本身的模板

    //                 // 如果通知本身没有设置模板，则尝试从关联的分类中获取默认模板
    //                 if (empty($template) && $notice->category) {
    //                     $template = trim($notice->category->$tplKey); // 获取分类的默认模板 在NoticeModel中定义了关联
    //                 }

    //                 if (empty($template)) {
    //                     Log::get('system', 'system')->info("通知ID {$noticeId} 未定义模板，在消息类型模板{$notice->notice_type}也未找到{$item}的默认模板");
    //                     continue;
    //                 }

    //                 $queueData = [
    //                     'class'  => '\App\Job\Notice\\' . ucwords($item) . '\SendNoticeJob',
    //                     'params' => [
    //                         'notice_title' => $notice->name,
    //                         'method'       => $method,
    //                         'user_id'      => $userId,
    //                         'params'       => $params,
    //                         'tpl'          => $template,
    //                         'notice_type'  => $notice->notice_type,
    //                         'notice_mode'  => $item,
    //                         'notice_id'    => $noticeId
    //                     ],
    //                 ];
    //                 Log::get('system', 'system')->info("以下消息内容压入队列...", $queueData);
    //                 $this->queueService->push($queueData);
    //             }
    //         } else {
    //             Log::get('system', 'system')->info("消息 {$notice->name} 没有默认的推送模板，请到后台设置");
    //         }
    //     }
    //     return true;
    // }

    /**
     * 每天工作提醒通知
     * @return void
     */
    public function everydayWorkRemind()
    {
        Log::get('system', 'system')->info('--------------------- 消息通知:everydayWorkRemind;开始 ---------------------');
        $notice   = $this->model::query()->where('method', 'everydayWorkRemind')->where('status', 1)->first();
        // $usersIds = !empty($notice->users) ? (!is_array($notice->users) ? json_decode($notice->users, true) : $notice->users) : [];

        // 2025.04.17使用新方法获取所有目标用户ID
        $usersIds = $this->getTargetUserIds($notice->id);

        if ($usersIds) {
            $userCount = count($usersIds);
            Log::get('system', 'system')->info("--------------------- 查找到共 {$userCount} 名用户 ---------------------");
            $issueService = make(\App\Core\Services\Project\IssueService::class);
            foreach ($usersIds as $userId) {
                $user = UserModel::query()->with(['third' => function ($query){
                    return $query->where('platform', 'redmine');
                }])->where('id', $userId)->where('status', 1)->first();
                if (!$user) {
                    Log::get('system', 'system')->info("用户ID: {$userId} 不存在...");
                    continue;
                }
                $userArr = $user->toArray();
                // $issueList  = $issueService->getList(['assigned_to_id' => $userArr['third']['third_user_id'], 'status_id' => implode(',', $issueService->getToDoStatusIds())], ['status_id' => 'IN'], 'id', 'DESC', 100);
                // $issueList  = $issueList['data'] ?? [];
                // $issueCount = count($issueList);
                $todoCount    = $issueService->toDoCount($userArr['third']['third_user_id']);
                $overdueCount = $issueService->overdueCount($userArr['third']['third_user_id']);
                $params     = [
                    'username'      => $userArr['name'],
                    // 'todo_count'    => $issueService->toDoCount($userArr['third']['third_user_id']),
                    // 'overdue_count' => $issueService->overdueCount($userArr['third']['third_user_id']),
                    'other_data'    => $userArr['third']['third_user_id'],
                ];
                Log::get('system', 'system')->info("用户: {$userArr['name']} , uid: {$userArr['id']} , redmineId: {$userArr['third']['third_user_id']}");
                // 有数据才需要推送
                if ($todoCount == 0 && $overdueCount == 0) {
                    Log::get('system', 'system')->info("用户: {$userArr['name']} 没有需要处理的工作事项，不下发消息...");
                    continue;
                }

                Log::get('system', 'system')->info("用户: {$userArr['name']} 开始下发消息...");
                $this->handleNotice('everydayWorkRemind', $userArr['id'], $params);
            }
        } else {
            Log::get('system', 'system')->info('--------------------- 没有需要推送的用户。 ---------------------');
        }
        Log::get('system', 'system')->info('--------------------- 消息通知:everydayWorkRemind;结束 ---------------------');
    }

    /**
     * 检验超时未检通知
     * @return void
     */
    public function qcTimeoutTodo() :bool
    {
        $notice   = $this->model::query()->where('method', 'qcTimeoutTodo')->where('status', 1)->first();
        if (!$notice) {
            Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]不存在执行结束');
            return false;
        }
        Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]开始执行');
        // $usersIds = !empty($notice->users) ? (!is_array($notice->users) ? json_decode($notice->users, true) : $notice->users) : [];
        
        // 2025.04.17使用新方法获取所有目标用户ID
        $usersIds = $this->getTargetUserIds($notice->id);

        if (!$usersIds) {
            Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]没有可通知的用户执行结束');
            return false;
        }
        $users = UserModel::query()->whereIn('id', $usersIds)->get();
        if (!$users) {
            Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]没有可通知的用户执行结束');
            return false;
        }
        $userCount = count($usersIds);
        Log::get('system', 'system')->info("查找到共 {$userCount} 名用户");
        $qcService = make(\App\Core\Services\TchipOa\OaQcService::class);
        // 两天前
        $date = date('Y-m-d H:i:s', (time() - 172800));
        $filter = [
            'created_at' => $date,
            'status' => 0
        ];
        $op = [
            'created_at' => '<'
        ];
        $rows = $qcService->getAllList($filter, $op);
        $rows = $rows ? (is_object($rows) ? $rows->toArray() : $rows) : [];
        if (count($rows) <= 0) {
            Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]没有未检验的记录结束通知');
            return false;
        }
        $content     = '';
        $bodyContent = '';
        foreach ($rows as $row) {
            $content     .= "类型:{$row['type_text']},单号:{$row['order_no']},入库号:{$row['enter_no']},料号:{$row['prod_code']},产品:{$row['prod_name']},报检时间:{$row['created_at']}." . PHP_EOL;
            $bodyContent .= "<tr><td>{$row['type_text']}</td><td>{$row['order_no']}</td><td>{$row['enter_no']}</td><td>{$row['prod_code']}</td><td>{$row['prod_name']}</td><td>{$row['created_at']}</td></tr>";
        }


        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        foreach ($users as $user) {
            Log::get('system', 'system')->info("[消息通知:qcTimeoutTodo]推送到用户:{$user->name}");
            $params = [
                'username'    => $user->name,
                'content'     => $content,
                'bodyContent' => $bodyContent,
                'host'        => $host,
            ];
            // $this->handleNotice('qcTimeoutTodo', $user->id, $params);
        }
        Log::get('system', 'system')->info('[消息通知:qcTimeoutTodo]执行完成');
        return true;
    }

    /**
     * AT我的
     * @param $issueId
     * @param $notes
     * @return void
     */
    public function atMe($issueId, $notes, $containerType = 'issue', $values = null)
    {
        $issue = IssueModel::query()->where('id', $issueId)->first();
        if (!$issue) {
            return false;
        }
        $host  = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $names = matchAtUserName($notes);
        //去除@自身
        if(!empty($values['author_info']['name'])){
            $index = array_search($values['author_info']['name'], $names);
            if ($index !== false) {
                unset($names[$index]);
            }
        }
        // $assigned = $issue->assigned_to_id;
        // if (!empty($issue->assigned_to_id)) {
        //     $assigned = make(\App\Core\Services\UserService::class)->userInfoByThirdUserId($issue->assigned_to_id);
        //     if ($assigned) {
        //         $names[] = $assigned['name'];
        //     }
        // }
        $users = UserModel::query()->whereIn('name', $names)->where('status', 1)->get();
        // markdown转html
        $notes = strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($notes)));
        foreach ($users as $user) {
            $params = [
                'author'      => $values['author_info']['name'] ?? '',
                'username'    => $user->name,
                'subject'     => $issue->subject,
                'host'        => $host,
                'project_id'  => $issue->project_id,
                'project_name' => ProjectModel::query()->where('id', $issue->project_id)->value('name'),
                'id'          => $issue->id,
                'notes'       => $notes,
            ];
            $method = 'at' . ucfirst($containerType);
            $this->handleNotice($method, $user->id, $params);
        }
    }

    /**
     * @param int $assignedId 指定推送的用户
     * @param $issueId 事项ID
     * @param $notes 内容
     * @param $method 推送方法
     * @param array $values 其它数据
     * @return false|void
     */
    public function assignedNotice(int $assignedId, int $issueId, string $notes, string $method, array $values = [])
    {
        $issue = IssueModel::query()->with('issueAssigned')->where('id', $issueId)->first();

        // 指派人通知，并不存在指派人时退出
        if (!$issue) {
            return false;
        }
        $issueArr = $issue->toArray();
        $assignedIds = !empty($issueArr['issue_assigned']) ? array_column($issueArr['issue_assigned'], 'user_id') : (!empty($issue->assigned_to_id) ? [$issue->assigned_to_id] : []);

        // 处理人通知时，没有处理人不需要通知
        if (($method == 'assignedJournals' && empty($assignedIds))) {
            return false;
        }


        // 解释是否有@的人
        $names = matchAtUserName($notes);

        // 处理人通知时，如果处理人同时也是创建人，即不需要再发送通知 发送逻辑 @我 > 创建人 > 处理人 > 关注人
        // if ($method == 'assignedJournals' && !empty($issue->assigned_to_id) && $issue->assigned_to_id == $issue->author_id) {
        if ($method == 'assignedJournals' &&  in_array($issue->author_id, $assignedIds)) {
            return false;
        }
        $assigned = make(\App\Core\Services\UserService::class)->userInfoByThirdUserId($assignedId);

        // 如果@的人是 创建人 或者 处理人 即不需要发送
        if (!empty($names) && is_array($names) && count($names) > 0 && in_array($assigned['name'], $names)) {
            return false;
        }

        $host  = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        // markdown转html
        // 假设条件为 $convertToMarkdown 为 true 时进行转换 20250113
        $noHtmltags= $values['journal_need_no_html_tags'] ?? false; // 条件，根据需要设置
        //var_dump($notes);

        if ($noHtmltags) {
            // 提取代码块并保留其内容
            preg_match_all('/```(.*?)```/is', $notes, $matches);

            // 保存代码块内容并替换为占位符
            $codeBlocks = $matches[1];
            foreach ($codeBlocks as $index => $codeBlock) {
                $notes = str_replace($codeBlock, "{{CODE_BLOCK_$index}}", $notes);
            }

            // 移除其他 HTML 标签，除了代码块
            $notes = strip_tags($notes, ''); // 移除所有HTML标签

            // 恢复代码块内容
            foreach ($codeBlocks as $index => $codeBlock) {
                $notes = str_replace("{{CODE_BLOCK_$index}}", "```$codeBlock```", $notes);
            }

            //var_dump('---------------------');

        } else {
            //var_dump('---------------------');
            // 原逻辑：处理 Markdown 转 HTML，然后去掉标签
            $notes = strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($notes)));
        }
        //var_dump($notes);

        $params = [
            'username'     => $assigned['name'], // 事项处理人名称
            'author'       => $values['author'] ?? ($values['author_info']['name'] ?? ''), // 回复人名称
            'subject'      => $issue->subject,
            'host'         => $host,
            'id'           => $issue->id,
            'project_id'   => $issue->project_id,
            'project_name' => ProjectModel::query()->where('id', $issue->project_id)->value('name'),
            'notes'        => $notes,
            'created_on'   => !empty($values['created_on']) ? date('Y-m-d H:i', strtotime($values['created_on'])) : '',
        ];
        $this->handleNotice($method, $assigned['id'], $params);
    }

    /**
     * @param int $assignedId 事项关注人事项说明通知
     * @param $issueId 事项ID
     * @param $notes 内容
     * @param array $values 其它参数
     * @return false|void
     */
    public function watchersNotice(int $issueId, string $notes, array $values = [])
    {
        $issue = IssueModel::query()->with('issueAssigned')->where('id', $issueId)->first();
        if (!$issue) {
            return false;
        }
        $issueArr = $issue->toArray();
        $watchers = make(\App\Core\Services\Project\WatcherService::class)->getIssueWatcherUserList($issueId);
        $assignedIds = !empty($issueArr['issue_assigned']) ? array_column($issueArr['issue_assigned'], 'user_id') : (!empty($issue->assigned_to_id) ? [$issue->assigned_to_id] : []);
        if ($watchers) {
            foreach ($watchers as $key => $watcher) {
                // issue为创建，或者处理人时，不需要再进行关注通知. 发送逻辑 创建人 > 处理人 > 关注人
                // if ( $watcher['id'] == $issue->author_id || (!empty($issue->assigned_to_id) && $watcher['id'] == $issue->assigned_to_id) ) {
                if ( $watcher['id'] == $issue->author_id || in_array($watcher['id'], $assignedIds) ) {
                    unset($watchers[$key]);
                }
            }
            $biUserIds =  array_column(array_values($watchers), 'user_id');

            // 解释是否有@的人
            $names = matchAtUserName($notes);
            $users = \App\Model\TchipBi\UserModel::query()->whereIn('id', $biUserIds)->where('status', 1)->get()->toArray();
            $host  = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
            // markdown转html
            $notes = strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($notes)));
            foreach ($users as $user) {
                // 如果@的人是 创建人 或者 处理人 即不需要发送
                if (in_array($user['name'], $names)) continue;
                $params = [
                    'username'     => $user['name'],
                    'author'       => $values['author_info']['name'] ?? '',
                    'subject'      => $issue->subject,
                    'host'         => $host,
                    'id'           => $issueId,
                    'project_id'   => $issue->project_id,
                    'project_name' => ProjectModel::query()->where('id', $issue->project_id)->value('name'),
                    'notes'        => $notes,
                    'created_on'   => !empty($values['created_on']) ? date('Y-m-d H:i', strtotime($values['created_on'])) : '',
                ];
                $this->handleNotice('watchersJournals', $user['id'], $params);
            }
        }
    }


    /**
     * QC报检通知
     * @param $qcId
     * @return void
     */
    public function qcExamine($qcId, $notice = 'examine')
    {
        $appParams = [
            'examine_text' => '外观检测',
            'type_text'  => '',
            'order_no'  => '',
            'enter_no'  => '',
            'status_text'  => '',
            'examine_num' => '',
            'prod_name' => '',
            'prod_spec' => '',
            'examine_remark'  => '',
            'host'     => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $exaParams = [
            'examine_text' => '负责人',
            'type_text'  => '',
            'order_no'  => '',
            'enter_no'  => '',
            'status_text'  => '',
            'examine_num' => '',
            'prod_name' => '',
            'prod_spec' => '',
            'examine_remark'  => '',
            'host'     => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $funParams = [
            'examine_text' => '功能检测',
            'type_text'  => '',
            'order_no'  => '',
            'enter_no'  => '',
            'status_text'  => '',
            'examine_num' => '',
            'prod_name' => '',
            'prod_spec' => '',
            'examine_remark'  => '',
            'host'     => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        if (is_array($qcId)) {
            // 多条报捡推送消息
            $qc = \App\Model\TchipBi\OaQcModel::query()->whereIn('id', $qcId)->get();
            $qc = $qc ? $qc->toArray() : [];

            // 获取类型
            $typeIds = array_unique(array_column($qc, 'type'));
            $typeList = \App\Model\TchipBi\CategoryModel::query()->whereIn('id', $typeIds)->get();
            $typeList = $typeList ? array_column($typeList->toArray(), null, 'id') : [];

            $appIds = $exaIds = $funIds = [];
            $appIds = array_filter(array_unique(array_column($qc, 'appearance_examine_user')));
            $exaIds = array_filter(array_unique(array_column($qc, 'examine_user')));
            $funIds = array_filter(array_unique(array_column($qc, 'function_examine_user')));
            $appOrders = $exaOrders = $funOrders = [];
            foreach ($qc as &$q) {

                // 加入负责人需要通知的人
                if (!empty($q['examine_user'])) {
                    $exaParams['examine_remark'] .= ($typeList[$q['type']]['name'] ?? '未知类型') . ':' . $q['order_no'] . PHP_EOL;
                    $exaIds[] = $q['examine_user'];
                }

                // 加入外观需要通知的人
                if (!empty($q['appearance_examine_user'])) {
                    $appParams['examine_remark'] .= ($typeList[$q['type']]['name'] ?? '未知类型') . ':' . $q['order_no'] . PHP_EOL;
                    $appIds[] = $q['appearance_examine_user'];
                }

                // 加入功能需要通知的人
                if (!empty($q['function_examine_user'])) {
                    $funParams['examine_remark'] .= ($typeList[$q['type']]['name'] ?? '未知类型') . ':' . $q['order_no'] . PHP_EOL;
                    $funIds[] = $q['function_examine_user'];
                }


                // $examineOption = !empty($q['examine_option']) ? (is_array($q['examine_option']) ? $q['examine_option'] : json_decode($q['examine_option'], true)) : [];
                // // 加入用户ID列表
                // if (!in_array($q['appearance_examine_user'], $uIds)) {
                //     $uIds[] = $q['appearance_examine_user'];
                // }
                // if (!in_array($q['function_examine_user'], $uIds)) {
                //     $uIds[] = $q['function_examine_user'];
                // }
                // // 加入推送外观单号的用户
                // if (empty($examineUsers[$q['appearance_examine_user']])) {
                //     $examineUsers[$q['appearance_examine_user']] = [];
                //     $examineUsers[$q['appearance_examine_user']][] = $q['order_no'];
                // } else {
                //     $examineUsers[$q['appearance_examine_user']][] = $q['order_no'];
                // }
                // // 加入推送功能单号的用户
                // if (empty($examineUsers[$q['function_examine_user']])) {
                //     $examineUsers[$q['function_examine_user']] = [];
                //     $examineUsers[$q['function_examine_user']][] = $q['order_no'];
                // } else {
                //     $examineUsers[$q['function_examine_user']][] = $q['order_no'];
                // }
                // if (in_array('外观', $examineOption) && !empty($q['appearance_examine_user'])) {
                //
                //     if (empty($appUsers[$q['appearance_examine_user']])){
                //         $appUsers[$q['appearance_examine_user']] = [];
                //     }
                //     $appUsers[$q['appearance_examine_user']][] = $q['order_no'];
                //     // if (!in_array($q['appearance_examine_user'], $appUsers)) {
                //     //     $appUsers[] = $q['appearance_examine_user'];
                //     // }
                //     // $appOrder[] = $q['order_no'];
                // }
                // if (in_array('功能', $examineOption) && !empty($q['function_examine_user'])) {
                //     if (!in_array($q['appearance_examine_user'], $uIds)) {
                //         $uIds[] = $q['appearance_examine_user'];
                //     }
                //     if (!in_array($q['function_examine_user'], $funUsers)) {
                //         $funUsers[] = $q['function_examine_user'];
                //     }
                //     $funOrder[] = $q['order_no'];
                // }
            }
            $users = \App\Model\TchipBi\UserModel::query()->whereIn('id', array_merge($exaIds, $appIds, $funIds))->get();
            $users = $users ? array_column($users->toArray(), null, 'id') : [];
            foreach ($users as $user) {
                // 开始推送外观
                // if (in_array($user['id'], $appIds)) {
                //     $appParams['username'] = $user['name'];
                //     // $uid = $user->id;
                //     $this->handleNotice('qcExamine',  $user['id'], $appParams);
                // }
                // 开始推送负责人
                if (in_array($user['id'], $exaIds)) {
                    $exaParams['username'] = $user['name'];
                    $this->handleNotice('qcExamine', $user['id'], $exaParams);
                }
                // 开始推送功能
                // if (in_array($user['id'], $funIds)) {
                //     $funParams['username'] = $user['name'];
                //     $this->handleNotice('qcExamine', $user['id'], $funParams);
                // }
            }
        } else {
            // 单条报检推送消息
            $qc = \App\Model\TchipBi\OaQcModel::query()->find($qcId);
            if ($qc && !empty($qc->examine_user) ) {
                $user = \App\Model\TchipBi\UserModel::query()->find($qc->examine_user);
                if ($user) {
                    $params['examine_text'] = '负责人';
                    $params['username'] = $user->name;
                    $params['type_text'] = $qc->type_text ?? \App\Model\TchipBi\CategoryModel::query()->where('id', $qc->type)->value('name');
                    $params['order_no'] = $qc->order_no;
                    $params['enter_no'] = $qc->enter_no;
                    $params['status_text'] = $qc->status_text;
                    $params['examine_num'] = $qc->examine_num;
                    $params['prod_name'] = $qc->prod_name;
                    $params['prod_spec'] = $qc->prod_spec;
                    $params['examine_remark'] = $qc->examine_remark;
                    $uid = $user->id;
                    $this->handleNotice('qcExamine', $uid, $params);
                }
            }
        }
    }

    /**
     * QC报检，检验完成通知检验结果通知
     * @param $qcId
     * @return void
     */
    public function qcExamineResult($qcId) :bool
    {
        Log::get('system', 'system')->info("[消息通知:qcExamineResult]开始执行");
        $userService = make(\App\Core\Services\UserService::class);
        $users = [];
        $usersOfPurchase = [];
        $usersOfWarehouse = $userService->assignDepartmentUserList([DepartmentCode::DEPARTMENT_WAREHOUSE]);
        if (!$usersOfWarehouse) {
            Log::get('system', 'system')->info("[消息通知:qcExamineResult]没有可推送的用户执行结束");
            return false;
        }
        $params = [
            'type_text'     => '',
            'order_no'      => '',
            'enter_no'      => '',
            'status_text'   => '',
            'examine_num'   => '',
            'prod_name'     => '',
            'prod_spec'     => '',
            'handle_text'   => '',
            'result_remark' => '',
            'host'          => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $allInfo = [];

        if (is_array($qcId)) {
            $qcAll = \App\Model\TchipBi\OaQcModel::query()->whereIn('id', $qcId)->get();
            $qcAll = $qcAll ? $qcAll->toArray() : [];

            foreach ($qcAll as $qc) {
                    $tmpParams = $params;
                    $tmpParams['type_text']     = $qc['type_text'] ?? \App\Model\TchipBi\CategoryModel::query(true)->where('id', $qc['type'])->value('name');
                    $tmpParams['order_no']      = $qc['order_no'];
                    $tmpParams['enter_no']      = $qc['enter_no'];
                    $tmpParams['status_text']   = $qc['status_text'];
                    $tmpParams['examine_num']   = $qc['examine_num'];
                    $tmpParams['prod_name']     = $qc['prod_name'];
                    $tmpParams['prod_spec']     = $qc['prod_spec'] ;
                    $tmpParams['handle_text']   = $qc['handle_text']  ?? '';
                    $tmpParams['result_remark'] = $qc['result_remark'];
                    $tmpParams['host']          = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');

                    // 状态为合格，但存在 不合格数量 > 0 才会推送通知给仓库 20240828[issue-id=14291] (合格但退货情况暂时不在批量处理中考虑)
                    if (($qc['status'] == OaQcErpCode::STATUS_OK)) {
                        if ($qc['defective_num'] == 0) {
                            // 状态为合格不合格数量 == 0，不予发送该条信息
                            continue;
                        } else if ($qc['defective_num'] > 0) {
                            $tmpParams['status_text'] .= ',  不合格数量:' . $qc['defective_num'];
                        }
                    }
                    $allInfo[] = $tmpParams;
            }

        } else {
            $qc = \App\Model\TchipBi\OaQcModel::query()->find($qcId);
            if ($qc) {
                $params['type_text']     = $qc->type_text ?? \App\Model\TchipBi\CategoryModel::query()->where('id', $qc->type)->value('name');
                $params['order_no']      = $qc->order_no;
                $params['enter_no']      = $qc->enter_no;
                $params['status_text']   = $qc->status_text;
                $params['examine_num']   = $qc->examine_num;
                $params['prod_name']     = $qc->prod_name;
                $params['prod_spec']     = $qc->prod_spec;
                $params['handle_text']   = $qc->handle_text ?? '';
                $params['result_remark'] = $qc->result_remark;
                $params['host']          = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
            }

            $allInfo[] =  $params;

            // 状态为合格，但存在 不合格数量 > 0 才会推送通知给仓库 20240828[issue-id=14291]
            if (($qc['status'] == OaQcErpCode::STATUS_OK )) {
                if ($qc['defective_num'] == 0) {
                    // 不符合条件，不发送
                    $usersOfWarehouse = [];
                } else if ($qc['defective_num'] > 0) {
                    $params['status_text'] .= ',  不合格数量:' . $qc['defective_num'] ;
                }
            }
        }

        $returnCode = null;
        foreach (OaQcErpCode::HANLDE_LIST as $item) {
            if ( $item['label'] == '退货') {
                $returnCode = $item['value'];
            }
        }
        // 不合格
        // 只有批量处理方案与不合格处理方案中之一存在'退货'时候需要通知采购角色20240625
        if (
            (!empty($qc['handle']) && $qc['handle'] == $returnCode)
            || (!empty($qc['unqualified_handle']) && $qc['unqualified_handle'] == $returnCode)) {
            // 获取采购分类
            $pCategory = make(\App\Core\Services\Setting\CategoryService::class)->detail(['type' => 'qc_type', 'keywords' => 'purchase_enter']);
            $pCategory = $pCategory ? (is_object($pCategory) ? $pCategory->toArray() : $pCategory) : null;
            if ($pCategory) {
                if ($qc['type'] == $pCategory['id']) {
                    $purchaseUser = $userService->assignDepartmentUserList([DepartmentCode::DEPARTMENT_PURCHASE]);
                    if (is_array($purchaseUser) && count($purchaseUser) > 0) {
                        //$usersOfWarehouse = array_merge($usersOfWarehouse, $purchaseUser);
                        $usersOfPurchase = array_merge($usersOfPurchase, $purchaseUser);

                    }
                }
            }
        }



        $users = array_merge($usersOfWarehouse,$usersOfPurchase);

        if (empty($users) || empty($allInfo)) {
            Log::get('system', 'system')->info("[消息通知:qcExamineResult]没有可推送的用户或可推送的内容，执行结束");
            return false;
        }



        // 合并需要推送的用户信息
        foreach ($allInfo as $params) {
            foreach ($users as $user) {
                Log::get('system', 'system')->info("[消息通知:qcExamineResult]开始推送用户:{$user['name']}{$user['id']}");
                $params['username'] = $user['name'];
                $uid = $user['id'];
                $this->handleNotice('qcExamineResult', $uid, $params);
            }
        }

        Log::get('system', 'system')->info("[消息通知:qcExamineResult]执行完成");
        return true;
    }

    public function recoverProject($params)
    {
        $userService = make(\App\Core\Services\UserService::class);
        $rauthorInfo = $userService->userInfoByThirdUserId($params['rauthor_info']['id']);
        $tqRauthorInfo = UserModel::query()->where('id', $rauthorInfo['id'])->where('status', 1)->first();
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $values = [
            'id'           => $params['journalized_id'],
            'project_id'   => $params['project_id'],
            'subject'      => $params['subject'],
            'project_name' => $params['project_name'],
            'author'       => $params['author_info']['name'] ?? '',
            'created_on'   => !empty($params['created_on']) ? date('Y-m-d H:i', strtotime($params['created_on'])) : '',
            'project_name' => $params['project_name'],
            'notes'        => strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($params['journal_ext']['notes_html']))),
            'host'         => $host,
        ];
        $this->handleNotice('recoverProject', $tqRauthorInfo->id, $values);
    }

    public function recoverProduct($params)
    {
        $userService = make(\App\Core\Services\UserService::class);
        $rauthorInfo = $userService->userInfoByThirdUserId($params['rauthorid']);
        $tqRauthorInfo = UserModel::query()->where('id', $rauthorInfo['id'])->where('status', 1)->first();
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $host .= "/#/project/ProjectProgress?project_id={$params['project_id']}&skip={$params['pid']}";
        $values = [
            'author'            => $params['author'],
            'rauthor'           => $params['rauthor'],
            'project_type_text' => $params['type_text'],
            'project_name'      => $params['product']['name'],
            'notes'             => strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($params['description']))),
            'host'              => $host,
        ];
      $this->handleNotice('recoverProduct', $tqRauthorInfo->id, $values);
    }

    public function atProjectProgress($progressId, $values)
    {
        $notes = $values['description'] ?? null;
        if (!$notes) return false;
        // TODO 原子性操作问题因为异步任务问题，在这里执行的时候上轮数据有可能提交未完成导致查询可能出来false，需要重复执行
        $c = 0;
        while ($c < 5) {
            $progress = \App\Model\Redmine\ProjectsProgressModel::query()->with(['createUser', 'project', 'projectExt'])->find($progressId);
            if ($progress) {
                $c = 10;
                break;
            } else {
                sleep(1);
                $c++;
            }
        }
        if (!$progress) return false;
        $progress = $progress->toArray();
        $names = matchAtUserName($notes);
        if (count($names) <= 0) return false;
        if(isset($values['pid'])){
            $index = array_search($values['author'], $names);
            if ($index !== false) {
                unset($names[$index]);
            }
        }
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $projectTypeText = '项目';
        if (!empty($progress['project_ext']['project_type']) && $progress['project_ext']['project_type'] == ProjectCode::PROJECT_TYPE_PROD) {
            $activeName = 'workProgress';
            if ($progress['type'] == 10) {
                $activeName = 'noticePCN';
            }
            $host .= "/#/project/productDetailsIndex?product_id={$progress['project_id']}&active_name={$activeName}&skip={$progress['id']}";
            $projectTypeText = '产品';
        } else {
            $host .= "/#/project/ProjectProgress?project_id={$progress['project_id']}&skip={$progress['id']}";
        }

        $users = UserModel::query()->whereIn('name', $names)->where('status', 1)->get();
        foreach ($users as $user) {
            $params = [
                'username'    => $values['author'],
                'project_type_text' => $projectTypeText,
                'project_name' => $progress['project']['name'],
                'notes' => strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($notes))),
                'host' => $host,
            ];
            $this->handleNotice('atProjectProgress', $user->id, $params);
        }
    }

    public function atProjectComment($commentId, $values)
    {
        $notes = $values['description'] ?? null;
        if (!$notes) return false;
        // TODO 原子性操作问题因为异步任务问题，在这里执行的时候上轮数据有可能提交未完成导致查询可能出来false，需要重复执行
        $c = 0;
        while ($c < 5) {
            $progress = \App\Model\Redmine\UserCommentsModel::query()->with(['progress', 'project', 'projectExt'])->find($commentId);
            if ($progress) {
                $c = 10;
                break;
            } else {
                sleep(1);
                $c++;
            }
        }
        if (!$progress) return false;
        $progress = $progress->toArray();
        $names = matchAtUserName($notes);
        if (count($names) <= 0) return false;
        if(isset($values['pid'])){
            $index = array_search($values['author'], $names);
            if ($index !== false) {
                unset($names[$index]);
            }
        }
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $projectTypeText = '项目';
        if (!empty($progress['project_ext']['project_type']) && $progress['project_ext']['project_type'] == ProjectCode::PROJECT_TYPE_PROD) {
            $activeName = 'workProgress';
            if (!empty($progress['progress']['type']) && $progress['progress']['type'] == 10) {
                $activeName = 'noticePCN';
            }
            $host .= "/#/project/productDetailsIndex?product_id={$progress['project_id']}&active_name={$activeName}&skip={$progress['id']}";
            $projectTypeText = '产品';
        } else {
            $host .= "/#/project/ProjectProgress?project_id={$progress['project_id']}&skip={$progress['id']}";
        }

        $users = UserModel::query()->whereIn('name', $names)->where('status', 1)->get();
        foreach ($users as $user) {
            $params = [
                'username'    => $values['author'],
                'project_type_text' => $projectTypeText,
                'project_name' => $progress['project']['name'],
                'notes' => strip_tags(\Parsedown::instance()->text(htmlspecialchars_decode($notes))),
                'host' => $host,
            ];
            $this->handleNotice('atProjectProgress', $user->id, $params);
        }
    }

    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            
            // 保存通知基本信息
            $result = parent::doEdit($id, $values);
            $id = $id > 0 ? $id : $result->id;
            $resData = is_object($result) ? $result->toArray() : $result;
            
            $users = [];
            // 手动选择的用户
            if (!empty($values['users'])) {
                $users = $values['users'];
            }
            
            // 选择的部门下的用户
            if (!empty($values['departments'])) {
                $departmentService = make(DepartmentManagermentService::class);
                foreach ($values['departments'] as $deptId) {
                    $deptUsers = $departmentService->departmentsUsers($deptId);
                    foreach ($deptUsers as $user) {
                        $users[] = $user['id'];
                    }
                }
            }
            
            // 选择的角色包含的用户
            if (!empty($values['roles'])) {
                $roleUsers = AuthGroupAccessModel::query()
                    ->whereIn('group_id', $values['roles'])
                    ->pluck('user_id')
                    ->toArray();
                $users = array_merge($users, $roleUsers);
            }
            
            // 去重合并后的用户ID
            $users = array_values(array_unique($users));

            // 获取现有的所有订阅记录
            $existingSubscriptions = UserNoticeSubscribeModel::query()
                ->where('notice_id', $id)
                ->get()
                ->toArray();
                        
            // 提取现有订阅记录的用户ID
            $existingUserIds = array_column($existingSubscriptions, 'user_id');
            
            // 获取已存在记录的用户订阅状态映射
            $userSubscriptionStatus = [];
            foreach ($existingSubscriptions as $subscription) {
                $userSubscriptionStatus[$subscription['user_id']] = $subscription['is_subscribe'];
            }
            
            // 计算需要取消订阅的用户ID（在现有记录中但不在新用户列表中）
            $unsubscribeUserIds = array_values(array_diff($existingUserIds, $users));

            // 先更新不订阅的用户记录
            if (!empty($unsubscribeUserIds)) {
                $updateCount = UserNoticeSubscribeModel::query()
                    ->where('notice_id', $id)
                    ->whereIn('user_id', $unsubscribeUserIds)
                    ->update(['is_subscribe' => 0]);
            }

            /**
             * 是否覆盖用户设置
             * Q：为什么需要覆盖用户设置？
             * A：因为用户可能主动取消订阅，但是管理员可能需要强制这些用户重新订阅，所以需要覆盖用户设置
             */
            $overrideUserSettings = !empty($values['override_user_settings']);
            
            // 再处理需要订阅的用户
            if (!empty($users)) {
                $userNoticeSubscribeService = make(UserNoticeSubscribeService::class);
                $saveUser = [];
                foreach ($users as $user) {
                    // 确定订阅状态
                    $isSubscribe = 1; // 默认为订阅
                    
                    // 如果不覆盖用户设置，并且用户已经主动取消订阅，则保持取消状态
                    if (!$overrideUserSettings && isset($userSubscriptionStatus[$user]) && $userSubscriptionStatus[$user] == 0) {
                        $isSubscribe = 0;
                    } else if ($overrideUserSettings && isset($userSubscriptionStatus[$user]) && $userSubscriptionStatus[$user] == 0) {
                        Log::get('system', 'system')->info("用户ID: {$user} 已主动取消订阅，但管理员选择覆盖设置，重新订阅");
                        $isSubscribe = 1;
                    }

                    // 强制订阅
                    if ($values['is_force_subscribe'] == 1) {
                        $isSubscribe = 1;
                    }
                
                    $saveUser[] = [
                        'user_id' => $user,
                        'notice_id' => $id,
                        'notice_type' => $values['notice_type'] ?? $resData['notice_type'],
                        'subscribe_type' => $values['subscribe_type'] ?? 'id',
                        'subscribe_mode' => $values['default_subscribe'] ?? $resData['default_subscribe'],
                        'is_subscribe' => $isSubscribe,
                    ];
                }
                
                $userNoticeSubscribeService->doMultiSave($saveUser);
            }
            
            Db::commit();
            return $result;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 生产订单工作状态变更推送企业微信
     * @param $userId int|array 推送用户
     * @param $data   array 数据
     * @return void
     */
    public function changeProductionOrderWorkStatus($userId, $data)
    {
        $params = [
            'code'         => $data['code'] ?? '',
            'product_name' => $data['product_name'] ?? '',
            'woqty'        => $data['woqty'] ?? 0,
            'product_code' => $data['product_code'] ?? '',
            'factory_name' => $data['factory_name'] ?? '',
            'old_status'   => $data['old_status'] ?? '',
            'new_status'   => $data['new_status'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id' => $data['id'] ?? 0,
            'extra_msg' => $data['extra_msg']??''
        ];
        $this->bindUserSend($userId, $params, 'changeProductionOrderWorkStatus');
    }

    /**
     * 生产订单文件驳回通知
     * @param $userId
     * @param $productionOrderId
     * @param $data
     * @return void
     */
    public function rejectProductionOrder($userId,$productionOrderId,$data)
    {
        $order = ProductionOrderModel::find($productionOrderId);
        $params = [
            'code'         => $order['code'] ?? '',
            'product_name' => $order['product_name'] ?? '',
            'woqty'        => $order['woqty'] ?? 0,
            'product_code' => $order['product_code'] ?? '',
            'factory_name' => $order['factory_name'] ?? '',
            'file_name'   => $data['file_name'] ?? '',
//            'reject_reason'   => $data['reject_reason'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id' => $order['id'] ?? 0
        ];
        $this->bindUserSend($userId, $params, 'rejectProductionOrder');
    }

    /**
     * 生产订单录入通知
     * @param $userId
     * @param $data
     * @return void
     */
    public function createProductionOrder($userId, $data)
    {
        $params = [
            'code'         => $data['code'] ?? '',
            'product_name' => $data['product_name'] ?? '',
            'woqty'        => $data['woqty'] ?? 0,
            'product_code' => $data['product_code'] ?? '',
            'factory_name' => $data['factory_name'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id' => $data['id'] ?? 0
        ];
        $this->bindUserSend($userId, $params, 'createProductionOrder');
    }

    /**
     * 生产订单信息完善通知
     * @param $userId
     * @param $data
     * @return void
     */
    public function commonProductionOrderNotice($userId, $data)
    {
        $params = [
            'notice_msg'   => $data['notice_msg'] ?? '',
            'code'         => $data['code'] ?? '',
            'product_name' => $data['product_name'] ?? '',
            'woqty'        => $data['woqty'] ?? 0,
            'product_code' => $data['product_code'] ?? '',
            'factory_name' => $data['factory_name'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id'     => $data['id'] ?? 0
        ];
        $this->bindUserSend($userId, $params, 'commonProductionOrderNotice');
    }
    /**生产订单上线日期未填通知
     * @return void
     */
    public function remindEditOrderPredictTime()
    {
        Log::get('system', 'system')->info('--------------------- 消息通知:remindEditOrderPredictTime;开始 ---------------------');
        //获取工作状态为预计上线日期但未填写预计上线时间的订单
        $field = [
            'production_order.*',
            'production_order_info.production_user_id',
        ];
        $list = ProductionOrderModel::query()->leftJoin('production_order_info', 'production_order_info.production_order_id', '=', 'production_order.id')
            ->leftJoin('work_status', 'work_status.id', '=', 'production_order_info.work_status_id')
            ->whereNull('production_order_info.predict_online_time')
            ->where('work_status.key','predict_online_time')
            ->select($field)
            ->get();
        $list = $list ? $list->toArray() : [];
        $bodyContent = '';
        Log::get('system', 'system')->info('--------------------- 消息通知:remindEditOrderPredictTime;数据获取 ---------------------');
        $userData = [];
        //发送给：生产主管
        foreach ($list as $item) {
            if(empty($item['production_user_id'])) continue;
            if(!isset($userData[$item['production_user_id']])){
                $userData[$item['production_user_id']] = "<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";;
            }else{
                $userData[$item['production_user_id']] .="<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";
            }
        }
        foreach ($userData as $userId => $content){
            $data = [
                'bodyContent' => $content,
                'host'        => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            ];
            $this->bindUserSend($userId,$data,'remindEditOrderPredictTime');
        }
        Log::get('system', 'system')->info('--------------------- 消息通知:remindEditOrderPredictTime;结束 ---------------------');
    }

    /**
     * 生产订单上线时间逾期通知
     * @return void
     */
    public function overPredictOnlineTimeNotice()
    {
        Log::get('system', 'system')->info('--------------------- 消息通知:overPredictOnlineTimeNotice;开始 ---------------------');

        //获取已填写预计上线时间但未处理首件的订单,首件上传完会更新actual_online_time，以此作判断
        $field = [
            'production_order.*',
            'production_order_info.test_user_id',
            'production_order_info.production_user_id',
        ];
        $list = ProductionOrderModel::query()->leftJoin('production_order_info', 'production_order_info.production_order_id', '=', 'production_order.id')
//            ->leftJoin('work_status', 'work_status.id', '=', 'production_order_info.work_status_id')
            ->where('production_order_info.predict_online_time', '<', date('Y-m-d'))
            ->whereNull('production_order_info.actual_online_time')
            ->select($field)
            ->get();
        $list = $list ? $list->toArray() : [];
        $bodyContent = '';
        $userData = [];
        Log::get('system', 'system')->info('--------------------- 消息通知:overPredictOnlineTimeNotice;获取数据 ---------------------');
        //发送给创建人，负责人，生产主管
        foreach ($list as $item) {
            if(!empty($item['production_user_id'])){
                if(!isset($userData[$item['production_user_id']])){
                    $userData[$item['production_user_id']] = "<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";
                }else{
                    $userData[$item['production_user_id']] .="<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";
                }
            }
            //防止两个用户是同一个人导致数据重复
            if($item['production_user_id'] == $item['test_user_id']) continue;
            if(!empty($item['test_user_id'])){
                if(!isset($userData[$item['test_user_id']])){
                    $userData[$item['test_user_id']] = "<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";;
                }else{
                    $userData[$item['test_user_id']] .="<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td><td>{$item['created_at']}</td></tr>";
                }
            }
        }
        foreach ($userData as $userId => $content){
            $data = [
                'bodyContent' => $content,
                'host'        => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            ];
            $this->bindUserSend($userId,$data,'overPredictOnlineTimeNotice');
        }
        Log::get('system', 'system')->info('--------------------- 消息通知:overPredictOnlineTimeNotice;结束 ---------------------');

    }

    /**
     * 获取用户然后推送
     * @param $userId
     * @param $params
     * @param $method
     * @return false|void
     */
    public function bindUserSend($userId, $params, $method)
    {
        if (empty($userId) || empty($method)) return false;
        if (is_array($userId)) {
            $userList = UserModel::query()->whereIn('id', $userId)->pluck('name', 'id')->toArray();
            Log::get('system', 'system')->info('--------------------- 发送用户' . implode(',', $userList) . '---------------------');
            foreach ($userList as $k => $v) {
                $params['username'] = $v;
                $this->handleNotice($method, $k, $params);
            }
        } else {
            $params['username'] = UserModel::query()->where('id', $userId)->value('name') ?: '';
            Log::get('system', 'system')->info('--------------------- 发送用户' . $params['username'] . '---------------------');
            $this->handleNotice($method, $userId, $params);
        }
    }

    /**
     * 生产备注通知
     * @param $productionOrderId
     * @param $userIds
     * @return void
     */
    public function sendProductionOuthelpRecord($productionOrderId, $userIds) : bool
    {
        Log::get('system', 'system')->info('--------------------- [生产备注通知]准备执行 ---------------------');
        $userList = UserModel::query()->select(['name', 'id'])->whereIn('id', $userIds)->where('status', 1)->get();
        if (!$userList) {
            Log::get('system', 'system')->info('[生产备注通知]没有可发送的用户执行结束');
            return false;
        }
        $userList = $userList->toArray();
        $productOverview = make(ProductionOrderService::class)->getOverView($productionOrderId);

        $outhelpRow = make(ProductionOuthelpRecordService::class)->getLastOverView($productionOrderId);
        if (empty($outhelpRow)) {
            Log::get('system', 'system')->info('[生产备注通知]没有可发送的备注执行结束');
            throw new AppException(StatusCode::ERR_SERVER, '[生产备注通知]没有可发送的备注');
        }
        $outhelpRow['send_username'] = \App\Model\TchipBi\UserModel::query()->where('id', $outhelpRow['user_id'])->value('name');
        foreach ($userList as $user) {
            $outhelpRow['host']     = biFrontendHost();
            $outhelpRow['username'] = $user['name'];
            $outhelpRow['code'] = $productOverview['code'];
            $outhelpRow['product_name'] = $productOverview['product_name'];
            $outhelpRow['product_spec'] = $productOverview['product_spec'];
            $outhelpRow['product_code'] = $productOverview['product_code'];
            $outhelpRow['woqty'] = $productOverview['woqty'];
            $this->handleNotice('sendProductionOuthelpRecord', $user['id'], $outhelpRow);
        }
        Log::get('system', 'system')->info('--------------------- [生产备注通知]执行完成 ---------------------');
        return true;
    }

    /**
     *
     * @param $values
     * @return false|void
     */
    public function sendOrderMessage($values)
    {
        // 原始查询参数
        $queryParams = [
            'pageNo' => 1,
            'pageSize' => 9999999,
            'filter' => [
                'id' => implode(',', $values['ordersId']),
                'approve_status' => '',
                'work_status_id' => '',
                'keywords' => ''
            ],
            'op' => [
                'id' => 'IN',
                'approve_status' => 'IN',
                'work_status_id' => 'IN'
            ],
            'sort' => 'id',
            'order' => 'DESC'
        ];

        // 转换参数以符合 getList 方法
        $filter = [
            'production_order.id' => $queryParams['filter']['id'],
        ];

        $op = [
            'production_order.id' => $queryParams['op']['id'],

        ];
        $sort = $queryParams['sort'];
        $order = $queryParams['order'];
        $limit = $queryParams['pageSize'];
        //$pageNo = $queryParams['pageNo'];

        $productionOrderService = make(\App\Core\Services\ProductionOrder\ProductionOrderService::class);

        // 调用 getList 方法
        $result = $productionOrderService->getList($filter, $op, $sort, $order, $limit);

        //$userIds = make(\App\Core\Services\UserService::class)->biUserIdByThirdUserId($values['notification_member']) ;
        $userIds = $values['notification_member'];
        $row = '';
        foreach ($result['data'] as $item) {
            $code = $item['code'] ?? '';
            $product_code = $item['product_code'] ?? '';
            $product_name = $item['product_name'] ?? '';
            $woqty = $item['woqty'] ?? '';
            $ddr_name = $item['ddr_name'] ?? '';
            $emmc_name = $item['emmc_name'] ?? '';
            $pcb_name = $item['pcb_name'] ?? '';

            $row .= "<tr>
                <td>{$code}</td>
                <td>{$product_code}</td>
                <td>{$product_name}</td>
                <td>{$woqty}</td>
                <td>{$ddr_name}</td>
                <td>{$emmc_name}</td>
                <td>{$pcb_name}</td>
            </tr>";
        }


        foreach ($userIds as $userId){
            $data = [
                'sender' => UserModel::query()->find( $this->auth->user()->getId())->name,
                'bodyContent' => $row,
                'host'        => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            ];
            //$this->bindUserSend(232, $data, 'sendOrderMessage');
            $this->bindUserSend($userId, $data, 'sendOrderMessage');
        }
    }

    //生产订单同步通知
    public function syncProductionOrderSend($data)
    {
        $content = "";
        foreach ($data as $item) {
            $content .= "<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['woqty']}</td><td>{$item['delivery_date']}</td></tr>";
        }
        $data = [
            'bodyContent' => $content,
            'host'        => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $productionUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::PRODUCTION_ROLE);
        $productionUserArr = array_column($productionUserArr, 'id');
        $this->bindUserSend($productionUserArr, $data, 'syncProductionOrderSend');
    }

    //组装订单同步通知
    public function syncAssembleOrderSend($userId,$data)
    {
        $content = "";
        foreach ($data as $item) {
            $content .= "<tr><td>{$item['code']}</td><td>{$item['product_name']}</td><td>{$item['product_code']}</td><td>{$item['num']}</td><td>{$item['order_date']}</td></tr>";
        }
        $data = [
            'bodyContent' => $content,
            'host'        => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $this->bindUserSend($userId, $data, 'syncAssembleOrderSend');
    }

    /**
     * 组装订单信息完善通知
     * @param $userId
     * @param $data
     * @return void
     */
    public function commonAssembleOrderNotice($userId, $params)
    {
        $this->bindUserSend($userId, $params, 'commonAssembleOrderNotice');
    }


    /**
     * 组装订单文件驳回通知
     * @param $userId
     * @param $assembleOrderId
     * @param $data
     * @return void
     */
    public function rejectAssembleOrder($userId,$assembleOrderId,$data)
    {
        $order = AssembleOrderModel::find($assembleOrderId);
        $params = [
            'notice_msg' =>$data['notice_msg']??'',
            'code'         => $order['code'] ?? '',
            'product_name' => $order['product_name'] ?? '',
            'num'        => $order['num'] ?? 0,
            'product_code' => $order['product_code'] ?? '',
            'assemble_address' => $order['assemble_address'] ?? '',
            'file_name'   => $data['file_name'] ?? '',
            //            'reject_reason'   => $data['reject_reason'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id' => $order['id'] ?? 0
        ];
        $this->bindUserSend($userId, $params, 'rejectAssembleOrder');
    }

    //旧版产品&项目进度通知 （已经拆分为ProductProgressNotice和ProjectProgressNotice，在ProjectsProgressService文件中判断调用）
    // public function projectProgress($progressId)
    // {
    //     // TODO 原子性操作问题因为异步任务问题，在这里执行的时候上轮数据有可能提交未完成导致查询可能出来false，需要重复执行
    //     $c = 0;
    //     while ($c < 5) {
    //         $progress = \App\Model\Redmine\ProjectsProgressModel::query()->with(['project', 'projectExt'])->find($progressId);
    //         if ($progress) {
    //             $c = 10;
    //             break;
    //         } else {
    //             sleep(1);
    //             $c++;
    //         }
    //     }
    //     if (!$progress || empty($progress->description)) return false;
    //     $progress = $progress->toArray();

    //     $redmineUserIds = array_merge($progress['mail_user'], $progress['mail_user']);
    //     if (!$redmineUserIds) return false;

    //     $progressType = CategoryModel::query()->where('keywords', $progress['type'])->where('type', 'project_progress_type')->first();


    //     $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
    //     $projectTypeText = '项目';
    //     if (!empty($progress['project_ext']['project_type']) && $progress['project_ext']['project_type'] == ProjectCode::PROJECT_TYPE_PROD) {
    //         $activeName = 'workProgress';
    //         if ($progress['type'] == 10) {
    //             $activeName = 'noticePCN';
    //         }
    //         $host .= "/#/project/productDetailsIndex?product_id={$progress['project_id']}&active_name={$activeName}&skip={$progress['id']}";
    //         $projectTypeText = '产品';
    //     } else {
    //         $host .= "/#/project/ProjectProgress?project_id={$progress['project_id']}&skip={$progress['id']}";
    //     }

    //     $userService = make(\App\Core\Services\UserService::class);
    //     $users = $userService->biUserIdByThirdUserId($redmineUserIds);
    //     $users = array_unique($users);
    //     foreach ($users as $user) {
    //         $params = [
    //             'project_name' => $progress['project']['name'],
    //             'project_type_text' => $projectTypeText,
    //             'progress_type_text' => $progressType['name'] ?? '',
    //             'notes' => $progress['description_html'] ?? ($progress['description'] ?? ''),
    //             'host' => $host,
    //         ];
    //         $this->handleNotice('projectProgress', $user, $params);
    //     }
    // }

    /**
     * 产品流程节点状态通知
     * @param $userId
     * @param $assembleOrderId
     * @param $data
     * @return void
     */
    public function productFlowNodeStatusNotice($userId,$data)
    {
        $params = [
            'notice_msg' =>$data['message'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'product_id' => $data['product_id'] ?? 0,
            'product_name' => $data['product_name'] ?? '',
        ];
        $this->bindUserSend($userId, $params, 'productFlowNodeStatusNotice');
    }

    /**
     * 产品流程节点 临期&逾期 期限通知
     * @param $userId
     * @param $assembleOrderId
     * @param $data
     * @return void
     */
    public function productFlowNodeDeadlineNotice(int $userId = 232, array $data = [])
    {
        $params = [
            'notice_msg' =>$data['text'] ?? '',
            'host'         => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];
        $this->bindUserSend($userId, $params, 'productFlowNodeDeadlineNotice');
    }

    /**
     * 获取所有消息通知（包含手动选择、部门、角色的用户ID）
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return array
     */
    public function getListNew(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $data = $query->orderBy($sort, $order)->paginate($limit);

        if (!$data->isEmpty()) {
            foreach ($data as $notice) {
                // 获取所有目标用户ID（包括手动选择、部门和角色）
                $targetUserIds = $this->getTargetUserIds($notice->id);
                $targetUserIds = array_values(array_filter(array_unique($targetUserIds)));
                $notice->all_users = $targetUserIds;
            }
        }
    
        return $data;
    }

    /**
     * 事项创建通知
     * @param int $userId
     * @param array $data
     * @return void
     */
    public function issueCreated(int $userId, array $data = [])
    {
        $params = [
            'username' => $data['username'] ?? '',
            'issue_id' => $data['issue_id'] ?? 0,
            'issue_subject' => $data['issue_subject'] ?? '',
            'issue_description' => $data['issue_description'] ?? '',
            'tracker_name' => $data['tracker_name'] ?? '',
            'status_name' => $data['status_name'] ?? '',
            'priority_name' => $data['priority_name'] ?? '',
            'author_name' => $data['author_name'] ?? '',
            'assigned_to_name' => $data['assigned_to_name'] ?? '',
            'project_name' => $data['project_name'] ?? '',
            'project_id' => $data['project_id'] ?? 0,
            'issue_url' => $data['issue_url'] ?? '',
            'project_url' => $data['project_url'] ?? '',
            'settings_url' => $data['settings_url'] ?? '',
            'created_on' => $data['created_on'] ?? '',
            'host' => $data['host'] ?? env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            // 新增邮件通知专用变量
            'operator_name' => $data['operator_name'] ?? '',
            'operation_type' => $data['operation_type'] ?? '',
            'email_title' => $data['email_title'] ?? '',
            'due_date' => $data['due_date'] ?? '',
        ];
        $this->bindUserSend($userId, $params, 'issueCreated');
    }

    /**
     * 文档审核拒绝的通知
     * @param int $userId
     * @param array $data
     * @return void
     */
    public function wikiDocumentReject(int $userId, array $data = [])
    {
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');

        // 获取用户信息
        $user = UserModel::query()->where('id', $userId)->first();
        $applicantName = $user ? $user->name : '';

        // 获取操作人信息
        $operatorName = '';
        if (!empty($data['operator_id'])) {
            $operator = UserModel::query()->where('id', $data['operator_id'])->first();
            $operatorName = $operator ? $operator->name : '';
        }

        // 构建详情页面链接
        $docInfo = \App\Model\TchipBi\WikiDocumentModel::query()->where('doc_id', $data['doc_id'])->first();
        $detailUrl = $host . '/#/Oa/Wiki/Detail?space_id=' . $docInfo->space_id . '&doc_id=' . $docInfo->doc_id;

        $params = [
            'action_text' => '文档审核拒绝',
            'doc_id' => $data['doc_id'] ?? '',
            'applicant_name' => $applicantName,
            'doc_title' => $data['title'] ?? '',
            'status_type' => '审核状态',
            'reject_info' => $data['reason'] ?? '',
            'action_info' => '您的文档审核被拒绝',
            'detail_url' => $detailUrl ?? '',
            'host' => $host,
            'operator_name' => $operatorName,
            'created_on' => \Carbon\Carbon::now()->format('Y-m-d H:i:s'),
        ];

        $this->bindUserSend($userId, $params, 'wikiDocumentReject');
    }

}