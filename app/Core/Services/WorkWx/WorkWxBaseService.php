<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午8:02
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Core\Utils\Weworkapi\WXBizMsgCrypt;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use App\Model\User\User;
use App\Model\User\UserDepartment;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Guzzle\ClientFactory;

class WorkWxBaseService extends BusinessService
{

    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    /**
     * @Inject()
     * @var UserModel
     */
    protected $userModel;

    /**
     * @Inject()
     * @var UserDepartment
     */
    protected $userDepartmentModel;

    protected $defaultExpires = 3600;

    public function __construct(ClientFactory $clientFactory)
    {
        $options = [
            'base_uri' => 'https://qyapi.weixin.qq.com/cgi-bin/'
        ];
        $this->clientFactory = $clientFactory->create($options);
    }

    protected function sendRequest($uri, $options, $method = 'get')
    {
        Log::get()->info('企业微信接口请求', $options);
        try {
            $response = $this->clientFactory->request($method, $uri, $options);
            $responseArr = json_decode($response->getBody()->getContents(), true);
            if($responseArr['errcode'] !== 0){
                if($responseArr['errcode'] === 40014){
                    delCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN);
                    delCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN);
                }
                throw new AppException(StatusCode::ERR_SERVER, json_encode($responseArr));
            }
            return $responseArr;
        } catch (GuzzleException $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    protected function getTicket()
    {

        $response = $this->sendRequest('get_jsapi_ticket', [
            'query'=>[
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
                'type' => 'agent_config'
            ]
        ]);
        return $response['ticket'] ?? '';
    }

    public function getContactToken()
    {
        $response = $this->sendRequest('gettoken', [
            'query'=>[
                'corpid' => env('CORPID'),
                'corpsecret' => env('CONTACTSAPI_SECRET')
            ]
        ]);
        setCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN, $response['access_token'], $response['expires_in'] ?? $this->defaultExpires);
        return $response;
    }

    /**
     * 获取不同场景下的token
     * @param $secret
     * @param $redisKey
     * @return mixed
     */
    public function getToken($secret, $redisKey)
    {
        $response = $this->sendRequest('gettoken', [
            'query'=>[
                'corpid' => env('CORPID'),
                'corpsecret' => $secret
            ]
        ]);
        // Log::get('system', 'system')->info('重新获取accessToken:'.$response['access_token']);
        setCache($redisKey, $response['access_token'], $response['expires_in'] ?? $this->defaultExpires);
        return $response;
    }

    public function verifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr)
    {
        $crypt = $this->getCrypt();
        $sEchoStr = '';
        $errCode = $crypt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
        if ($errCode == 0) {
            // var_dump($sEchoStr);
            return $sEchoStr;
            // 验证URL成功，将sEchoStr返回
            // HttpUtils.SetResponce($sEchoStr);
        }
        throw new AppException(StatusCode::ERR_SERVER, 'CallbackEventController-index-error:'.$errCode);
    }

    public function decryptMsg($sReqMsgSig, $sReqTimeStamp, $sReqNonce, $sReqData)
    {
        $crypt = $this->getCrypt();
        $sMsg = '';
        if (is_array($sReqData)) {
            $xmlObj = new \SimpleXMLElement('<xml></xml>');
            // 使用循环将数据添加到 SimpleXML 对象中
            foreach ($sReqData as $key => $value) {
                $xmlObj->addChild($key, $value);
            }
            $sReqData = $xmlObj->asXML();
        }
        $errCode = $crypt->DecryptMsg($sReqMsgSig, $sReqTimeStamp, $sReqNonce, $sReqData, $sMsg);
        if ($errCode == 0) {
            return json_decode(json_encode(simplexml_load_string($sMsg, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        }
        throw new AppException(StatusCode::ERR_SERVER, 'DecryptMsg fail ERROR:' . $errCode);
    }

    public function getCrypt()
    {
        $corpId = env('CORPID');
        $token = env('WORKWX_TOKEN');
        $encodingAesKey = env('WORKWX_ENCODING_AES_KEY');
        if ($corpId && $token && $encodingAesKey) {
            return make(WXBizMsgCrypt::class, [$token, $encodingAesKey, $corpId]);
        }
        throw new AppException(StatusCode::ERR_SERVER, 'corpId or token or encodingAesKey is empty');
    }
}