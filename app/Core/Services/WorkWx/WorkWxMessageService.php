<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/16 下午5:51
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Utils\Log;
use Hyperf\Context\Context;
use Hyperf\Utils\Coroutine;

class WorkWxMessageService extends WorkWxBaseService
{
    public function sendMarkdownCoroutine(array $data, string $templet, $toUser, $logMessage, $toParty, $toTag)
    {
        $content = messageTempletReplace($data, $templet);
        Coroutine::create(function () use ($content, $toUser, $toParty, $toTag, $data, $logMessage) {
            try {
                $this->sendMarkdown($content, $toUser, $toParty, $toTag);
            } catch (\Exception $e) {
                $logMessage = "通知失败，" . $e->getMessage();
            }
            $handleName = Context::get('crontabTaskHandleName');
            Log::get('message', 'message')->info($handleName . ':' . $logMessage, $data);
        });
    }

    /**
     * 发送文本消息
     * @param $content
     * @param $toUser
     * @param $toParty
     * @param $toTag
     * @return mixed
     */
    public function sendText($content, $toUser, $toParty, $toTag)
    {
        $sendContent = [
            'msgtype' => 'text',
            'text'    => [
                'content' => $content
            ]
        ];
        return $this->send($sendContent, $toUser, $toParty, $toTag);
    }

    /**
     * 发送文本卡片消息
     * @param $title
     * @param $content
     * @param $url
     * @param $toUser
     * @param $toParty
     * @param $toTag
     * @return mixed
     */
    public function sendTextCard($title, $content, $url, $toUser, $toParty, $toTag)
    {
        $sendContent = [
            'msgtype'  => 'textcard',
            'textcard' => [
                'title'       => $title,
                'description' => $content,
                'url'         => $url,
                'btntxt'      => '查看详情'
            ]
        ];
        return $this->send($sendContent, $toUser, $toParty, $toTag);
    }

    /**
     * 发送markdown消息
     * @param $content
     * @param $toUser
     * @param $toParty
     * @param $toTag
     * @return mixed
     */
    public function sendMarkdown($content, $toUser, $toParty, $toTag)
    {
        $sendContent = [
            'msgtype'  => 'markdown',
            'markdown' => [
                'content' => $content
            ]
        ];
        return $this->send($sendContent, $toUser, $toParty, $toTag);
    }

    /**
     * 发送markdown消息
     * @param $content
     * @param $toUser
     * @param $toParty
     * @param $toTag
     * @return mixed
     */
    public function mpnews($content, $toUser, $toParty, $toTag)
    {
        $sendContent = [
            'msgtype' => 'mpnews',
            'mpnews'  => [
                'articles' => [
                    [
                        "title"              => "Title",
                        "thumb_media_id"     => "MEDIA_ID",
                        "author"             => "Author",
                        "content_source_url" => "URL",
                        "content"            => "Content",
                        "digest"             => "Digest description"
                    ]
                ]
            ]
        ];
        return $this->send($sendContent, $toUser, $toParty, $toTag);
    }

    public function templateCard($templateCard, $toUser, $toParty = '', $toTag = '')
    {

        return $this->send($templateCard, $toUser, $toParty, $toTag);

    }


    /**
     * 发送内容
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @param $sendContent
     * @param $toUser
     * @param $toParty
     * @param $toTag
     * @return mixed
     */
    public function send($sendContent, $toUser, $toParty, $toTag)
    {
        $sendContent = array_merge($sendContent, $this->sendBase($toUser, $toParty, $toTag));

        // Log::get('system', 'system')->info('准备发送企微消息token:'.getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN));
        Log::get()->info(json_encode($sendContent));
        Log::get()->info(getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN));
        $result = $this->sendRequest('message/send', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
            ],
            'json'  => $sendContent
        ], 'post');

        return $result;
    }


    /**
     * 发送消息基本结构体
     * @return array
     */
    public function sendBase($toUser, $toParty, $toTag)
    {
        return [
            "touser"                   => $toUser,
            "toparty"                  => $toParty,
            "toTag"                    => $toTag,
            "agentid"                  => env('AGENT_TCHIP_BI_AGENTID'),
            "safe"                     => 0,
            "enable_id_trans"          => 0,
            "enable_duplicate_check"   => 0,
            "duplicate_check_interval" => 1800
        ];
    }
}