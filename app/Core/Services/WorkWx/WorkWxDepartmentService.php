<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午8:04
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;
use App\Constants\StatusCode;
use App\Exception\AppException;
use Hyperf\DbConnection\Db;

/**
 * 部门管理
 */
class WorkWxDepartmentService extends WorkWxBaseService
{
    /**
     * 获取部门列表
     * @return array
     */
    public function syncDepartmentList()
    {
        $items = $this->sendRequest('department/list', [
            'query'=>[
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN),
                'id'           => 1
            ]
        ]);
        Db::beginTransaction();
        try {
            foreach ($items['department'] as $item) {
                $this->userDepartmentModel::query()->updateOrCreate(['id'=>$item['id']],$item);
            }
            Db::commit();
        }catch (\Throwable $e){
            Db::rollBack();
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
        return $items;
    }
}