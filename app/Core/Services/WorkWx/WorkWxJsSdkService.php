<?php

namespace App\Core\Services\WorkWx;


use App\Constants\CommonCode;
use App\Core\Utils\Random;

class WorkWxJsSdkService extends WorkWxBaseService
{
    public function getConfigSignature($url)
    {
        $jsapiTicket = $this->getTicket();
        $nonceStr = Random::alnum();
        $timestamp = time();

        $string = "jsapi_ticket={$jsapiTicket}&noncestr={$nonceStr}&timestamp={$timestamp}&url={$url}";
        $sha1 = sha1($string);
        return [
            'timestamp' => $timestamp,
            'nonceStr' => $nonceStr,
            'signature' => $sha1,
        ];
    }
}