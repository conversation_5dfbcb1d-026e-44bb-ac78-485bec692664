<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/16 下午5:51
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;

/**
 * 应用管理
 */
class WorkWxAgentService extends WorkWxBaseService
{
    /**
     * 设置应用在工作台展示的模版
     * @return array
     */
    public function setWorkbenchTemplate(): array
    {
        $result = $this->sendRequest('agent/set_workbench_template', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
            ],
            'json'  => [
                "agentid"           => env('AGENT_TCHIP_BI_AGENTID'),
                "type"              => "keydata",
                "items"             => [
                    [
                        "key"      => "跟进订单",
                        "data"     => "2",
                        "jump_url" => "",
                        "pagepath" => ""
                    ],
                    [
                        "key"      => "跟进客户",
                        "data"     => "4",
                        "jump_url" => "",
                        "pagepath" => ""
                    ],
                    [
                        "key"      => "月业绩目标",
                        "data"     => "45.17%",
                        "jump_url" => "",
                        "pagepath" => ""
                    ],
                ],
                "replace_user_data" => false
            ]
        ], 'post');

        return $result;
    }

    /**
     * 获取应用在工作台展示的模版
     * @return array
     */
    public function getWorkbenchTemplate(): array
    {
        $result = $this->sendRequest('agent/get_workbench_template', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
            ],
            'json'  => [
                "agentid" => env('AGENT_TCHIP_BI_AGENTID'),
            ]
        ], 'post');

        return $result;
    }

    /**
     * 设置应用在工作台展示的数据
     * @return array
     */
    public function setWorkbenchData(): array
    {
        $result = $this->sendRequest('agent/set_workbench_data', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
            ],
            'json'  => [
                "agentid" => env('AGENT_TCHIP_BI_AGENTID'),
                "userid"  => "foyLin",
                "type"    => "keydata",
                "keydata" => [
                    "items"   => [
                        [
                            "key"      => "跟进订单",
                            "data"     => "2",
                            "jump_url" => "",
                            "pagepath" => ""
                        ],
                        [
                            "key"      => "跟进客户",
                            "data"     => "4",
                            "jump_url" => "",
                            "pagepath" => ""
                        ],
                        [
                            "key"      => "月业绩目标",
                            "data"     => "45.17%",
                            "jump_url" => "",
                            "pagepath" => ""
                        ],
                    ],
                ]

            ]
        ], 'post');

        return $result;
    }
}