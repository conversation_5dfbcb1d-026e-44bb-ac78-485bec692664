<?php

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;
use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\Setting\DepartmentManagermentService;
use App\Core\Services\TchipSale\ErpService;
use App\Core\Utils\Log;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkWxApprovalModel;
use Hyperf\Guzzle\ClientFactory;
use Hyperf\Di\Annotation\Inject;

class WorkWxApprovalService extends WorkWxBaseService
{

    /**
     * @Inject()
     * @var WorkWxApprovalModel
     */
    protected $model;

    public function getOverView($id)
    {
        $row = $this->model::query()->find($id);
        $row = $row ? $row->toArray() : [];
        if ($row) {
            $row['department_name'] = make(DepartmentManagermentService::class)->getDepartmentByUserId($row['applyer_id'], 'name');
            $row['apply_date'] = TimeUtils::getCnDate($row['apply_time_date']);
            if (!empty($row['apply_data']['contents'])) {
                $this->getContentDetails($row);
            }
        }
        return $row;
    }

    /**
     * 根据tamplate_id与sp_no获取详情
     * @return void
     */
    public function getOverViewByTemplateNo($templateId, $spNo)
    {
        $row = $this->model::query()->where('template_id', $templateId)->where('sp_no', $spNo)->first();
        return $row ?: null;
    }



    /**
     * 保存企业微信sys_approval_change调回的数据
     * @param int $id
     * @param array $values
     * @return bool|\Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model|int
     */
    public function doEditWxEvent(int $id, array $values)
    {
        $saveData = [];
        if (!empty($values['SpNo'])) {
            $saveData['sp_no'] = $values['SpNo'];
            $details = $this->getApprovalDetail($values['SpNo']);
            if ($details['info']) {
                $saveData['sp_name'] = $details['info']['sp_name'];
                $saveData['sp_status'] = $details['info']['sp_status'];
                $saveData['template_id'] = $details['info']['template_id'];
                $saveData['apply_time'] = $details['info']['apply_time'];
                $saveData['apply_time_date'] = date('Y-m-d H:i:s', $details['info']['apply_time']);
                // 申请人信息
                if (!empty($details['info']['applyer']['userid'])) {
                    $saveData['applyer'] = $details['info']['applyer'];
                    $user = UserModel::query()->where('workwx_userid', $details['info']['applyer']['userid'])->first();
                    if ($user) {
                        $saveData['applyer_id'] = $user->id;
                        $saveData['applyer_name'] = $user->name;
                        $saveData['applyer_workwx_userid'] = $details['info']['applyer']['userid'];
                    }
                }

                if (!empty($details['info']['sp_record'])) {
                    $saveData['sp_record'] = $details['info']['sp_record'];
                }
                if (!empty($details['info']['notifyer'])) {
                    $saveData['notifyer'] = $details['info']['notifyer'];
                }
                if (!empty($details['info']['apply_data'])) {
                    $saveData['apply_data'] = $details['info']['apply_data'];
                }
                if (!empty($details['info']['comments'])) {
                    $saveData['comments'] = $details['info']['comments'];
                }
                if (!empty($details['info']['process_list'])) {
                    $saveData['process_list'] = $details['info']['process_list'];
                }
            } else {
                if (!empty($values['SpName'])) {
                    $saveData['sp_name'] = $values['SpName'];
                }
                if (!empty($values['SpStatus'])) {
                    $saveData['sp_status'] = $values['SpStatus'];
                }
                if (!empty($values['SpStatus'])) {
                    $saveData['sp_status'] = $values['SpStatus'];
                }
                if (!empty($values['TemplateId'])) {
                    $saveData['template_id'] = $values['TemplateId'];
                }

                if (!empty($values['TemplateId'])) {
                    $saveData['template_id'] = $values['TemplateId'];
                }
                if (!empty($values['TemplateId'])) {
                    $saveData['template_id'] = $values['TemplateId'];
                }
                if (!empty($values['ApplyTime'])) {
                    $saveData['apply_time'] = $values['ApplyTime'];
                    $saveData['apply_time_date'] = date('Y-m-d H:i:s', $values['ApplyTime']);
                }
                if (!empty($values['Applyer'])) {
                    $saveData['applyer'] = $values['Applyer'];
                }
                if (!empty($values['SpRecord'])) {
                    $saveData['sp_record'] = $values['SpRecord'];
                }
                if (!empty($values['Notifyer'])) {
                    $saveData['notifyer'] = $values['Notifyer'];
                }
                if (!empty($values['ProcessList'])) {
                    $saveData['process_list'] = $values['ProcessList'];
                }
                if (!empty($values['Comments'])) {
                    $saveData['comments'] = $values['Comments'];
                }
            }
            if (!empty($values['StatuChangeEvent'])) {
                $saveData['statu_change_event'] = $values['StatuChangeEvent'];
            }
            var_dump($saveData);
            return parent::doEdit($id, $saveData); // TODO: Change the autogenerated stub
        }
        return false;
    }

    public function getContentDetails(&$row)
    {
        foreach ($row['apply_data']['contents'] as $content) {
            switch ($content['id']) {
                // 报销类型
                case 'item-1503317593875':
                    $row['expense_type'] = $content['title'][0]['text'] ?? '';
                    $row['expense_value'] = $content['value']['selector']['options'][0]['value'][0]['text'];
                    break;
                // 报销事由
                case 'item-1503317835288':
                // 出差事由
                case 'item-1497581558567':
                    $row['reason_title'] = $content['title'][0]['text'] ?? '';
                    $row['reason_value'] = $content['value']['text'] ?? '';
                    break;
                // 明细 包括费用类型, 发生时间, 费用金额, 费用说明, 附件
                case 'item-1503317853434':
                    if (!empty($content['value']['children']) && is_array($content['value']['children'])) {
                        foreach ($content['value']['children'] as $child) {
                            if (!empty($child['list'])) {
                                foreach ($child['list'] as $ch) {
                                    switch ($ch['id']) {
                                        // 费用类型
                                        case 'item-1503317870534':
                                            $row['cost_type'] = $ch['value']['selector']['options'][0]['value'][0]['text'] ?? '';
                                            break;
                                        // 发生时间
                                        case 'item-1503317973968':
                                            $row['occur_date'] = $ch['value']['date']['s_timestamp'] ?? '';
                                            if ($row['occur_date']) {
                                                $row['occur_date'] = $ch['value']['date']['type'] == 'day' ? TimeUtils::getCnDate(date('Y-m-d', $row['occur_date'])) : TimeUtils::getCnDate($row['occur_date']);
                                            }
                                            break;
                                        // 费用说明
                                        case 'item-1503318001306':
                                            $row['content_text'] = $ch['value']['text'];
                                            break;

                                    }
                                }
                            }
                        }
                    }
                    $row['total_amount'] = !empty($content['value']['stat_field'][0]['value']) ? sprintf("%.2f", (floor($content['value']['stat_field'][0]['value'] * 100) / 100)) : '0.00';
                    $row['total_amount_upper'] = $row['total_amount'] ? convertToChinese($content['value']['stat_field'][0]['value']) : '';
                    break;
                // 出差地点
                case 'item-1497581575653':
                    $row['location_title'] = $content['title'][0]['text'] ?? '';
                    $row['location_value'] = $content['value']['text'] ?? '';
                    break;
                case 'smart-time':
                    $row['attendance']['begin'] = date('Y年m月d日 H时i分', $content['value']['attendance']['date_range']['new_begin']);
                    $row['attendance']['end'] = date('Y年m月d日 H时i分', $content['value']['attendance']['date_range']['new_end']);
                    $row['attendance']['duration'] = TimeUtils::formatSeconds($content['value']['attendance']['date_range']['new_duration']);
                    break;
            }
        }
    }

    /**
     * 申请领用物品审批(多物品申请)
     * @param $creatorUserid 申请人
     * @param $approverIds 审批人
     * @param $notifyerIds 抄送人
     * @param $use 用途
     * @param $details 数据详情
     * @param $mediaId 企业微信素材ID
     * @param $templateApprover 选择审批人模式，0=使用当前数据参数，1=使用后台默认设置
     * @WorkWxTokenAnnotation(type="approval")
     * @return mixed|void
     * $templateApprover
     */
    public function lendApplyevent($creatorUserid, $approverIds, $notifyerIds, $use, $details, $mediaId = null, $templateApprover = 0)
    {
        $templateId = env('APPROVAL_LEND_TEMPLATEID');
        if ($templateId) {
            // $erpService = make(ErpService::class);
            $list = [];
            foreach ($details as $detail) {
                // $prodName = $erpService->getGoodsByCodes([$detail['prod_code']]);
                $list[]['list'] = [
                    [
                        'control' => "Text",
                        'id'      => "Text-1568881527830",
                        'value'   => [
                            'text' => str_replace(PHP_EOL, '', $detail['prod_name'])
                        ]
                    ],
                    [
                        'control' => "Text",
                        'id'      => "Text-1686817178779",
                        'value'   => [
                            'text' => str_replace(PHP_EOL, '', $detail['prod_spec']) ?? ''
                        ]
                    ],
                    [
                        'control' => "Text",
                        'id'      => "Text-1683797389096",
                        'value'   => [
                            'text' => $detail['prod_code']
                        ]
                    ],
                    [
                        'control' => "Number",
                        'id'      => "Number-1568881536087",
                        'value'   => [
                            'new_number' => $detail['prod_count']
                        ]
                    ],
                ];

            }



            $applyData   = [
                'contents' => [
                    [
                        'control' => "Textarea",
                        'id'      => "item-1494250993171",
                        'value'   => [
                            'text' => $use
                        ]
                    ],
                    [
                        'control' => "Table",
                        'id'      => "Table-1568881464583",
                        'value'   => [
                            'children' => $list
                        ]
                    ],
                    // [
                    //     'control' => "File",
                    //     'id' => "item-1494251026821",
                    //     'files' => [
                    //         'file_id' => $mediaId
                    //     ]
                    // ],
                ]
            ];
            $userName    = UserModel::query()->where('workwx_userid', $creatorUserid)->first();
            $userName    = $userName->name ?? '';
            $summaryList = [
                [
                    'summary_info' => [
                        [
                            'text' => '产品领用',
                            "lang" => "zh_CN",
                        ]
                    ]
                ],
                [
                    'summary_info' => [
                        [
                            'text' => "{$userName} 申请领用产品 {$details[0]['prod_name']} 料号: {$details[0]['prod_code']} ...",
                            "lang" => "zh_CN",
                        ]
                    ]
                ]
            ];

            $data = [
                'creator_userid'        => $creatorUserid,
                'template_id'           => $templateId,
                // 'approver' => [
                //     [
                //         'attr' => 2,
                //         'userid' => $approverIds,
                //     ]
                // ],
                'apply_data'            => $applyData,
                'summary_list'          => $summaryList,
                'use_template_approver' => $templateApprover
            ];

            // 审批模式为0时传入审批人数据
            if ($templateApprover == 0) {
                $data['approver']    = [
                    [
                        'attr'   => 2,
                        'userid' => $approverIds,
                    ]
                ];
                $data['notifyer']    = $notifyerIds;
                $data['notify_type'] = 1;
            }
            $items = $this->sendRequest('oa/applyevent', [
                'query' => [
                    'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
                    'id'           => 1
                ],
                'json'  => $data,

            ], 'post');
            return $items;
        } else {
            throw new AppException(StatusCode::ERR_SERVER, __('tchipbi.Workwx_template_fault'));
        }
    }

    /**
     * 申请领用物品审批(单物品申请)
     * @param $creatorUserid 申请人
     * @param $approverIds 审批人
     * @param $notifyerIds 抄送人
     * @param $use 用途
     * @param $prodName 物品名称
     * @param $number 数量
     * @WorkWxTokenAnnotation(type="approval")
     * @return mixed|void
     */
    public function lendApplyevent1($creatorUserid, $approverIds, $notifyerIds, $use = null, $detail, $mediaId = null)
    {
        $templateId = env('APPROVAL_LEND_TEMPLATEID');
        if ($templateId) {
            // $list = [];
            // foreach ($details as $detail) {
            //     $list[]['list'] = [
            //         [
            //             'control' => "Text",
            //             'id' => "Text-1568881527830",
            //             'value' => [
            //                 'text' => $detail['prod_name']
            //             ]
            //         ],
            //         [
            //             'control' => "Text",
            //             'id' => "Text-1683797389096",
            //             'value' => [
            //                 'text' => $detail['material']
            //             ]
            //         ],
            //         [
            //             'control' => "Number",
            //             'id' => "Number-1568881536087",
            //             'value' => [
            //                 'new_number' => $detail['num']
            //             ]
            //         ],
            //     ];
            // }
            $applyData = [
                'contents' => [
                    [
                        'control' => 'Table',
                        "id"      => "Table-1568881464583",
                        'value'   => [
                            'children' => [
                                [
                                    'list' => [
                                        [
                                            'control' => "Text",
                                            'id'      => "Text-1568881527830",
                                            'value'   => [
                                                'text' => $detail['prod_name'],
                                            ]
                                        ],
                                        [
                                            'control' => "Text",
                                            'id'      => "Text-1683797389096",
                                            'value'   => [
                                                'text' => $detail['prod_code']
                                            ]
                                        ],
                                        [
                                            'control' => "Number",
                                            'id'      => "Number-1568881536087",
                                            'value'   => [
                                                'new_number' => $detail['prod_count']
                                            ]
                                        ],
                                        [
                                            "control" => "Date",
                                            "id"      => "Date-1685691735296",
                                            "value"   => [
                                                'date' => [
                                                    "type"        => "day",
                                                    "s_timestamp" => !empty($detail['return_date']) ? strtotime($detail['return_date']) : '',
                                                ]
                                            ],
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    [
                        'control' => 'Textarea',
                        "id"      => "item-1494250993171",
                        'value'   => [
                            'text' => $use ?? (!empty($detail['remark']) ? $detail['remark'] : '')
                        ]
                    ]
                ]
            ];
            Log::get()->info('apply_data数据', $applyData);
            $userName    = UserModel::query()->where('workwx_userid', $creatorUserid)->first();
            $userName    = $userName->name ?? '';
            $summaryList = [
                [
                    'summary_info' => [
                        [
                            'text' => "产品：{$detail['prod_name']}",
                            "lang" => "zh_CN",
                        ]
                    ]
                ],
                [
                    'summary_info' => [
                        [
                            'text' => "数量：{$detail['prod_count']} ",
                            "lang" => "zh_CN",
                        ]
                    ]
                ]
            ];
            $data        = [
                'creator_userid' => $creatorUserid,
                'template_id'    => $templateId,
                'approver'       => [
                    [
                        'attr'   => 2,
                        'userid' => $approverIds,
                    ]
                ],
                'apply_data'     => $applyData,
                'summary_list'   => $summaryList,
            ];
            if ($notifyerIds) {
                $data['notifyer']    = $notifyerIds;
                $data['notify_type'] = 1;
            }
            $items = $this->sendRequest('oa/applyevent', [
                'query' => [
                    'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
                    'id'           => 1
                ],
                'json'  => $data,

            ], 'post');
            return $items;
        } else {
            throw new AppException(StatusCode::ERR_SERVER, __('tchipbi.Workwx_template_fault'));
        }
    }

    /**
     * 获取审批申请详情
     * @param $spNo
     * @WorkWxTokenAnnotation(type="approval")
     * @return mixed
     */
    public function getApprovalDetail($spNo)
    {
        $items = $this->sendRequest('oa/getapprovaldetail', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
                'id'           => 1
            ],
            'json'  => [
                'sp_no' => $spNo
            ],
        ], 'post');
        return $items;
    }

    /**
     * 申请借用审批
     * @param $creatorUserid
     * @param $approverIds
     * @param $notifyerIds
     * @param $prodName
     * @param $material
     * @param $number
     * @param $date
     * @WorkWxTokenAnnotation(type="approval")
     * @return mixed|void
     */
    // public function lendApplyevent($creatorUserid, $approverIds, $notifyerIds, $prodName, $material, $number, $date)
    // {
    //     // {"errcode":0,"errmsg":"ok","template_id":"3WLJWdGZsCpZpkxkJYbF5ADRPsXWU71fSQBjVm7m"}
    //     $templateId = env('APPROVAL_LEND_TEMPLATEID');
    //     if ($templateId) {
    //         $applyData = [
    //             'contents' => [
    //                 [
    //                     'control' => "Text",
    //                     'id' => "Text-01",
    //                     'value' => [
    //                         'text' => $prodName
    //                     ]
    //                 ],
    //                 [
    //                     'control' => "Text",
    //                     'id' => "Text-02",
    //                     'value' => [
    //                         'text' => $material
    //                     ]
    //                 ],
    //                 [
    //                     'control' => "Number",
    //                     'id' => "Number-01",
    //                     'value' => [
    //                         'new_number' => $number
    //                     ]
    //                 ],
    //                 [
    //                     'control' => "Date",
    //                     'id' => "Date-01",
    //                     'value' => [
    //                         'date' => [
    //                             'type'=> 'hour',
    //                             's_timestamp' => strtotime($date)
    //                         ]
    //                     ]
    //                 ],
    //             ]
    //         ];
    //
    //         $userName = UserModel::query()->where('workwx_userid', $creatorUserid)->first();
    //         $userName = $userName->name ?? '';
    //         $summaryList = [
    //             [
    //                 'summary_info' => [
    //                     [
    //                         'text' => '产品借用',
    //                         "lang" => "zh_CN",
    //                     ]
    //                 ]
    //             ],
    //             [
    //                 'summary_info' => [
    //                     [
    //                         'text' => "{$userName} 申请借用产品 {$prodName} 料号: {$material},数量为:{$number}",
    //                         "lang" => "zh_CN",
    //                     ]
    //                 ]
    //             ]
    //         ];
    //         $data = [
    //             'creator_userid' => $creatorUserid,
    //             'template_id' => $templateId,
    //             'approver' => [
    //                 [
    //                     'attr' => 2,
    //                     'userid' => $approverIds,
    //                 ]
    //             ],
    //             'apply_data' => $applyData,
    //             'summary_list' => $summaryList,
    //         ];
    //         if ($notifyerIds) {
    //             $data['notifyer'] = $notifyerIds;
    //             $data['notify_type'] = 1;
    //         }
    //         $items = $this->sendRequest('oa/applyevent', [
    //             'query'=>[
    //                 'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
    //                 'id'           => 1
    //             ],
    //             'json' => $data,
    //
    //         ], 'post');
    //         return $items;
    //     } else {
    //         throw new AppException(StatusCode::ERR_SERVER, __('tchipbi.Workwx_template_fault'));
    //     }
    // }

    /**
     * 创建借用模板 type="approval" 企业微信审批应用配置
     * @WorkWxTokenAnnotation(type="approval")
     * @return void
     */
    public function applyCreateTemplate()
    {
        $templateName = '产品借用';
        $lang         = 'zh_CN';
        $data         = [
            'template_name'    => [
                [
                    'text' => $templateName,
                    'lang' => $lang,
                ]
            ],
            'template_content' => [
                'controls' => [
                    [
                        'property' => [
                            'control'     => 'Text',
                            'id'          => 'Text-01',
                            'title'       => [
                                [
                                    'text' => '产品名称',
                                    'lang' => $lang,
                                ]
                            ],
                            'placeholder' => [
                                [
                                    'text' => '填写产品名称',
                                    'lang' => $lang,
                                ]
                            ],
                            'require'     => 0,
                            'un_print'    => 0,
                        ],
                        // 'config' => []
                    ],
                    [
                        'property' => [
                            'control'     => 'Text',
                            'id'          => 'Text-02',
                            'title'       => [
                                [
                                    'text' => '产品料号',
                                    'lang' => $lang,
                                ]
                            ],
                            'placeholder' => [
                                [
                                    'text' => '填写产品料号',
                                    'lang' => $lang,
                                ]
                            ],
                            'require'     => 1,
                            'un_print'    => 0,
                        ],
                        // 'config' => []
                    ],
                    [
                        'property' => [
                            'control'     => 'Number',
                            'id'          => 'Number-01',
                            'title'       => [
                                [
                                    'text' => '借用数量',
                                    'lang' => $lang,
                                ]
                            ],
                            'placeholder' => [
                                [
                                    'text' => '填写借用数量',
                                    'lang' => $lang,
                                ]
                            ],
                            'require'     => 1,
                            'un_print'    => 0,
                        ],
                    ],
                    [
                        'property' => [
                            'control'     => 'Date',
                            'id'          => 'Date-01',
                            'title'       => [
                                [
                                    'text' => '借用日期',
                                    'lang' => $lang,
                                ]
                            ],
                            'placeholder' => [
                                [
                                    'text' => '借用日期',
                                    'lang' => $lang,
                                ]
                            ],
                            'require'     => 1,
                            'un_print'    => 0,
                        ],
                        'config'   => [
                            'date' => [
                                'type' => 'hour'
                            ]
                        ]
                    ],
                ]
            ]
        ];
        return $this->createTemplate($data);
    }

    public function createTemplate($data)
    {
        $data['access_token'] = getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN);
        $items                = $this->sendRequest('oa/approval/create_template', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
                // 'id'           => 1
            ],
            'json'  => $data,
        ], 'post');
        return $items;
    }

    /**
     * 获取模块详情
     * @param $templateId
     * @WorkWxTokenAnnotation(type="approval")
     * @return mixed
     */
    public function getTemplateDetail($templateId = null)
    {
        $templateId = $templateId ?: env('APPROVAL_LEND_TEMPLATEID');
        $data       = [
            'template_id' => $templateId
        ];
        $items      = $this->sendRequest('oa/gettemplatedetail', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN),
                // 'id'           => 1
            ],
            'json'  => $data,
        ], 'post');
        return $items;
    }
}




// {
//     "errcode": 0,
//     "errmsg": "ok",
//     "template_names": [
//         {
//             "text": "物品领用",
//             "lang": "zh_CN"
//         },
//         {
//             "text": "Requisition",
//             "lang": "en"
//         }
//     ],
//     "template_content": {
//     "controls": [
//             {
//                 "property": {
//                 "control": "Textarea",
//                     "id": "item-1494250993171",
//                     "title": [
//                         {
//                             "text": "用途",
//                             "lang": "zh_CN"
//                         },
//                         {
//                             "text": "Purpose",
//                             "lang": "en"
//                         }
//                     ],
//                     "placeholder": [
//                         {
//                             "text": "",
//                             "lang": "zh_CN"
//                         }
//                     ],
//                     "require": 1,
//                     "un_print": 0,
//                     "inner_id": "",
//                     "un_replace": 0,
//                     "display": 1
//                 }
//             },
//             {
//                 "property": {
//                 "control": "Table",
//                     "id": "Table-1568881464583",
//                     "title": [
//                         {
//                             "text": "明细",
//                             "lang": "zh_CN"
//                         },
//                         {
//                             "text": "Details",
//                             "lang": "en"
//                         }
//                     ],
//                     "placeholder": [
//                         {
//                             "text": "",
//                             "lang": "zh_CN"
//                         }
//                     ],
//                     "require": 0,
//                     "un_print": 0,
//                     "inner_id": "",
//                     "un_replace": 0,
//                     "display": 1
//                 },
//                 "config": {
//                 "table": {
//                     "children": [
//                             {
//                                 "property": {
//                                 "control": "Text",
//                                     "id": "Text-1568881527830",
//                                     "title": [
//                                         {
//                                             "text": "物品名称",
//                                             "lang": "zh_CN"
//                                         },
//                                         {
//                                             "text": "Item",
//                                             "lang": "en"
//                                         }
//                                     ],
//                                     "placeholder": [
//                                         {
//                                             "text": "",
//                                             "lang": "zh_CN"
//                                         }
//                                     ],
//                                     "require": 1,
//                                     "un_print": 0,
//                                     "un_replace": 0,
//                                     "display": 1
//                                 }
//                             },
//                             {
//                                 "property": {
//                                 "control": "Text",
//                                     "id": "Text-1683797389096",
//                                     "title": [
//                                         {
//                                             "text": "物品料号",
//                                             "lang": "zh_CN"
//                                         }
//                                     ],
//                                     "placeholder": [
//                                         {
//                                             "text": "",
//                                             "lang": "zh_CN"
//                                         }
//                                     ],
//                                     "require": 0,
//                                     "un_print": 0
//                                 }
//                             },
//                             {
//                                 "property": {
//                                 "control": "Number",
//                                     "id": "Number-1568881536087",
//                                     "title": [
//                                         {
//                                             "text": "物品数量",
//                                             "lang": "zh_CN"
//                                         },
//                                         {
//                                             "text": "Quantity",
//                                             "lang": "en"
//                                         }
//                                     ],
//                                     "placeholder": [
//                                         {
//                                             "text": "",
//                                             "lang": "zh_CN"
//                                         }
//                                     ],
//                                     "require": 1,
//                                     "un_print": 0,
//                                     "un_replace": 0,
//                                     "display": 1
//                                 }
//                             }
//                         ],
//                         "stat_field": [],
//                         "sum_field": [],
//                         "print_format": 0
//                     }
//                 }
//             },
//             {
//                 "property": {
//                 "control": "File",
//                     "id": "item-1494251026821",
//                     "title": [
//                         {
//                             "text": "附件",
//                             "lang": "zh_CN"
//                         },
//                         {
//                             "text": "Attachment",
//                             "lang": "en"
//                         }
//                     ],
//                     "placeholder": [
//                         {
//                             "text": "",
//                             "lang": "zh_CN"
//                         }
//                     ],
//                     "require": 0,
//                     "un_print": 0,
//                     "inner_id": "",
//                     "un_replace": 0,
//                     "display": 1
//                 }
//             }
//         ]
//     }
// }