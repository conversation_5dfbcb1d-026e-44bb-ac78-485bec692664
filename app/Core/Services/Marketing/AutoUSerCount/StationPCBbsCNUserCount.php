<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/5 下午4:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing\AutoUSerCount;

use App\Constants\DataBaseCode;
use App\Model\StationPCBbs\CommonMember;
use Hyperf\Di\Annotation\Inject;

/**
 * 中文论坛用户数
 */
class StationPCBbsCNUserCount implements AutoUserCountInterface
{

    /**
     * @Inject()
     * @var CommonMember
     */
    protected $model;

    public function getUserCount($channelId, $spaceUrl):int
    {
        return $this->model::query()->count();
    }
}