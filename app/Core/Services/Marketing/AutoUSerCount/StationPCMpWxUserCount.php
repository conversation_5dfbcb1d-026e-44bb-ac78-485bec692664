<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/6 下午5:14
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing\AutoUSerCount;

use App\Constants\CommonCode;
use App\Core\Services\MpWx\MpWxUserService;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;

/**
 * 采集StationPC公众号用户数
 */
class StationPCMpWxUserCount implements AutoUserCountInterface
{
    /**
     * @Inject()
     * @var MpWxUserService
     */
    protected $mpWxUserService;

    public function getUserCount($channelId, $spaceUrl): int
    {
        $beginDate = Carbon::yesterday()->format('Y-m-d');
        $endDate = Carbon::yesterday()->format('Y-m-d');
        $result = $this->mpWxUserService->getUserCumulate(CommonCode::BRAND_STATIONPC, $beginDate, $endDate);
        return $result['list'][0]['cumulate_user'];
    }
}