<?php
namespace App\Core\Services\MacAddress;

use App\Constants\ProductionCode;
use App\Constants\StatusCode;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use App\Core\Services\Production\Code\CodeService;
use App\Core\Services\Setting\CategoryService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\MacAddressModel;
use App\Model\TchipBi\OrderMacSnBatchModel;
use App\Model\TchipBi\ProductionOrderModel;
use App\Model\TchipBi\UserModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

class MacAddressService extends CodeService
{
    /**
     * @Inject()
     * @var MacAddressModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    protected $codeField = 'mac_address';
    protected $sortField = 'mac_decimal';

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = null;
        if (!empty($filter['keywords'])) {
            $keywords = $filter['keywords'];
            unset($filter['keywords']);
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('mac_address', 'like', "%{$keywords}%")
                    ->orWhere('used_no', 'like', "%{$keywords}%")
                    ->orWhere('used_product', 'like', "%{$keywords}%")
                    ->orWhere('used_product_code', 'like', "%{$keywords}%")
                    ->orWhere('used_client', 'like', "%{$keywords}%");
                return $query;
            });
        }
        $paginate = $query->orderBy($sort, $order)->orderBy('mac_decimal', 'desc')->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        if (!empty($paginate['data'])) {
            $userIds = array_column($paginate['data'], 'used_user_id');
            $users   = UserModel::query()->select(['id', 'name'])->whereIn('id', $userIds)->get();
            $users   = $users ? array_column($users->toArray(), null, 'id') : [];
            $types   = make(CategoryService::class)->lists('mac_used_type', ['type' => 'mac_used_type', 'status' => 1]);
            $types   = $types ? array_column($types->toArray(), null, 'keywords') : [];

            //查出对应的sn关联
            $macIds = array_column($paginate['data'], 'id');
            $macRelation = OrderMacSnBatchModel::query()->whereIn('mac_id', $macIds)
                ->leftJoin('sn_code', 'sn_code.id', '=', 'order_mac_sn_batch.sn_id')
                ->select([
                    'order_mac_sn_batch.batch_key',
                    'order_mac_sn_batch.mac_id',
                    'sn_code.sn_code'
                ])->get();

            $relation = [];
            foreach ($macRelation as $item) {
                $relation[$item['mac_id']] = $item;
            }
            foreach ($paginate['data'] as &$datum) {
                $datum['used_type_text'] = $types[$datum['used_type']]['name'] ?? '';
                $datum['used_date_day']  = !empty($datum['used_date']) ? date('Y-m-d', strtotime($datum['used_date'])) : '';
                $datum['used_user_name'] = !empty($users[$datum['used_user_id']]['name']) ? $users[$datum['used_user_id']]['name'] : '';
                $datum['origin_type_text'] = ProductionCode::ORIGIN_TYPE[$datum['origin_type']] ?? '';
                $datum['sn_code'] = $relation[$datum['id']]['sn_code'] ?? '';
                $datum['batch_key'] = $relation[$datum['id']]['batch_key'] ?? '';
            }
        }
        return $paginate;
    }

    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }

            // 判断mac是否已存在
            if (!empty($values['mac_address']) && $row->mac_address != $values['mac_address']) {
                if ($this->model::query()->where('mac_address', $values['mac_address'])->first()) {
                    throw new AppException(StatusCode::ERR_SERVER, __('production.Mac_address_exist'));
                }
            }
            if (isset($values['is_used']) && $values['is_used'] === 0) {
                $this->setnotUsedData($values);
            }
            if(empty($row['mac_decimal']) && empty($values['mac_decimal'])){
                $macAddress = $values['mac_address']?? $row['mac_address'];
                $values['mac_decimal'] = hexdec($macAddress);
            }
            if((!empty($values['used_type']) && $row->used_type != $values['used_type']) ||
                (!empty($values['used_no']) && $row->used_no!= $values['used_no'])){
               $this->relateOrderId($values);
            }
            $result = $row->update($values);
        } else {
            // 判断mac是否已存在
            if ($this->model::query()->where('mac_address', $values['mac_address'])->first()) {
                throw new AppException(StatusCode::ERR_SERVER, __('production.Mac_address_exist'));
            }
            if (isset($values['is_used']) && $values['is_used'] === 0) {
                $this->setnotUsedData($values);
            }
            if(empty($values['mac_decimal'])){
                $values['mac_decimal'] = hexdec($values['mac_address']);
            }
            if(!empty($values['used_type'])){
                $this->relateOrderId($values);
            }
            $result = $this->model::query()->create($values);
        }

        return $result;
    }

    /**
     * 生产订单插入mac地址
     * @param $relationType
     * @param $relationId
     * @param $amount
     * @return bool
     */
    public function assignMacAddress($relationType, $relationId, $amount, $startMac = null, $isCli = false)
    {
        // $macIdArr = $this->model::query()->where('is_used',0)->limit($amount)->pluck('id');
        // if(count($macIdArr)< $amount){
        //     throw new AppException(StatusCode::ERR_EXCEPTION,'mac地址数量无法满足分配');
        // }
        // $this->model::query()->whereIn('id',$macIdArr)->update(['is_used'=>1]);
        if ($relationType == ProductionCode::CODE_USED_TYPE_PRODUCTION) {
            $order = ProductionOrderModel::query()->find($relationId);
        } elseif ($relationType == ProductionCode::CODE_USED_TYPE_ASSEMBLE) {
            $order = AssembleOrderModel::query()->find($relationId);
        } else {
            $order = [];
        }
        $save = [
            'used_type'    => $relationType,
            'used_relate_id' => $relationId,
            'used_no'        => $order['code'] ?? '',
            'used_product'   => $order['product_name'] ?? '',
            'used_product_code'   => $order['product_code'] ?? '',
            'used_user_id' => $isCli ? 0 : $this->auth->user()->getId(),
            'is_used'      => 1,
            'used_date'    => date('Y-m-d'),
            'start_mac'    => $startMac,
        ];
        $this->createMac($amount, $save);
        return true;
    }

    /**
     * 根据关联单号减少绑定mac地址
     * @param int $productionId 关联ID
     * @param int $relationType 关联类型
     * @param int $amount 减少的数量
     * @return bool
     */
    public function reduceByRelationId(int $productionId, int $relationType, int $amount): bool
    {
        $macs = $this->model::query()
            ->where('used_type', $relationType)
            ->where('used_relate_id', $productionId)
            ->orderBy('mac_decimal', 'desc')->limit($amount)->get();
        if ($macs) {
            if (count($macs) < $amount) {
                throw new AppException(StatusCode::ERR_EXCEPTION, sprintf(__('production.Mac_reduce_insufficient'), $amount));
            }
            Db::beginTransaction();
            try {
                $idsArr = array_column($macs->toArray(), 'id');
                $notUsedData = [];
                $this->setnotUsedData($notUsedData);
                // 批量更新未使用状态
                $this->doMulti(implode(',', $idsArr), $notUsedData);
                Db::commit();
                return true;
            } catch (\Throwable $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        throw new AppException(StatusCode::ERR_EXCEPTION, __('production.Not_exit_mac'));
    }

    /**
     *
     * @param $startMac
     * @return bool
     */
    public function createMac($amount, $values)
    {
        if (!empty($values['start_mac'])) {
            $startMac = $values['start_mac'];
            unset($values['start_mac']);
            $startNo = hexdec($startMac);
        } else {
            $startMac = $this->getLastMac();
            $startNo  = hexdec($startMac) + 1;
        }

        // 判断是否在公司购买的范围内
        $lastNo = $startNo + $amount;
        if ($startNo < hexdec(ProductionCode::START_MAC)) {
            throw new AppException(StatusCode::ERR_EXCEPTION, sprintf(__('production.Greater_start_mac'), dechex($startNo), dechex(ProductionCode::START_MAC)));
        }
        if ($lastNo > hexdec(ProductionCode::END_MAC)) {
            throw new AppException(StatusCode::ERR_EXCEPTION, sprintf(__('production.Greater_end_mac'), dechex($lastNo), dechex(ProductionCode::END_MAC)));
        }

        //判断区间是否满足数量，以保证mac地址的连续性

        $usedCount = $this->model::query()
            ->where('mac_decimal', '>=', $startNo)
            ->where('mac_decimal', '<=', $lastNo)
            ->where('is_used', 1)
            ->count();
        if ($usedCount > 0) {
            throw new AppException(StatusCode::ERR_EXCEPTION, sprintf(__('production.Not_Continuous'), $startMac, dechex($lastNo), $usedCount));
        }


        if (empty($values['used_type'])) {
            $values['used_type'] = ProductionCode::CODE_USED_TYPE_OTHER;
        }else{
            $this->relateOrderId($values);
        }
        //更新未使用的
        $notUsedMac = $this->model::query()
            ->where('mac_decimal', '>=', $startNo)
            ->where('mac_decimal', '<=', $lastNo)
            ->where('is_used', 0)
            ->pluck('mac_decimal', 'id')->toArray();
        $this->model::query()->whereIn('id', array_keys($notUsedMac))->update([
            'used_date'      => date('Y-m-d'),
            'used_type'      => $values['used_type'],
            'used_relate_id' => $values['used_relate_id'] ?? 0,
            'used_no'        => $values['used_no'] ?? '',
            'used_product'   => $values['used_product'] ?? '',
            'used_product_code'   => $values['used_product_code'] ?? '',
            'used_client'    => $values['used_client'] ?? '',
            'used_user_id'   => $values['used_user_id'] ?? 0,
            'is_used'        => $values['is_used'] ?? 0,
        ]);
        $insertMacData = [];
        for ($i = 0; $i < $amount; $i++) {
            $macDecimal = $startNo + $i;
            if (in_array($macDecimal, $notUsedMac)) {
                continue;
            }
            $insertMacData[] = [
                'mac_decimal' => $macDecimal,
                'mac_address' => dechex($macDecimal),
                'used_date' => date('Y-m-d'),
                'used_type' => $values['used_type'],
                'used_relate_id' => $values['used_relate_id'] ?? 0,
                'used_no'        => $values['used_no'] ?? '',
                'used_product'   => $values['used_product'] ?? '',
                'used_product_code'   => $values['used_product_code'] ?? '',
                'used_client'    => $values['used_client'] ?? '',
                'used_user_id' => $values['used_user_id'] ?? 0,
                'is_used'        => $values['is_used'] ?? 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        Logger()->info('---------maccount------', [
            count($insertMacData),
            [
                $startNo,
                $macDecimal
            ]
        ]);
        $insertBatch = array_chunk($insertMacData, 1000);
        foreach ($insertBatch as $batch) {
            $result = $this->model::query()->insert($batch);
            if (!$result) {
                throw new AppException(StatusCode::ERR_EXCEPTION, __('exception.err_sqlerror'));
            }
        }
        return $result;
    }

    /**
     * 批量更新
     * @param $ids
     * @param $params
     * @return bool
     */
    public function doMulti($ids, $params): bool
    {
        if ($ids) {
            $values = $params;
            if (!is_array($params)) {
                parse_str($params, $values);
            }
            if (!$values) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
            }
            $idsArr = explode(',', $ids);
            $result = $this->model::query()->whereIn('id', $idsArr)->update($values);
            if ($result) {
                return true;
            } else {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
            }
        }
        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
    }

    /**
     * 生产订单休息
     * @return array
     */
    public function factoryProductionExport($productionId,$type)
    {
        $macs = $this->model::query()
            ->where('used_type', $type)
            ->where('used_relate_id', $productionId)
            ->orderBy('mac_decimal')->get();
        $exportList    = [];
        if ($macs) {
            $macs     = $macs->toArray();
            $startMac = (int)$macs[0]['mac_decimal'];
            $order    = 0;
            foreach ($macs as $key => $mac) {
                $nowMac = $key == 0 ? $startMac : $startMac + 1;
                if ($nowMac == $mac['mac_decimal']) {
                    $exportList[$order][] = $mac;
                } else {
                    $order++;
                    $exportList[$order][] = $mac;
                }
                $startMac = $mac['mac_decimal'];
            }
        }
        return $exportList;
    }

    /**
     * 保存数据设置为未使用
     * @param $data
     * @return void
     */
    public function setnotUsedData(&$data)
    {
        $data['is_used']      = 0;
        $data['used_no']      = null;
        $data['used_date']    = null;
        $data['used_client']  = null;
        $data['used_product'] = null;
        $data['used_product_code'] = '';
        $data['used_type']    = 0;
        $data['used_user_id'] = 0;
        $data['used_relate_id'] = 0;
    }

    public function getLastMac()
    {
        $last = $this->model::query()->orderByRaw('HEX(mac_address) desc')->value('mac_address');
        $last = $last ?: ProductionCode::START_MAC;
        return $last;
    }

    public function doDelete($ids): int
    {
        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);
            $this->model::destroy($ids);
            OrderMacSnBatchModel::query()->whereIn('mac_id', $ids)->delete();
            Db::commit();
            return 1;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }


    /**
     * 返回不在指定范围内的十六进制数
     * @param array $macAddress
     * @return array
     */
    public function checkHexRange(array $macAddress)
    {
        $minHex = hexdec(ProductionCode::START_MAC);
        $maxHex = hexdec(ProductionCode::END_MAC);

        // 使用array_filter过滤出不在指定范围内的十六进制数
        $outRange = array_filter($macAddress, function ($hex) use ($minHex, $maxHex) {
            return hexdec($hex) < $minHex || hexdec($hex) > $maxHex;
        });

        // 返回超出范围的十六进制数
        return $outRange;
    }

    /**
     * 获取订单的mac地址
     * @param $orderType
     * @param $orderId
     * @return array|mixed[]
     */
    public function getOrderCode($orderType, $orderId)
    {
        $data = $this->model::query()->where([
            'mac_address.used_type'      => $orderType,
            'mac_address.used_relate_id' => $orderId,
        ])->leftJoin('order_mac_sn_batch', function ($join) {
            $join->on('order_mac_sn_batch.mac_id', '=', 'mac_address.id');
            $join->whereNull('order_mac_sn_batch.deleted_at');
        })
            ->leftJoin('sn_code', 'sn_code.id', '=', 'order_mac_sn_batch.sn_id')
            ->select([
                'mac_address.mac_address',
                'order_mac_sn_batch.batch_key',
                'order_mac_sn_batch.batch_num',
                'sn_code.sn_code'
            ])->orderBy('order_mac_sn_batch.batch_num')
            ->orderBy('mac_address.mac_decimal')->get();
        return $data ? $data->toArray() : [];
    }

    public function getCodeField()
    {
        return 'mac_address';
    }

    public function buildEditData($data)
    {
        $data['mac_decimal'] = hexdec($data['mac_address']);
        return $data;
    }

    /**
     * 修复关联的订单
     * @return void
     */
    public function fixRelateOrder()
    {
        $list = $this->model::query()
            ->whereIn('used_type', [ProductionCode::CODE_USED_TYPE_PRODUCTION,ProductionCode::CODE_USED_TYPE_ASSEMBLE])
            ->where('used_relate_id', 0)
            ->get();
        foreach ($list as $item){
            if($item['used_type'] == ProductionCode::CODE_USED_TYPE_PRODUCTION){
                $order = ProductionOrderModel::query()->where('code', $item['used_no'])->first();
            }else{
                $order = AssembleOrderModel::query()->where('code', $item['used_no'])->first();
            }
            if($order){
                $item->used_relate_id = $order['id'];
                $item->save();
            }
        }
    }

    /**
     * 校验订单填写的mac地址区间是否合理
     * @param $originType
     * @param $rangeArr
     * @param $relateOrder
     * @return void
     */
    public function checkOrderRange($originType, $rangeArr, $relateOrder)
    {
        $err = '';

        foreach ($rangeArr as $range) {
            list($startHex, $endHex) = $range;
            $startDec = hexdec($startHex);
            $endDec = hexdec($endHex);
            //校验是否在公司购买范围内
            if($originType == ProductionCode::ORIGIN_TYPE_OF_SELF){
                if ($startDec < ProductionCode::START_MAC_DEC || $endDec > ProductionCode::END_MAC_DEC) {
                    $err .= $startHex . '-' . $endHex . '不在公司购买范围内；';
                }
            }
            //是否有被使用
            $usedMacCount = $this->model::query()
                ->whereBetween('mac_address', [
                    $startDec,
                    $endDec
                ])->where('origin_type', $originType)
                ->where('used_type', '<>', $relateOrder['used_type'])->where('used_relate_id', '<>', $relateOrder['used_relate_id'])
                ->where('is_used', 1)->count();
            if ($usedMacCount) {
                $err .= $startHex . '-' . $endHex . '已被使用.' . $usedMacCount . '个；';
            }

        }
        if ($err) {
            throw new AppException(StatusCode::ERR_SERVER, $err);
        }
    }

    /**
     * 根据区间值获取全部mac地址
     * @param $rangeArr
     * @return array
     */
    public function getRangeData($rangeArr)
    {
        $macArr = [];
        foreach ($rangeArr as $range){
            list($startHex, $endHex) = $range;
            $startDec = hexdec($startHex);
            $endDec = hexdec($endHex);
            $macArr = array_merge($macArr,range($startDec,$endDec));
        }
        $macArr = array_map('dechex', $macArr);
        return array_unique($macArr);
    }

    /**
     * 导出MAC地址
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function export(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $data = $this->getList($filter, $op, $sort, $order, 9999);
        $data = !empty($data['data']) ? $data['data'] : [];
        $filename = "MAC地址.xls";
        $sheetName = 'sheet1';
        $excelWriter = new ExcelWriter($filename, $sheetName);
        $option = [
            '序号'         => 'index',
            'MAC地址'      => 'mac_address',
            '使用类型'     => 'used_type_text',
            '生产/组装单号' => 'used_no',
            '使用人'       => 'used_user_name',
            '使用时间'     => 'used_date_day',
            '使用产品'     => 'used_product',
            '产品料号'     => 'used_product_code',
            '使用客户'     => 'used_client',
            '十进制码'     => 'mac_decimal',
            'SN号'         => 'sn_code',
            '订单备注'     => 'batch_key',
        ];
        $titleData = [
            array_keys($option)
        ];

        $width = [
            0,
            5,
            15,
            10,
            18,
            6,
            12,
            35,
            18,
            10,
            15  ,
            15,
            20,
        ];
        $excelWriter->setColumnWidthFromList($width);

        $title_style = [];
        for($i=0;$i<count($titleData[0]);$i++){
            $title_style[$i] = ['backgroundColor' => 'b4c6e7', 'bold' => true];
        }

        // 设置首行高度
        $excelWriter->setRowHeight(1, 30);

        $excelWriter->addData($titleData,[],[],[],$title_style);
        $row = [];
        foreach ($data as $k => $v) {
            //获取目录
            $index = $k + 1;
            $tempRow = [];
            foreach ($option as $key => $value) {
                if($value ==='index'){
                    $tempRow[] = $index;
                }else{
                    $tempRow[] = $v[$value];
                }
            }
            $row = [
                $tempRow
            ];
            $excelWriter->addData($row);
        }
        $result = $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

}