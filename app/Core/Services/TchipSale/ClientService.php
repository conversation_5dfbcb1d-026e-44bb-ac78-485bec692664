<?php

namespace App\Core\Services\TchipSale;

use App\Constants\DataBaseCode;
use App\Constants\TchipSaleCode;
use App\Core\Services\BusinessService;
use App\Model\TchipSale\ClientTableModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ClientService extends BusinessService
{

    /**
     * @Inject
     * @var ClientTableModel
     */
    protected $model;


    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        if (!isset($filter['delete_time'])) {
            $filter['delete_time'] = 0;
        }

        $searchName = null;
        if (!empty($filter['name'])) {
            $searchName = $filter['name'];
            unset($filter['name']);
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $clientTable = 'client_table';
        $clientConTable = 'client_contact_table';
        $paginate = $query->select(["{$clientTable}.*", "{$clientConTable}.comy", "{$clientConTable}.contact"])
            ->join($clientConTable, "{$clientTable}.id", '=', "{$clientConTable}.client_id")
            ->where(function ($query) use ($searchName, $clientTable, $clientConTable) {
                if ($searchName) {
                    $query->where("{$clientTable}.name", 'LIKE', "%{$searchName}%")->orWhere("{$clientConTable}.comy", 'LIKE', "%{$searchName}%")
                        ->orWhere("{$clientConTable}.contact", 'LIKE', "%{$searchName}%");
                }
                return $query;
            })
            ->paginate();
        $paginate = $paginate ? $paginate->toArray() : [];
        if (!empty($paginate['data'])) {

            foreach ($paginate['data'] as &$datum) {
                if (empty($datum['comy'])) {
                    $datum['comy'] = $datum['name'];
                }
            }
        }
        return $paginate;
    }
}