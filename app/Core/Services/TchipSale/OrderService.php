<?php

namespace App\Core\Services\TchipSale;

use App\Constants\StatusCode;
use App\Constants\TchipSaleCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipSale\OrderlistModel;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Model\TchipSale;
use App\Model\TchipBi;
use App\Core\Utils\TimeUtils;
use App\Core\Services\BusinessService;

class OrderService extends BusinessService
{
    /**
     * @Inject
     * @var OrderlistModel
     */
    protected $model;

    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;

    /**
     * @Inject()
     * @var SaleService
     */
    protected $saleService;

    /**
     * userId接受tchip_bi.user.id
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @return bool
     */
    public function orderMsgToUser($userId = null)
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始任务-推送未完成订单');
        $collectUserCountStartTime = microtime(true);

        $biUserModel     = make(TchipBi\UserModel::class);
        $saleUserModel   = make(TchipSale\UserTableModel::class);
        if ($userId) {
            $emailsWhere = [];
            $uidWhere    = [];
            if (is_array($userId)) {
                foreach ($userId as $uid) {
                    if (filter_var($uid,FILTER_VALIDATE_EMAIL)) {
                        $emailsWhere[] = $uid;
                    } else {
                        $uidWhere[] = $uid;
                    }
                }
            } else {
                if (filter_var($userId,FILTER_VALIDATE_EMAIL)) {
                    $emailsWhere[] = $userId;
                } else {
                    $uidWhere[] = $userId;
                }
            }
            $biUsers = $biUserModel::query();
            if ($uidWhere && $emailsWhere) {
                $biUsers = $biUsers->whereIn('name', $uidWhere)->orWhereIn('id', $uidWhere)->orWhereIn('email', $emailsWhere)->orWhereIn('biz_mail', $emailsWhere)->get()->toArray();
            } else if($uidWhere) {
                $biUsers = $biUsers->whereIn('name', $uidWhere)->orWhereIn('id', $uidWhere)->get()->toArray();
            } else if ($emailsWhere) {
                $biUsers = $biUsers->whereIn('email', $emailsWhere)->orWhereIn('biz_mail', $emailsWhere)->get()->toArray();
            } else {
                throw new AppException(StatusCode::ERR_EXCEPTION, '用户不存在');
                // Log::get('system', 'system')->info("推送用户: ".$biUsers['name'] . '不存在，跳过');
            }

            $emails    = array_column($biUsers, 'email');
            $bizEmails = array_column($biUsers, 'biz_mail');
            $emails    = array_unique(array_merge($emails, $bizEmails));
            $saleUsers = $saleUserModel::query()->select(['user_table.*', 'user_main_table.group_id'])
                ->join('user_main_table', 'user_table.id', '=', 'user_main_table.user_id')
                ->whereIn('user_table.email', $emails)->where('user_table.status', 1)->get()->toArray();
        } else {
            // 3=销售部门经理,4=销售,6=财务
            $saleUsers = $saleUserModel::query()->select(['user_table.*', 'user_main_table.group_id'])
                ->join('user_main_table', 'user_table.id', '=', 'user_main_table.user_id')
                ->where('user_table.status', 1)
                ->whereIn('user_main_table.group_id', [3,4,6])->get()->toArray();
        }
        $orderModel    = make(TchipSale\OrderlistModel::class);
        Log::get('system', 'system')->info("推送未完成订单-推送用户: ".count($saleUsers), implode(',', array_column($saleUsers, 'username')));
        // 2022.10.28加入月度业绩完成进度
        $monthTarget = $this->saleService->getAchievement($this->saleService::TYPE_MONTH, date('Y-m-d'));
        $saleCount   = $this->saleService->statisticsMoneyByDate(TimeUtils::getMonthFirstday(), TimeUtils::getMonthLastday());
        $saleTarget  = $monthTarget == 0 ? 0 : round(($saleCount / $monthTarget) * 100, 2);
        $targetColor = $saleTarget >= 80 ? 'info' : 'warning';

        foreach ($saleUsers as $key => $user) {
            $logContent = [];
            $groupId = $user['group_id'];
            $userId  = $user['id'];
            $orders  = $orderModel::query()->selectRaw('dwin_orderlist.*, (select sum(total) from dwin_orderlist co where (dwin_orderlist.id = co.parent_id or dwin_orderlist.id = co.id) and delete_time = 0) as total_money, IFNULL((SELECT sum(cp.paymount) as pay_money FROM dwin_orderpaylist cp WHERE (dwin_orderlist.id = cp.order_id)), 0) as pay_money')
                ->where('parent_id', 0)->whereIn('status', $orderModel->todo_status)
                ->where(function ($query) use($userId, $groupId) {
                    // 总管，总经理，销售经理级别查看所有
                    if (in_array($groupId, [1,2,3,6])) {
                        $query->where('user_id', '>', 0);
                    }else {
                        $query->where('user_id', $userId);
                    }
                })->where('delete_time', 0)->get()->toArray();
            $orderCount = count($orders);
            if ($orderCount <= 0) {
                $logMessage="用户:{$user['username']}，订单数 0，跳过推送 ";
            }
            $arrearage = 0;
            foreach ($orders as $order) {
                $arrearageItem = $order['total_money'] - $order['pay_money'];
                $arrearageItem = $arrearageItem > 0 ? $arrearageItem : 0;
                if ($arrearageItem > 0 && $order['curr'] == '美元') {
                    $rate = nowUsdRate($order['ordertime']);
                    $arrearageItem = $arrearageItem * $rate;
                }
                $arrearage += $arrearageItem;
            }
            // 转换货币格式
            if ($arrearage > 0) {
                $arrearage = num_format($arrearage, 2);
            }

            $biUser = $biUserModel::query()->where('email', $user['email'])->orWhere('biz_mail', $user['email'])->first()->toArray();
            if ($biUser) {
                $userName = $user['username'];
                $content = <<<EOT
Hi {$userName}，
数字天启提醒您：
目前销售系统
共有 <font color="warning">{$orderCount}</font> 笔订单正在跟进，
共有 <font color="warning">￥{$arrearage}</font> 款项未收回，
已完成<font color="{$targetColor}"> {$saleTarget}%</font> 月度业绩目标。
[点击查看详情](http://***********:8080/tchipsale/index.php?s=/Admin)
EOT;

                if ($orderCount && $arrearage) {
                    try {
                        $this->workWxMessageService->sendMarkdown($content, $biUser['workwx_userid'], '', '');
                        // $this->workWxMessageService->sendMarkdown($content, 'XiaoJiaJie', '', '');
                        $logMessage = "用户:{$user['username']} 推送成功";
                        $logContent = ['订单数'=>$orderCount, '推送内容'=>$content];
                    }catch (\Exception $e){
                        $logMessage = "用户:{$user['username']} 推送失败" . $e->getMessage();
                    }

                }
            } else {
                $logMessage = "用户:{$user['username']},找不到企业微信信息";
            }
            Log::get('system','system')->info('进度:' . ($key + 1) . '/' . count($saleUsers) . ' ' . $logMessage, $logContent);
        }

        $executionTime = sprintf('%.2f', ((microtime(true) - $collectUserCountStartTime) * 1000));
        Log::get('system', 'system')->info('推送结束，耗时'.$executionTime.'毫秒');
        Log::get('system', 'system')->info('==============================================================================');
        return true;
    }

    public function orderDemandByStockSn($sn)
    {
        $demand = $this->model::query()->select(['orderlist.id', 'orderlist.demand'])
            ->with(['demandRecord'=>function ($query){
                return $query->orderBy('created_at', 'DESC');
            }])
            ->join('stock_orderlist', 'stock_orderlist.order_id', '=', 'orderlist.id')
            ->where('stock_orderlist.sn', $sn)->first();
        $demand = $demand ? $demand->toArray() : null;
        if (!empty($demand['demand_record'])) {
            $userIds = array_column($demand['demand_record'], 'user_id');
            $users = TchipSale\UserTableModel::query()->whereIn('id', $userIds)->get();
            $users = $users ? array_column($users->toArray(), null, 'id') : [];
            foreach ($demand['demand_record'] as &$record) {
                $record['username'] = $users[$record['user_id']]['username'] ?? '';
            }
        }
        return $demand;
    }
}