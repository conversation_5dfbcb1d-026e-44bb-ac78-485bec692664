<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/30 下午4:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\TchipSale;

use App\Constants\StatusCode;
use App\Core\Services\BaseService;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Guzzle\ClientFactory;

class SaleBaseService extends BaseService
{
    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    protected $uri;

    public function __construct()
    {
        parent::__construct();
        $options = [
            'base_uri' => env('TCHIP_SALE_ERP_URL'),
            'timeout'  => 10
        ];
        $this->clientFactory = make(ClientFactory::class)->create($options);
    }

    public function getPage($filter, $op, $sort, $order, $limit = 10)
    {
        $pageNo = make(\Hyperf\HttpServer\Contract\RequestInterface::class)->input('pageNo');
        $url = $this->uri ? $this->uri . 'getPage' : 'getPage';
        $result = $this->sendRequest($url, ['json' => ['filter' => $filter, 'op' => $op, 'sort' => $sort, 'order' => $order, 'limit' => $limit, 'page' => $pageNo]], 'POST');
        return $result['data'] ?? [];
    }

    public function getList($filter, $op, $sort, $order = 'DESC', $limit = null)
    {
        $url = $this->uri ? $this->uri . 'getList' : 'getList';
        $result = $this->sendRequest($url, ['json' => ['filter' => $filter, 'op' => $op, 'sort' => $sort, 'order' => $order, 'limit' => $limit]], 'POST');
        return $result['data'] ?? [];
    }

    public function overView($id, $filter = [], $op = [])
    {
        $url = $this->uri ? $this->uri . 'overView' : 'overView';
        $result = $this->sendRequest($url, ['json' => ['id' => $id, 'filter' => $filter, 'op' => $op]], 'POST');
        return $result['data'] ?? [];
    }

    public function sendRequest($uri, $options, $method = 'get', $header = [])
    {
        try {
            if($header){
                $options['headers'] = array_merge($options['headers'], $header);
            }

            $response = $this->clientFactory->request($method, $uri, $options);

            $responseArr = json_decode($response->getBody()->getContents(), true);
            return $responseArr;
        } catch (GuzzleException $e) {
            if($e->getCode() == '403'){
                throw new AppException(StatusCode::ERR_FORBIDDEN);
            }
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}