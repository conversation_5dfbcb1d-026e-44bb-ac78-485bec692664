<?php

namespace App\Core\Services\TchipSale;

use App\Constants\ProductionCode;
use App\Core\Services\BusinessService;
use App\Model\TchipSale\OutStockQrcodeModel;
use Hyperf\Di\Annotation\Inject;

class OutStockQrcodeService extends BusinessService
{
    /**
     * @Inject
     * @var OutStockQrcodeModel
     */
    protected $model;

    public function getOverView($id)
    {
        $info =  $this->model::query()->with('saleData')->find($id);
        if($info){
            $info = $info->toArray();
            $info['sale_data'] = array_filter($info['sale_data'],function($item){
                return in_array($item['platform_id'],[23,25]);
            });
            foreach ($info['sale_data'] as &$item){
                $item['sn_data'] = [];
            }

            $info['qrcode_id'] = $info['id'];
            unset($info['id']);
            return $info;
        }
        return [];
    }

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('out_stock_no', 'like', '%' . $keywords . '%')
                    ->orWhere('order_no', 'like', '%' . $keywords . '%')
                    ->orWhere('client_name', 'like', '%' . $keywords . '%')
                    ->orWhere('out_stock_qrcode_sale.product_name', 'like', '%' . $keywords . '%')
                    ->orWhere('out_stock_qrcode_sale.product_code', 'like', '%' . $keywords . '%');
            });

        }
        $query->join('out_stock_qrcode_sale',function($join){
            $join->on('out_stock_qrcode_sale.qrcode_id', '=', 'out_stock_qrcode.id')
                ->whereIn('out_stock_qrcode_sale.platform_id', ProductionCode::SHIPMENT_PLATFORM_ARR);
        });
        $query->select('out_stock_qrcode.*');
        $data =  $query->with('saleData')->groupBy('out_stock_qrcode.id')->orderBy($sort, $order)->paginate($limit)->toArray();
        foreach ($data['data'] as &$item){
            !$item['sale_data'] && $item['sale_data'] = [];
            //汇总产品，去重
            $productData =[];
            foreach ($item['sale_data'] as $sale){
                in_array($sale['platform_id'],ProductionCode::SHIPMENT_PLATFORM_ARR) && $productData[$sale['product_id']] = [
                    'name' => $sale['product_name'],
                    'code' => $sale['product_code'],
                ];
            }
            $item['product_name'] = implode(',',array_column($productData,'name'));
            $item['product_code'] = implode(',',array_column($productData,'code'));
        }
        return $data;
    }
}