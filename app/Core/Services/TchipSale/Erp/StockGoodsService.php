<?php

namespace App\Core\Services\TchipSale\Erp;

use App\Constants\StatusCode;
use App\Constants\TchipErpCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use App\Model\TchipSale\LinkageModel;
use App\Model\TchipSale\ErpStockDayModel;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Annotation\CrotabLogsAnnotation;

class StockGoodsService extends \App\Core\Services\TchipSale\SaleBaseService
{
    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;

    protected $uri = 'firefly_erpapi/public/index.php/stock/index/';

    public function getPage($filter, $op, $sort, $order, $limit = 10)
    {
        $url = $this->uri ? $this->uri . 'getPage' : 'getPage';
        $limit = $limit == 10 ? 100 : $limit;
        $result = $this->sendRequest($url, ['query' => ['filter' => $filter, 'op' => $op, 'sort' => $sort, 'order' => $order, 'limit' => $limit]]);
        return $result['data'] ?? [];
    }
}