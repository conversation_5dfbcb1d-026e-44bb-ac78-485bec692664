<?php

namespace App\Core\Services\TchipSale\Erp;

use App\Constants\StatusCode;
use App\Constants\TchipErpCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use App\Model\TchipSale\LinkageModel;
use App\Model\TchipSale\ErpStockDayModel;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Annotation\CrotabLogsAnnotation;

class OrderPohService extends \App\Core\Services\TchipSale\SaleBaseService
{
    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    protected $uri = 'firefly_erpapi/public/index.php/order/poh/';

    public function overView($id, $filter = [], $op = [])
    {
        $url = $this->uri ? $this->uri . 'overView' : 'overView';
        $result = $this->sendRequest($url, ['query' => ['code' => $id, 'filter' => $filter, 'op' => $op]]);
        return $result['data'] ?? [];
    }

    /**
     * 获取供应商列表
     * @param array $filter 筛选条件
     * @param array $op 操作符
     * @return array
     */
    public function getSupplierList($filter = [], $op = [])
    {
        $url = 'firefly_erpapi/public/index.php/order/supply/getList';
        $result = $this->sendRequest($url, ['query' => ['filter' => $filter, 'op' => $op]]);
        
        return $result['data'] ?? [];
    }

}