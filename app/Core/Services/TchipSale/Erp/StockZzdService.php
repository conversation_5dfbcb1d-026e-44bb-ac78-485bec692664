<?php

namespace App\Core\Services\TchipSale\Erp;

use App\Constants\StatusCode;
use App\Constants\TchipErpCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use App\Model\TchipSale\LinkageModel;
use App\Model\TchipSale\ErpStockDayModel;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use App\Annotation\CrotabLogsAnnotation;

class StockZzdService extends \App\Core\Services\TchipSale\SaleBaseService
{
    /**
     * @Inject()
     * @var \Hyperf\Contract\StdoutLoggerInterface
     */
    private $logger;

    protected $uri = 'firefly_erpapi/public/index.php/stock/zzd/';

}