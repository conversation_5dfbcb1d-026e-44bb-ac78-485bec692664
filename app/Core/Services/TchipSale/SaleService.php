<?php

namespace App\Core\Services\TchipSale;

use App\Core\Services\BusinessService;
use App\Core\Utils\TimeUtils;
use App\Model\Model;
use App\Model\TchipSale;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;

class SaleService extends BusinessService
{

    const TYPE_MONTH   = 'month';
    const TYPE_QUARTER = 'quarter';
    const TYPE_YEAR    = 'year';

    /**
     * 获得季度业绩目标额度
     * @param $type String 类型
     * @param $date
     * @return float|int
     */
    public function getAchievement($type, $date)
    {
        $kpiModel = make(TchipSale\SaleKpiModel::class);
        $quarter  = getQuarter($date);
        if (empty($kpiModel->quarterField[$quarter])) {
            return 0;
        }
        $year = date('Y', strtotime($date));
        $row  = $kpiModel::query()->where('year_date', $year)->where('delete_time', 0)->first();
        if (!$row) {
            return 0;
        }
        $row         = $row->toArray();
        $achievement = $row[$kpiModel->quarterField[$quarter]] * getUnitNum($row[$kpiModel->quarterField[$quarter] . '_unit']);
        $quota       = 0;
        $decimal     = 2;

        if ($achievement) {
            switch ($type) {
                case self::TYPE_MONTH :
                    // // 月度业绩改为年度总额除12
                    // $quota = ($achievement / 3);
                    $quota = ($row[$kpiModel->quarterField[1]] * getUnitNum($row[$kpiModel->quarterField[1].'_unit'])) +
                             ($row[$kpiModel->quarterField[2]] * getUnitNum($row[$kpiModel->quarterField[2].'_unit'])) +
                             ($row[$kpiModel->quarterField[3]] * getUnitNum($row[$kpiModel->quarterField[3].'_unit'])) +
                             ($row[$kpiModel->quarterField[4]] * getUnitNum($row[$kpiModel->quarterField[4].'_unit'])) ;
                    $quota = $quota / 12;
                    break;
                case self::TYPE_QUARTER:
                    $quota = $achievement;
                    break;
                case self::TYPE_YEAR:
                    $quota = ($row[$kpiModel->quarterField[1]] * getUnitNum($row[$kpiModel->quarterField[1] . '_unit'])) +
                             ($row[$kpiModel->quarterField[2]] * getUnitNum($row[$kpiModel->quarterField[2] . '_unit'])) +
                             ($row[$kpiModel->quarterField[3]] * getUnitNum($row[$kpiModel->quarterField[3] . '_unit'])) +
                             ($row[$kpiModel->quarterField[4]] * getUnitNum($row[$kpiModel->quarterField[4] . '_unit']));
            }
            $quota = round($quota, $decimal);
        }
        return $quota;
    }

    /**
     * 业绩进度
     * @param string $type
     * @param $date
     * @return void
     */
    public function achievementProgress(string $type = 'month', $date = null)
    {
        $date = $date ?? date('Y-m-d');
        switch ($type) {
            case 'month' :
                $dates = TimeUtils::getMonthFirstandLast($date);
                break;
            case 'quarter' :
                $quarter  = getQuarter($date);
                $dates = TimeUtils::getQuarterDate($quarter);
                break;
            case 'year' :
                $year = date('Y', strtotime($date));
                $dates = [
                    'starttime' => $year.'-01-01 00:00:00',
                    'endtime' => $year.'-12-31 23:59:59',
                ];
                break;
        }
        if ($dates) {
            $sale = $this->statisticsMoneyByDate($dates['starttime'], $dates['endtime']);
        }
        $quota = $this->getAchievement($type, $date);
        return $sale > 0 ? round(($sale / $quota) * 100, 2) : $sale;
    }

    /**
     * 按订单数量获取机型的销售占比
     * @param $filter
     * @param $propType
     * @return float|int
     */
    public function deviceProportion($filter, $propType = 'device')
    {
        $linkageService = make(LinkageService::class);
        // 整机 = 行业整机 + StationPC
        if ($propType == 'device') {
            $ids = $linkageService->getOpensoureCategoryComputerIds();
        }
        $prods     = LinkageModel::query()->whereIn('opensoure_category', $ids)->where(function ($query) {
            $query->where('material', '<>', '')->orWhereNotNull('material');
        })->where('status', 1)->pluck('id');
        $kyptModel = make(TchipSale\ClientSaleKyptTableModel::class)::query();
        // 加入条件
        if (!empty($filter['saledate'])) {
            $filter['saledate'] = is_array($filter['saledate']) ? $filter['saledate'] : explode(' - ', trim($filter['saledate']));
            if (count($filter['saledate']) > 1) {
                $kyptModel->whereBetween('saledate', $filter['saledate']);
            } else {
                $kyptModel->where('saledate', '>=', $filter['saledate'][0]);
            }
        }

        // $retas = TchipSale\CurrencyRateModel::query()->select('linkage.text as curr, currency_rate.year_date, currency_rate.reta')
        //     ->leftJoin('linkage', 'currency_rate.currency_id', '=', 'linkage.id')->get();
        // $retas = $retas ? $retas->toArray() : [];
        // // 币种列表
        // $currency = [];
        // foreach ($retas as $reta) {
        //     $currency[$reta['curr']][$reta['year_date']] = $reta;
        // }

        $orderCount     = $kyptModel->where('delete_time', 0)->where('num', '>', 0)->count();
        $typeOrderCount = $kyptModel->where('delete_time', 0)->where('num', '>', 0)->whereIn('size_id', $prods)->count();
        $prop = $typeOrderCount != 0 ? round(($typeOrderCount / $orderCount) * 100, 2) : $typeOrderCount;
        return $prop < 0 ? 0 : $prop;
    }

    /**
     * @param $filter
     * @return array[]
     */
    public function orderCount($filter)
    {

        $kyptModel = make(TchipSale\ClientSaleKyptTableModel::class)::query();
        $pjModel   = make(TchipSale\ClientSalePjTableModel::class)::query();
        $zxfwModel = make(TchipSale\ClientSaleZxfwTableModel::class)::query();

        // 加入条件
        if (!empty($filter['saledate'])) {
            $filter['saledate'] = is_array($filter['saledate']) ? $filter['saledate'] : explode(' - ', trim($filter['saledate']));
            if (count($filter['saledate']) > 1) {
                $kyptModel->whereBetween('saledate', $filter['saledate']);
                $pjModel->whereBetween('saledate', $filter['saledate']);
                $zxfwModel->whereBetween('saledate', $filter['saledate']);
            } else {
                $kyptModel->where('saledate', '>=', $filter['saledate'][0]);
                $pjModel->where('saledate', '>=', $filter['saledate'][0]);
                $zxfwModel->where('saledate', '>=', $filter['saledate'][0]);
            }
        }

        // 通用查询字段
        $commonField = 'sale_id, num, money, ratemoney, curr';

        $pjQuery   = $pjModel->selectRaw('concat(\'pj_\', id) as cid, ' . $commonField);
        $zxfwQuery = $zxfwModel->selectRaw('concat(\'zxfw_\', id) as cid, ' . $commonField);
        $rows      = $kyptModel->selectRaw('concat(\'kypt_\', id) as cid, ' . $commonField)
            ->unionAll($pjQuery)->unionAll($zxfwQuery)->get();
        // 外单ID列表sale_id
        $exteriorSource = make(LinkageService::class)->saleSource(2);
        $exteriorIds    = array_column($exteriorSource, 'id');
        $exteriorCount  = $rows->whereIn('sale_id', $exteriorIds)->count();
        $insideCount    = $rows->whereNotIn('sale_id', $exteriorIds)->count();
        $result         = [
            'all'      => [
                'count' => count($rows),
                'prop'  => 100,
            ],
            'exterior' => [
                'count' => $exteriorCount,
                'prop'  => $exteriorCount != 0 ? round(($exteriorCount / count($rows)) * 100, 2) : $exteriorCount,
            ],
            'inside'   => [
                'count' => $insideCount,
                'prop'  => $insideCount != 0 ? round(($insideCount / count($rows)) * 100, 2) : $insideCount,
            ],
        ];
        return $result;
    }


    /**
     * 根据指定时间统计所有销售额
     * @param $start 开始日期
     * @param $end   结束日期
     * @param $table 需要
     * @param $filter 筛选条件
     * @param $op 条件类型
     * @return float
     */
    public function statisticsMoneyByDate($start, $end, $table = null, $filter = [], $op = []) : float
    {
        $kyptModel = make(TchipSale\ClientSaleKyptTableModel::class);
        $pjModel   = make(TchipSale\ClientSalePjTableModel::class);
        $zxfwModel = make(TchipSale\ClientSaleZxfwTableModel::class);

        $models = [];
        switch ($table) {
            case 'kypt':
                $models[] = $kyptModel;
                break;
            case 'pj':
                $models[] = $pjModel;
                break;
            case 'zxfw':
                $models[] = $zxfwModel;
                break;
            default:
                $models = [$kyptModel, $pjModel, $zxfwModel];
        }
        $num  = 2;
        $saleCount = 0;
        foreach ($models as $model) {
            /** @var $model Model */
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $model);
            $saleItems  = $query->selectRaw("round(IFNULL(SUM(IF(curr = '美元', money , 0)), 0), {$num}) as dollar, round(IFNULL(SUM(IF(curr = '人民币', money , 0)), 0), {$num}) as rmb, round(IFNULL(SUM(IF(curr = '港元', money , 0)), 0), {$num}) as hkb, DATE_FORMAT(saledate, '%Y') as year")
                ->whereBetween('saledate', [$start, $end])->where('replenish', 0)->where('delete_time', 0)->groupBy('year')->get();
            $saleItems= $saleItems? $saleItems->toArray() : [];
            foreach ($saleItems as $item) {
                $usdRate = nowUsdRate($item['year']);
                $hkbRate = nowHkbRate($item['year']);
                $saleCount += $item['rmb'] + ($item['dollar'] * $usdRate) + ($item['hkb'] * $hkbRate);
            }
        }
        return round($saleCount, $num);
    }

    /**
     * 根据指定时间统计所有销售量
     * @param $start 开始日期
     * @param $end   结束日期
     */
    public function statisticsVolumeByDate($start, $end, $table = null, $filter = [], $op = [])
    {
        $kyptModel = make(TchipSale\ClientSaleKyptTableModel::class);
        $pjModel   = make(TchipSale\ClientSalePjTableModel::class);
        $zxfwModel = make(TchipSale\ClientSaleZxfwTableModel::class);

        $models = [];
        switch ($table) {
            case 'kypt':
                $models[] = $kyptModel;
                break;
            case 'pj':
                $models[] = $pjModel;
                break;
            case 'zxfw':
                $models[] = $zxfwModel;
                break;
            default:
                $models = [$kyptModel, $pjModel, $zxfwModel];
        }
        $volumeCount = 0;
        foreach ($models as $model) {
            /** @var $model Model */
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $model);
            $saleItems  = $query->selectRaw("sum(num) as num")
                ->whereBetween('saledate', [$start, $end])->where('replenish', 0)->where('delete_time', 0)->first();
            $volumeCount += $saleItems['num'] ?? 0;

        }
        return $volumeCount;
    }

    /**
     * @param Model $model
     * @param $date
     * @return void
     */
    public function saleDateList(Model $model, $date = null)
    {
        $date = is_array($date) ? $date : explode(' - ', trim($date));
        if (empty($date)) {
            $date[0] = $model->where('delete_time', 0)->orderBy('saledate', 'asc')->value('saledate');
            $date[1] = $model->where('delete_time', 0)->orderBy('saledate', 'desc')->value('saledate');
        } else if (count($date) == 1) {
           $date[] = $model->where('delete_time', 0)->orderBy('saledate', 'desc')->value('saledate');
           if ($date[0] < $date[1]) {
               $date[0] = $date[1];
           }
        }
        $list = TimeUtils::yearList($date[0], $date[1]);
        return $list;
    }

    /**
     * 根据物流号查询开源平台销售单
     * @param $keyword
     * @return mixed[]
     */
    public function getKIdByLogisticsNo($keyword)
    {
        $idArr = TchipSale\ClientSaleKyptTableModel::query()
            ->where('delete_time', 0)
            ->where('logistics_no', 'like', '%' . $keyword . '%')
            ->pluck('id')->toArray();
        return $this->formatKId($idArr);
    }

    /**
     * 根据id查询物流号
     * @param $idArr
     * @return array|mixed[]
     */
    public function getLogisticsNoByKId($idArr)
    {
        if (empty($idArr)) return [];
        $idArr = $this->formatKId($idArr, 'reduce');
        $data = TchipSale\ClientSaleKyptTableModel::query()
            ->where('delete_time', 0)
            ->whereIn('id', $idArr)
            ->pluck('logistics_no', 'id')->toArray();
        return $this->formatKId($data, 'add', true);
    }

    /**
     * 格式化数组的开源平台销售单id
     * @param $data
     * @param $type
     * @return array|int[]|string[]
     */

    public function formatKId($data, $type = 'add', $formatKey = false)
    {
        $result = [];
        foreach ($data as $k => $v) {
            if ($type == 'add') {

                if ($formatKey) {
                    $result['Kypt_' . $k] = $v;
                } else {
                    $result[$k] = 'Kypt_' . $v;
                }
            } else {
                if ($formatKey) {
                    $s = explode('_', $k);
                    if (isset($s[1])) {
                        $result[$s[1]] = $v;
                    }
                } else {
                    $s = explode('_', $v);
                    $result[$k] = $s[1] ?? 0;
                }

            }
        }

        return $result;
    }

    /**
     * 根据料号查询相关客户
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return array
     */
    public function getClientByProductCode(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        //如果没有料号，则返回空
        if (empty($filter['linkage.material'])) {
            return [];
        }
//        unset($filter['linkage.material']);
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, ClientSaleKyptTableModel::query());
        //关键字查询
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('client_table.name', 'like', '%' . $keywords . '%')
                    ->orWhere('client_contact_table.comy', 'like', '%' . $keywords . '%')
                    ->orWhere('client_contact_table.contact', 'like', '%'. $keywords . '%');
            });
        }
        // 构建基础查询
        $query = $query
            ->select([
                'client_sale_kypt_table.client_id',
                'client_sale_kypt_table.prod_id',
                Db::raw('SUM(dwin_client_sale_kypt_table.num) as num_sum'),
                Db::raw('SUM(dwin_client_sale_kypt_table.money) as money_sum'),
                'client_table.id',
                'client_table.name as client_name',
                'client_table.type',
                'client_table.level',
                'client_contact_table.comy as company_name',
                'client_contact_table.contact as contact_name',
                'client_contact_table.email',
                Db::raw('group_concat(dwin_client_sale_kypt_table.prod_id) as prod_ids'),
                // 'linkage.text as product_name',
                'level_linkage.code as level_code'
            ])
            ->join('client_table', 'client_sale_kypt_table.client_id', '=', 'client_table.id')
            ->leftJoin('client_contact_table', 'client_table.id', '=', 'client_contact_table.client_id')
            ->join('linkage', 'client_sale_kypt_table.size_id', '=', 'linkage.id')
            ->leftJoin('linkage as level_linkage', 'client_table.level', '=', 'level_linkage.id')
            ->where('client_table.delete_time', 0)
            ->where('linkage.delete_time', 0)
            ->where('linkage.type', 1)
            ->groupBy('client_sale_kypt_table.client_id');



        // 执行分页查询
        $result = $query->orderBy('money_sum','desc')->orderBy($sort, $order)->paginate($limit)->toArray();

        //汇总产品id，查询产品
        $prodIdsArr = array_column($result['data'], 'prod_ids');
        $prodIdsArr = unique_filter(explode(',', implode(',', $prodIdsArr)));
        $prodList = $prodIdsArr ? LinkageModel::query()->whereIn('id', $prodIdsArr)->pluck('text', 'id')->toArray() : [];
        foreach ($result['data'] as &$item) {
            //把客户公司和联系人公司合并
            if (!empty($item['company_name'])) {
                // 有公司名：公司名 / （客户名 或 联系人）
                $item['company_client_name'] = $item['company_name'] . '/' . (!empty($item['client_name']) ? $item['client_name'] : $item['contact_name']);
            } else {
                // 没有公司名：（客户名 或 空）/ 联系人
                $item['company_client_name'] = (!empty($item['client_name']) ? $item['client_name'] : '') . '/' . $item['contact_name'];
            }
//            $item['company_client_name'] = $item['company_name'] . ' / ' . $item['client_name'];
            $iProdId = unique_filter(explode(',', $item['prod_ids']));
            $iProdName = '';
            foreach ($iProdId as $prodId) {
                $iProdName .= $prodList[$prodId] . ',';
            }
            $item['product_name'] = trim($iProdName, ',');
        }

        return $result;
    }
}