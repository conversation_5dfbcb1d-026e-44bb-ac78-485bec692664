<?php

namespace App\Core\Services\TchipSale;

use App\Constants\DataBaseCode;
use App\Constants\TchipSaleCode;
use App\Core\Services\BusinessService;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class LinkageService extends BusinessService
{

    /**
     * @Inject
     * @var LinkageModel
     */
    protected $model;





    /**
     * 根据产品料号获得产品的平台ID平台名称，产品ID产品名称，规格ID，规格名称
     * @param string $material
     * @return array [platform_id, prod_id, size_id, platform_name, prod_name, size_name]
     */
    public function getProductByMaterial($material)
    {
        $result = [
            'platform_id'   => 0,
            'prod_id'       => 0,
            'size_id'       => 0,
            'platform_name' => '',
            'prod_name'     => '',
            'size_name'     => '',
        ];
        $material = trim($material);
        $pro = $this->model->where(['material' => $material, 'status' => 1])->first();
        if ($pro) {
            $pro = $pro->toArray();
            if (!empty($pro['_parentId'])) {
                switch ($pro['_parentId']) {
                    case self::PJ_ID :
                        $result['prod_id']       = $pro['id'];
                        $result['platform_id']   = $pro['_parentId'];
                        $result['platform_name'] = $this->model->platformList[$pro['_parentId']];
                        $result['prod_name']     = $pro['pre_name'] ?? $pro['text'];
                        break;
                    case 328:
                    case 329:
                    case 330:
                    case 331:
                        $result['prod_id']       = $pro['id'];
                        $result['platform_id']   = self::PJ_ID;
                        $result['platform_name'] = $this->model->platformList[self::PJ_ID];
                        $result['prod_name']     = $pro['pre_name'] ?? $pro['text'];
                        break;
                    default:
                        $parentId                = $pro['_parentId'];
                        $parent                  = $this->model->where('text', function ($query) use ($parentId) {
                            $query->select('text')
                                ->from('linkage')
                                ->where('linkage.id', $parentId)
                                ->limit(1);
                        })->where('_parentId', '>', 0)->first();
                        $parent                  = $parent ? $parent->toArray() : [];
                        $result['platform_id']   = $parent['_parentId'] ?? 0;
                        $result['prod_id']       = $parent['id'] ?? 0;
                        $result['size_id']       = $pro['id'];
                        $result['platform_name'] = $this->model->platformList[$parent['_parentId']] ?? '';
                        $result['prod_name']     = $parent['text'] ?? '';
                        $result['size_name']     = $pro['pre_name'] ?? $pro['text'];
                }
            }
        }
        return $result;
    }

    /**
     * 获得来销售源单销售平台数据
     * @param int $source null:所有 1:国内单,2:国外单
     * @return void
     */
    public function saleSource($source = null)
    {
        // 外单列表
        $exterior = [
            'jingwairenminbifukuan', // 境外人民币付款
            'dalumeijinzhanghu', // 大陆美金
            'yinxingzhanghumeijin', //香港美金
            'sumaitong', // 速卖通
            'paypal',   // Paypal
            'yamaxun', // 亚马逊
            'yinxingzhanghuduisiwaidan'
        ];
        $saleSource = [];

        $salePlatform = $this->model->where(
            '_parentId',
            Db::connection(DataBaseCode::TCHIP_SALE)->table('linkage')->where('code', 'xiaoshoupingtai')->value('id')
        )->get();
        $salePlatform = $salePlatform ? $salePlatform->toArray() : [];

        if ($source) {
            foreach ($salePlatform as $sale){
                if($source == 2){
                    if($sale['code'] == 'shangcheng' || in_array($sale['code'], $exterior)){
                        $saleSource[] = $sale;
                    }
                }else{
                    if($sale['code'] == 'shangcheng' || !in_array($sale['code'], $exterior)){
                        $saleSource[] = $sale;
                    }
                }
            }
        } else {
            $saleSource = $salePlatform;
        }

        return $saleSource;
    }

    /**
     * 整机分类ID列表
     * @return void
     */
    public function getOpensoureCategoryComputerIds()
    {
        return [TchipSaleCode::KYPT_CATE_INDUSTRIAL_COMPUTER_ID, TchipSaleCode::KYPT_CATE_STATIONPC_ID];
    }

    /**
     * * 根据产品名称列表获取产品列表
     * @param array $names 产品名称列表
     * @return array
     */
    public function getProductsByNames(array $names) :array
    {
        $names = implode(',', $names);
        $filter = [
            'text' => $names,
            '_parentId' => 0,
        ];
        $op = [
            'text' => 'IN',
            '_parentId' => '>',
        ];
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999);
        $list = $query->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * 根据产品名称列表获取产品ID列表
     * @param array $names 产品名称列表
     * @return array
     */
    public function getProductsIdsByNames(array $names) :array
    {
        $products = $this->getProductsByNames($names);
        return $products ? array_column($products, 'id') : [];
    }

    /**
     * 返回选项列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return array
     */
    public function getOptions(array $filter = [], array $op = [], string $sort = 'sort', string $order = 'ASC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order,999);
        $list = $query->select(['id','text as name','code'])->orderBy($sort, $order)->get();
        return $list ? $list->toArray() : [];
    }
}