<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2022/5/31 上午10:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\FireflyBBS;

use App\Core\Services\BaseService;
use App\Core\Utils\Pinyin;
use App\Model\TchipBi\OaBbsFollowModel;
use App\Model\TchipBi\UserModel;
use Hyperf\Di\Annotation\Inject;

/**
 * firefly平台帖子服务类
 */
class TrackingService extends \App\Core\Services\BusinessService
{

    /**
     * @Inject
     * @var OaBbsFollowModel
     */
    public $model;
    /**
     * firefly平台帖子处理人 列表
     * @return array
     */
    public function processor()
    {
        /**
         *  具体参考 数据表：bi_user_department
         *  1:天启科技       2:销售部         3:营销策划部     4:综合部
         *  5:产品部         6:财务部        8:Linux软件部     9:Android软件部
         *  10:总经办        11:硬件研发中心  12:硬件设计组     13:工程组             14:生产组
         *  17:验证测试组     18:深圳天启     19:采购部
         *  20:互联网软件部   21:仓库部       22:中山萤火       23:其他（待设置部门）   24:Station软件组
         *  25:Android软件组 26:系统软件研发中心
         */
        $departmentIds = ["8", "9", "11", "12", "13", "17", "20", "24", "25","26"];
        $res           = UserModel::query()->select(['id', 'workwx_userid', 'name', 'status', 'department'])
            ->whereJsonLength('department', '>=', 1)//刷选 有部门的
            ->where(function ($query) use ($departmentIds) {
                if ($departmentIds) {
                    $departmentIds = is_array($departmentIds) ? $departmentIds : explode(',', $departmentIds);
                    foreach ($departmentIds as $key => $val) {
                        if ($key == 0) {
                            $query->whereJsonContains('department', (int)$val);
                        } else {
                            $query->orWhereJsonContains('department', (int)$val);
                        }
                    }
                }
            })
            ->where('status', 1)//激活状态的
            ->orderBy('email')
            ->orderBy('workwx_userid')
            ->get();

        /** @var UserModel $val */
        foreach ($res as $val) {
            $val->setHidden(['status', 'status_text']);
            $val->setAttribute('initial', Pinyin::get($val->getAttribute('name'), true));
        }
        return $res->toArray();
    }


    //public function notice($content,$to_user){
    //    $result = $this->workWxMessageService->sendText($content, $to_user);
    //    return $result;
    //}

}