<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/20 上午9:33
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index\Quota;

use App\Core\Services\Project\IssueService;
use Hyperf\Di\Annotation\Inject;

class Issue
{
    /**
     * @Inject()
     * @var IssueService
     */
    protected $service;

    public function toDoCount()
    {
        return $this->service->toDoCount();
    }

    public function watcherCount()
    {
        return $this->service->watcherCount();
    }

    public function myCreateCount()
    {
        return $this->service->userCreateCount();
    }
}