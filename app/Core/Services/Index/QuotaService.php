<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/15 下午2:37
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index;

use App\Constants\StatusCode;
use App\Core\Services\Index\Quota\QuotaManager;
use App\Core\Utils\Tree;
use App\Exception\AppException;
use App\Model\TchipBi\AuthGroupModel;
use App\Model\TchipBi\UserModel;
use App\Model\User\User;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\AuthService;
use App\Model\TchipBi\IndexQuotaViewModel;
use Qbhy\HyperfAuth\AuthManager;

class QuotaService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var IndexQuotaViewModel
     */
    protected $model;

    public function doEdit(int $id, array $values)
    {
        if (isset($values['sort'])) {
            $values['sort'] = (int) $values['sort'];
        }
        return parent::doEdit($id, $values); // TODO: Change the autogenerated stub
    }

    /**
     * 指标树形列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return array
     */
    public function getTreeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 0);
        $rows = $query->orderBy($sort, $order)->get();
        $rows = $rows ? $rows->toArray() : [];
        $rows = make(Tree::class)->getTreeList($rows, 0, 'pid');
        return $rows;
    }

    /**
     * 获取角色可显示的所有指标列表
     * @return void
     */
    public function roleQuotaList($roleId = null)
    {
        if ($roleId) {
            $group = AuthGroupModel::query()->find($roleId);
            $views = $group->index_quota ?? [];
        } else {
            if ($this->authService->isSuper()) {
                $views = 'all';
            } else {
                $groups  = $this->authService->getRole();
                $views   = $groups ? array_filter(array_column($groups, 'index_quota')) : [];
                $views   = $views ? array_unique(array_merge([], ...$views)) : [];
            }
        }
        if ($views) {
            if ($views === 'all') {
                $views   = $this->model::query()->where('status', 1)->get();
                $views = $views ? $views->toArray() : [];
            } else {
                $views   = $this->model::query()->whereIn('id', $views)->where('status', 1)->orderBy('sort', 'desc')->get();
                $views   = $views ? $views->toArray() : [];
                $pids    = array_column($views, 'pid');
                $parents = $this->model::query()->whereIn('id', $pids)->where('status', 1)->orderBy('sort', 'desc')->get();
                $views   = array_merge($views, ($parents ? $parents->toArray() : []));
            }
        }
        if ($views) {
            $pids    = array_column($views, 'pid');
            $minId   = min($pids);
            $views   = make(Tree::class)->getTreeList($views, $minId, 'pid', 'sort');
        }
        return $views;
    }

    /**
     * 角色默认显示的指标
     * @return array
     */
    public function roleQuotaDefaultList()
    {
        $groups  = $this->authService->getRole();
        $defaultQuota   = $groups ? array_filter(array_column($groups, 'index_quota_default')) : [];
        $defaultQuota   = $defaultQuota ? array_unique(array_merge([], ...$defaultQuota)) : [];
        if ($defaultQuota) {
            $views = $this->model::query()->whereIn('id', $defaultQuota)->where('status', 1)->get();
            $views = $views ? $views->toArray() : [];
            $pids    = array_column($views, 'pid');
            $parents = $this->model::query()->whereIn('id', $pids)->where('status', 1)->orderBy('sort', 'desc')->get();
            $views   = array_merge($views, ($parents ? $parents->toArray() : []));
        }
        if ($views) {
            $pids    = array_column($views, 'pid');
            $minId   = min($pids);
            $views   = make(Tree::class)->getTreeList($views, $minId, 'pid', 'sort');
        }
        return $views;
    }

    public function doEditMyQuota($ids)
    {
        $user = UserModel::query()->find( $this->auth->user()->getId());
        if (!$user) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.User_exception'));
        }
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        foreach ($ids as &$id) {
            $id = (int) $id;
        }
        $user->index_quota = $ids;
        return $user->save();

    }

    /**
     * 获取用户显示的指标
     * @return void
     */
    public function myQuotaView()
    {
        $uid          = $this->auth->user()->getId();
        $myViews      = UserModel::query()->where('id', $uid)->where('status', 1)->value('index_quota');
        $groups       = $this->authService->getRole($uid);
        // 角色可以显示的
        $views        = $groups ? array_filter(array_column($groups, 'index_quota')) : [];
        $views        = $views ? array_unique(array_merge([], ...$views)): [];
        // 角色默认显示的
        $defaultViews = $groups ? array_filter(array_column($groups, 'index_quota_default')) : [];
        $defaultViews = $defaultViews ? array_unique(array_merge([], ...$defaultViews)) : [];

        // 我制定的 或者 默认显示的
        $myViews = $myViews ?: $defaultViews;
        if (!$this->authService->isSuper()) {
            // 不是超管，只匹配角色可以查看的
            $myViews = array_intersect($myViews, $views);
        }

        // 最多显示6个
        // $myViews  = array_splice($myViews, 0, 6);
        // 按数组顺序进行排序
        $viewsSort = array_flip($myViews);
        if ($myViews) {
            $myViews = array_filter($myViews);
            $myViews = IndexQuotaViewModel::query()->whereIn('id', $myViews)->where('status', 1)->get();
            $myViews = $myViews ? $myViews->toArray() : [];
            foreach ($myViews as &$view) {
                $view['value'] = null;
                if (!empty($view['drive']) && !empty($view['method'])) {
                    $view['value'] = make(QuotaManager::class, [$view['drive'], $view['method']])->getQuota();
                }
                unset($view['method']);
                unset($view['drive']);
                $viewsSort[$view['id']] = $view;
            }
        } else {
            $viewsSort = [];
        }
        return $viewsSort ? array_values($viewsSort) : [];
    }
}