<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/31 下午2:38
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index;

use App\Constants\DataBaseCode;
use App\Constants\ProjectCode;
use App\Exception\AppException;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\ProjectsExtModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use League\HTMLToMarkdown\HtmlConverter;
use Swoole\Coroutine\Channel;

class PublicService extends \App\Core\Services\BusinessService
{
    /**
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @param $page
     * @return \Hyperf\Utils\Collection|\Tightenco\Collect\Support\Collection
     */
    public function globalSearchOld($keywords = null, $filter = [], $op = [], $sort = 'created_at', $order = 'desc', $limit = 10, $page = 1)
    {
        if (!empty($filter['search'])) {
            $search = $filter['search'];
            unset($filter['search']);
        } else {
            $search = 'all';
        }

        if ($search == 'all') {
            $search = ['issue', 'product', 'project'];
        } else {
            $search = explode(',', $search);
        }
        // 获取redmineUid
        $uid = null;
        try {
            $uid = getRedmineUserId();
        } catch (\Qbhy\HyperfAuth\Exception\GuardException $e) {

        } catch (\Qbhy\HyperfAuth\Exception\UserProviderException $e) {

        }
        $result = [];

        // 事项
        if (in_array('issue', $search)) {
            $issueChannel    = new \Swoole\Coroutine\Channel();
            co(function () use ($issueChannel, $keywords, $filter, $op, $sort, $order, $uid) {
                try {
                    $keywords2 = null;

                    // 对类似 #7377 的输入进行处理, 需要搜索id为7377的事项
                    if (preg_match('/^#\d+$/', $keywords )) {
                        $keywords2 = substr($keywords, 1);
                    }

                    if (!empty($filter['projects.id'])) {
                        $filter['project_id'] = $filter['projects.id'];
                        unset($filter['projects.id']);
                    }

                    if (!$keywords && empty($filter)) {
                        // 没有筛选依句时不查询
                        $rows = [];
                    } else {
                        $projectTable = 'projects';
                        $watcherTable = 'projects_watchers';
                        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 1, IssueModel::query());

                        $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = issues.project_id and user_id = {$uid}" : '';
                        $rows = $query->selectRaw("'issue' as type, issues.id, subject as name, {$projectTable}.id as project_id, issues.description, projects.name as project_name, issues.tracker_id, issues.parent_id, issues.status_id, issues.category_id, issues.created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''))
                            ->with(['categoryText', 'issueStatus'])
                            ->leftJoin($projectTable, 'projects.id', '=', 'issues.project_id');
                        if ($keywords) {
                            $rows->where(function ($query) use ($keywords) {
                                $query->where('subject', 'like', "%{$keywords}%")
                                    ->orWhereExists(function ($query) use ($keywords) {
                                        $issuesTable = 'issues';
                                        $journalsTable = 'journals';
                                        $query->select()->from($journalsTable)->whereRaw("{$issuesTable}.id = {$journalsTable}.journalized_id")
                                            ->where('journalized_type', 'Issue')
                                            ->where('notes', 'LIKE', "%{$keywords}%");
                                    })
                                    ->orWhereExists(function ($query) use ($keywords) {
                                        $issuesTable = 'issues';
                                        $userTable = 'users';
                                        $query->select()->from($userTable)->whereRaw("{$issuesTable}.assigned_to_id = {$userTable}.id and CONCAT({$userTable}.lastname, {$userTable}.firstname) like '%$keywords%'");
                                    })
                                    ->orWhere('issues.description', 'like', "%{$keywords}%")
                                    ->orWhere('issues.id', $keywords2 ?? $keywords);
                            });
                            // $rows = $rows->where('subject', 'like', "%{$keywords}%")
                            //     ->orWhereExists(function ($query) use ($keywords) {
                            //         $issuesTable = 'issues';
                            //         $journalsTable = 'journals';
                            //         $query->select()->from($journalsTable)->whereRaw("{$issuesTable}.id = {$journalsTable}.journalized_id")
                            //             ->where('journalized_type', 'Issue')
                            //             ->where('notes', 'LIKE', "%{$keywords}%");
                            //     })
                            //     ->orWhereExists(function ($query) use ($keywords) {
                            //         $issuesTable = 'issues';
                            //         $userTable = 'users';
                            //         $query->select()->from($userTable)->whereRaw("{$issuesTable}.assigned_to_id = {$userTable}.id and CONCAT({$userTable}.lastname, {$userTable}.firstname) like '%$keywords%'");
                            //     })
                            //     ->orWhere('issues.description', 'like', "%{$keywords}%")
                            //     ->orWhere('issues.id', $keywords2 ?? $keywords);
                        }
                        // 24.3.30修改为优先显示关注项目的事项
                        $rows = $rows->orderBy('is_watcher', $order)->orderBy('project_id', 'DESC')->orderBy($sort, $order)->get();
                        $rows = $rows ? $rows->toArray() : [];
                        $parentIds = array_filter(array_column($rows, 'parent_id'));
                        if (count($parentIds) > 0) {
                            $parents = IssueModel::query()->whereIn('id', $parentIds)->get();
                            $parents = $parents ? array_column($parents->toArray(), null, 'id') : [];
                            foreach ($rows as &$row) {
                                if ($row['parent_id'] && !empty($parents[$row['parent_id']])) {
                                    $row['parent_issue'] = $parents[$row['parent_id']];
                                } else {
                                    $row['parent_issue'] = null;
                                }
                            }
                        }
                    }


                } catch (AppException $e) {
                    $rows = [];
                }
                $issueChannel->push($rows);
            });

            $result[0] = $issueChannel->pop() ?? [];
        }

        if (in_array('product', $search)) {
            $productChannel  = new \Swoole\Coroutine\Channel();
            // 产品搜索
            co(function () use ($productChannel, $keywords, $filter, $op, $sort, $order, $uid) {
                if (!empty($filter['assigned_to_id'])) {
                    unset($filter['assigned_to_id']);
                }
                $projectTable = 'projects';
                $projectExtTable = 'projects_ext';
                $projectInfoTable = 'projects_info';
                $watcherTable = 'projects_watchers';
                list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 1, ProjectModel::query());
                $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = id and user_id = {$uid}" : '';
                $rows = $query->selectRaw("'product' as type, {$projectTable}.id, name, description, icon, {$projectInfoTable}.img, {$projectTable}.created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''))
                    ->join($projectExtTable, "{$projectTable}.id", '=', "{$projectExtTable}.project_id")
                    ->join($projectInfoTable, "{$projectTable}.id", '=', "{$projectInfoTable}.project_id");

                if ($keywords) {
                    $rows = $rows->where(function ($query) use ($keywords, $projectExtTable) {
                        $query->where("{$projectExtTable}.project_type", ProjectCode::PROJECT_TYPE_PROD)
                            ->where('name', 'like', "%{$keywords}%")
                            ->orWhereExists(function ($query) use ($keywords) {
                                $projectTable = 'projects';
                                $progressTable = 'projects_progress';
                                $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                                    ->where('description', 'LIKE', "%{$keywords}%");
                            })
                            ->orWhere('description', 'like', "%{$keywords}%");
                    });
                    // $rows = $rows->where("{$projectExtTable}.project_type", ProjectCode::PROJECT_TYPE_PROD)
                    //     ->where('name', 'like', "%{$keywords}%")
                    //     ->orWhereExists(function ($query) use ($keywords) {
                    //         $projectTable = 'projects';
                    //         $progressTable = 'projects_progress';
                    //         $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                    //             ->where('description', 'LIKE', "%{$keywords}%");
                    //     })
                    //     ->orWhere('description', 'like', "%{$keywords}%");
                }
                $rows = $rows->orderBy('is_watcher', $order)->orderBy($sort, $order)->get();
                $rows = $rows ? $rows->toArray() : [];
                $productChannel->push($rows);
            });
            $result[1] = $productChannel->pop() ?? [];
        }

        // 项目
        if (in_array('project', $search)) {
            $projectChannel  = new \Swoole\Coroutine\Channel();
            co(function () use ($projectChannel, $keywords, $filter, $op, $sort, $order, $uid) {
                if (!empty($filter['assigned_to_id'])) {
                    unset($filter['assigned_to_id']);
                }
                try {
                    $projectTable = 'projects';
                    $projectExtTable = 'projects_ext';
                    $watcherTable = 'projects_watchers';
                    list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 1, ProjectModel::query());
                    $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = id and user_id = {$uid}" : '';
                    $rows = $query->selectRaw("'project' as type, id, name, description, created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''));
                    if ($keywords) {
                        $rows = $rows->where(function ($query) use ($projectTable, $projectExtTable) {
                            $productType = ProjectCode::PROJECT_TYPE_PROD;
                            $query->whereRaw("not exists(select id from {$projectExtTable} where {$projectExtTable}.project_id = {$projectTable}.id) or exists(select id from {$projectExtTable} where {$projectExtTable}.project_id = $projectTable.id and {$projectExtTable}.project_type <> '{$productType}')");
                            return $query;
                        })
                            ->where(function ($query) use ($keywords) {
                                $query->where('name', 'LIKE', "%{$keywords}%")
                                    ->orWhere('description', 'LIKE', "%{$keywords}%")
                                    ->orWhereExists(function ($query) use ($keywords) {
                                        $projectTable = 'projects';
                                        $progressTable = 'projects_progress';
                                        $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                                            ->where('description', 'LIKE', "%{$keywords}%");
                                        return $query;
                                    });
                                return $query;
                            });
                    }

                    $rows = $rows->orderBy($sort, $order)->get();
                    $rows = $rows ? $rows->toArray() : [];
                    $ids = array_column($rows, 'id');
                    $exts = ProjectsExtModel::query()->whereIn('project_id',$ids)->get();
                    $exts = $exts ? array_column($exts->toArray(), null, 'project_id') : [];
                    foreach ($rows as &$row) {
                        if (!empty($exts[$row['id']]['icon']) ) {
                            $row['icon'] = $exts[$row['id']]['icon'];
                        } else {
                            $row['icon'] = null;
                        }
                    }
                } catch (AppException $e) {
                    $rows = [];
                }
                $projectChannel->push($rows);
            });
            $result[2] = $projectChannel->pop() ?? [];
        }

        // 合并结果
        $result = array_merge([], ...$result);
        $total = count($result);

        // 计算起始索引
        $index = ($page - 1) * $limit;

        // 分页
        $pagedData = array_slice($result, $index, $limit);

        // 计算当前页的信息
        $lastPage = ceil($total / $limit);

        // 构建分页信息数组
        return [
            "current_page" => $page,
            "data"         => $pagedData,
            "from"         => $index + 1,
            "last_page"    => $lastPage,
            "per_page"     => $limit,
            "to"           => min($index + $limit, $total),
            "total"        => $total
        ];


        // 文化，暂时不需要
        // co(function () use ($tchipbbsChannel, $keywords) {
        //     if ($keywords) {
        //         try {
        //             $rows = CmsArchivesModel::query()->selectRaw("id, title as name, description")->where('title', 'like', "%{$keywords}%")
        //                 ->orWhere('description', 'like', "%{$keywords}%")->get();
        //         } catch (AppException $e) {
        //             $rows =null;
        //         }
        //     } else {
        //         $rows = [];
        //     }
        //     $tchipbbsChannel->push($rows ? $rows->toArray() : []);
        // });
        // $result['product'] = [
        //     'name' => '产品',
        //     'data' => $productChannel->pop()
        // ];
        // $result['project'] = [
        //     'name' => '项目',
        //     'data' => $projectChannel->pop()
        // ];
        // $result['issue'] = [
        //     'name' => '事项',
        //     'data' => $issueChannel->pop()
        // ];
        // $result['tchipbbs'] = [
        //     'name' => '文化',
        //     'data' => $tchipbbsChannel->pop()
        // ];
        // return $result;
    }

    public function globalSearch($keywords = null, $filter = [], $op = [], $sort = 'created_at', $order = 'desc', $limit = 10, $page = 1)
    {
        if (!empty($filter['search'])) {
            $search = $filter['search'];
            unset($filter['search']);
        } else {
            $search = 'all';
        }

        if ($search == 'all') {
            $search = [
                'issue',
                'product',
                'project'
            ];
        } else {
            $search = explode(',', $search);
        }
        // 获取redmineUid
        $uid = null;
        try {
            $uid = getRedmineUserId();
        } catch (\Qbhy\HyperfAuth\Exception\GuardException $e) {

        } catch (\Qbhy\HyperfAuth\Exception\UserProviderException $e) {

        }
        //建立协程通道
        $channel = new Channel(count($search));
        $result = [];
        $keywordArr = explode(' ', trim($keywords));
        foreach ($keywordArr as &$keyword) {
            $keyword = trim($keyword);
        }
        $keywordArr = array_unique($keywordArr);
        // 事项使用Db类获取数据来优化接口，如果需要额外信息，请分页后再获取
        if (in_array('issue', $search)) {
            co(function () use ($channel, $keywordArr, $filter, $op, $sort, $order, $uid) {
                try {
                    if (!empty($filter['projects.id'])) {
                        $filter['project_id'] = $filter['projects.id'];
                        unset($filter['projects.id']);
                    }

                    if (!$keywordArr && empty($filter)) {
                        // 没有筛选依句时不查询
                        $result = [];
                    } else {
                        //由于使用模型构造器无法过滤掉$append导致查询过慢,故用Db类来拼装
                        $redmineDb = Db::connection(DataBaseCode::TCHIP_REDMINE);
                        $projectTable = 'projects';
                        $watcherTable = 'projects_watchers';
                        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 1, Db::connection(DataBaseCode::TCHIP_REDMINE)->table('issues'));
                        $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = issues.project_id and user_id = {$uid}" : '';
                        $rows = $query->selectRaw("'issue' as type, issues.id, subject as name, {$projectTable}.id as project_id, issues.description, projects.name as project_name, issues.tracker_id, issues.parent_id, issues.status_id, issues.category_id, issues.created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''))
                            ->leftJoin($projectTable, 'projects.id', '=', 'issues.project_id');
                        if ($keywordArr) {
                            //记录多种情况下的issue的id集合
                            $issuesIdArr = [];
                            //id搜索
                            $idKeywordArr = [];
                            foreach ($keywordArr as $keyword) {
                                // 对类似 #7377 的输入进行处理, 需要搜索id为7377的事项
                                if (preg_match('/^#\d+$/', $keyword)) {
                                    $idKeywordArr[] = substr($keyword, 1);
                                }
                            }
                            if ($idKeywordArr) {
                                $idsArr = $redmineDb->table('issues')->whereIn('id', $idKeywordArr)->pluck('id')->toArray();
                                $issuesIdArr = array_merge($issuesIdArr, $idsArr);
                            }
                            $rows = $rows->where(function ($query) use ($keywordArr, $issuesIdArr,$redmineDb) {
                                //事项标题和内容关键字查询
                                $query->orwhere(function ($query) use ($keywordArr) {
                                    foreach ($keywordArr as $keyword) {
                                        $query->where(Db::raw("CONCAT(issues.subject,issues.description)"), 'like', "%{$keyword}%");
                                    }
                                });
                                //用户关键字查询
                                $assignUseridArr = [];
                                foreach ($keywordArr as $keyword) {
                                    $user_id = $redmineDb->table('users')->where(Db::raw('CONCAT(lastname, firstname) '), 'like', "%{$keyword}%")->value('id');
                                    if ($user_id) $assignUseridArr[] = $user_id;
                                }
                                //事项说明关键字查询
                                $query->orWhereExists(function ($query) use ($keywordArr) {
                                    $issuesTable = 'issues';
                                    $journalsTable = 'journals';
                                    $query->select()->from($journalsTable)->whereRaw("{$issuesTable}.id = {$journalsTable}.journalized_id")
                                        ->where('journalized_type', 'Issue')
                                        ->where(function ($query) use ($keywordArr) {
                                            foreach ($keywordArr as $keyword) {
                                                $query->where('notes', 'like', "%{$keyword}%");
                                            }
                                        });
                                });
                                if ($issuesIdArr) {
                                    $query->orWhereIn('issues.id', $issuesIdArr);
                                }
                                if ($assignUseridArr) {
                                    $query->orWhereIn('assigned_to_id', $assignUseridArr);
                                }
                            });
                        }

                        $rows = $rows->orderBy('is_watcher', $order)->orderBy('project_id', 'DESC')->orderBy($sort, $order)->get();
                        $result = [];
                        foreach ($rows as $row){
                            $result[] = (array)$row;
                        }
                    }


                } catch (AppException $e) {
                    $result = [];
                }
                $channel->push($result);
            });
        }

        if (in_array('product', $search)) {
            // 产品搜索
            co(function () use ($channel, $keywords, $filter, $op, $sort, $order, $uid) {
                if (!empty($filter['assigned_to_id'])) {
                    unset($filter['assigned_to_id']);
                }
                $projectTable = 'projects';
                $projectExtTable = 'projects_ext';
                $projectInfoTable = 'projects_info';
                $watcherTable = 'projects_watchers';
                list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 1, Db::connection(DataBaseCode::TCHIP_REDMINE)->table('projects'));
                $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = id and user_id = {$uid}" : '';
                $rows = $query->selectRaw("'product' as type, {$projectTable}.id, name, description, icon, {$projectInfoTable}.img, {$projectTable}.created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''))
                    ->join($projectExtTable, "{$projectTable}.id", '=', "{$projectExtTable}.project_id")
                    ->join($projectInfoTable, "{$projectTable}.id", '=', "{$projectInfoTable}.project_id");

                if ($keywords) {
                    $rows = $rows->where(function ($query) use ($keywords, $projectExtTable) {
                        $query->where("{$projectExtTable}.project_type", ProjectCode::PROJECT_TYPE_PROD)
                            ->where('name', 'like', "%{$keywords}%")
                            ->orWhereExists(function ($query) use ($keywords) {
                                $projectTable = 'projects';
                                $progressTable = 'projects_progress';
                                $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                                    ->where('description', 'LIKE', "%{$keywords}%");
                            })
                            ->orWhere('description', 'like', "%{$keywords}%");
                    });
                    // $rows = $rows->where("{$projectExtTable}.project_type", ProjectCode::PROJECT_TYPE_PROD)
                    //     ->where('name', 'like', "%{$keywords}%")
                    //     ->orWhereExists(function ($query) use ($keywords) {
                    //         $projectTable = 'projects';
                    //         $progressTable = 'projects_progress';
                    //         $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                    //             ->where('description', 'LIKE', "%{$keywords}%");
                    //     })
                    //     ->orWhere('description', 'like', "%{$keywords}%");
                }
                $rows = $rows->with(['categoryText', 'issueStatus'])->orderBy('is_watcher', $order)->orderBy($sort, $order)->get();
                $rows = $rows ? $rows->toArray() : [];
                $channel->push($rows);
            });
//            $result[1] = $productChannel->pop() ?? [];
        }

        // 项目
        if (in_array('project', $search)) {
            co(function () use ($channel, $keywords, $filter, $op, $sort, $order, $uid) {
                if (!empty($filter['assigned_to_id'])) {
                    unset($filter['assigned_to_id']);
                }
                try {
                    $projectTable = 'projects';
                    $projectExtTable = 'projects_ext';
                    $watcherTable = 'projects_watchers';
                    list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 1, ProjectModel::query());
                    $watcherSql = $uid ? "SELECT id FROM {$watcherTable} where project_id = id and user_id = {$uid}" : '';
                    $rows = $query->selectRaw("'project' as type, id, name, description, created_on" . ($watcherSql ? ", IF(($watcherSql), 1, 0) as is_watcher" : ''));
                    if ($keywords) {
                        $rows = $rows->where(function ($query) use ($projectTable, $projectExtTable) {
                            $productType = ProjectCode::PROJECT_TYPE_PROD;
                            $query->whereRaw("not exists(select id from {$projectExtTable} where {$projectExtTable}.project_id = {$projectTable}.id) or exists(select id from {$projectExtTable} where {$projectExtTable}.project_id = $projectTable.id and {$projectExtTable}.project_type <> '{$productType}')");
                            return $query;
                        })
                            ->where(function ($query) use ($keywords) {
                                $query->where('name', 'LIKE', "%{$keywords}%")
                                    ->orWhere('description', 'LIKE', "%{$keywords}%")
                                    ->orWhereExists(function ($query) use ($keywords) {
                                        $projectTable = 'projects';
                                        $progressTable = 'projects_progress';
                                        $query->select()->from($progressTable)->whereRaw("{$projectTable}.id = {$progressTable}.project_id")
                                            ->where('description', 'LIKE', "%{$keywords}%");
                                        return $query;
                                    });
                                return $query;
                            });
                    }

                    $rows = $rows->orderBy($sort, $order)->get();
                    $rows = $rows ? $rows->toArray() : [];
                    $ids = array_column($rows, 'id');
                    $exts = ProjectsExtModel::query()->whereIn('project_id', $ids)->get();
                    $exts = $exts ? array_column($exts->toArray(), null, 'project_id') : [];
                    foreach ($rows as &$row) {
                        if (!empty($exts[$row['id']]['icon'])) {
                            $row['icon'] = $exts[$row['id']]['icon'];
                        } else {
                            $row['icon'] = null;
                        }
                    }
                } catch (AppException $e) {
                    $rows = [];
                }
                $channel->push($rows);
            });
        }
        // 收集所有协程的结果
        for ($i = 0; $i < count($search); $i++) {
            $result[] = $channel->pop();
        }
        // 合并结果
        $result = array_merge([], ...$result);
        $total = count($result);

        // 计算起始索引
        $index = ($page - 1) * $limit;

        // 分页
        $pagedData = array_slice($result, $index, $limit);
        //处理分页的数据
        foreach ($pagedData as &$item) {
            if($item['type'] == 'issue'){
                $item['category_text'] = $item['category_id']?CategoryModel::find($item['category_id']):null;
                if (!empty($item['description'])) {
                    if (hasHtmlTags($item['description']) && !isMarkdown($item['description'])) {
                        $converter = make(HtmlConverter::class);
                        $value =  $converter->convert($item['description']);
                    } else {
                        $value = $item['description'];
                    }
                    $item['description'] = $value ? handleMarkdownImg($value, $this->attributes['id'] ?? '') : $value;
                    $item['parent_issue'] = $item['parent_id']?IssueModel::query()->find($item['parent_id']):null;
                }
            }
        }

        // 计算当前页的信息
        $lastPage = ceil($total / $limit);

        // 构建分页信息数组
        return [
            "current_page" => $page,
            "data" => $pagedData,
            "from" => $index + 1,
            "last_page" => $lastPage,
            "per_page" => $limit,
            "to" => min($index + $limit, $total),
            "total" => $total
        ];
    }
}