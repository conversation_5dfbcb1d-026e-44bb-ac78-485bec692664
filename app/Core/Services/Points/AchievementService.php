<?php

declare(strict_types=1);

namespace App\Core\Services\Points;

use App\Model\Points\AchievementModel;
use App\Model\Points\UserAchievementModel;
use App\Core\Services\BaseService;
use App\Core\Services\Points\LevelService;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Hyperf\Logger\LoggerFactory;

/**
 * 成就服务类
 */
class AchievementService extends BaseService
{
    /**
     * @Inject
     * @var LevelService
     */
    protected LevelService $levelService;

    /**
     * @Inject
     * @var LoggerFactory
     */
    protected LoggerFactory $loggerFactory;

    /**
     * 获取用户成就列表
     */
    public function getUserAchievements(int $userId, array $params = []): array
    {
        // 检查是否需要初始化用户成就
        if ($this->needsInitialization($userId)) {
            $this->initializeUserAchievements($userId);
        }

        $pointService = make(PointService::class);
        $userPoints = $pointService->getUserPoints($userId);
        $userLevel = $userPoints ? $userPoints->level : 1;

        $query = AchievementModel::scopes(['active', 'valid', 'unlockableForLevel' => $userLevel])
            ->with(['userAchievements' => function ($query) use ($userId) {
                $query->where('user_id', $userId);
            }]);

        // 筛选条件
        if (!empty($params['category'])) {
            $query->byCategory($params['category']);
        }

        if (!empty($params['difficulty'])) {
            $query->byDifficulty((int)$params['difficulty']);
        }

        if (isset($params['is_completed'])) {
            if ($params['is_completed']) {
                $query->whereHas('userAchievements', function ($subQuery) use ($userId) {
                    $subQuery->where('user_id', $userId)->where('is_completed', true);
                });
            } else {
                $query->whereDoesntHave('userAchievements', function ($subQuery) use ($userId) {
                    $subQuery->where('user_id', $userId)->where('is_completed', true);
                });
            }
        }

        // 是否显示隐藏成就
        if (empty($params['show_hidden'])) {
            $query->visible();
        }

        $achievements = $query->byDisplayOrder()->get();

        // 处理成就数据
        $result = $achievements->map(function ($achievement) use ($userId) {
            $userAchievement = $achievement->userAchievements->first();

            return [
                'id' => $achievement->id,
                'code' => $achievement->code,
                'name' => $achievement->name,
                'description' => $achievement->description,
                'icon' => $achievement->icon,
                'badge_image' => $achievement->badge_image,
                'category' => $achievement->category,
                'difficulty' => $achievement->difficulty,
                'condition_type' => $achievement->condition_type,
                'condition_value' => $achievement->condition_value,
                'reward_points' => $achievement->reward_points,
                'tips' => $achievement->tips,
                'is_hidden' => $achievement->is_hidden,
                'is_repeatable' => $achievement->is_repeatable,
                'display_order' => $achievement->display_order,
                'unlock_level' => $achievement->unlock_level,
                'user_progress' => $userAchievement ? [
                    'progress' => $userAchievement->progress,
                    'target_value' => $userAchievement->target_value,
                    'progress_percentage' => $userAchievement->progress_percentage,
                    'is_completed' => $userAchievement->is_completed,
                    'completed_at' => $userAchievement->completed_at,
                    'completion_count' => $userAchievement->completion_count,
                ] : [
                    'progress' => 0,
                    'target_value' => $achievement->condition_value,
                    'progress_percentage' => 0.00,
                    'is_completed' => false,
                    'completed_at' => null,
                    'completion_count' => 0,
                ],
            ];
        });

        return [
            'achievements' => $result,
            'total' => $result->count(),
            'completed_count' => $result->where('user_progress.is_completed', true)->count(),
            'in_progress_count' => $result->where('user_progress.progress', '>', 0)
                ->where('user_progress.is_completed', false)->count(),
        ];
    }

    /**
     * 获取我的成就
     */
    public function getMyAchievements(int $userId, array $params = []): array
    {
        // 检查是否需要初始化用户成就
        if ($this->needsInitialization($userId)) {
            $this->initializeUserAchievements($userId);
        }

        // 获取用户等级
        $pointService = make(PointService::class);
        $userPoints = $pointService->getUserPoints($userId);
        $userLevel = $userPoints ? $userPoints->level : 1;

        // 从成就表开始查询，左连接用户成就记录
        $query = AchievementModel::active()
            ->valid()
            ->unlockableForLevel($userLevel)
            ->with(['userAchievements' => function ($query) use ($userId) {
                $query->where('user_id', $userId);
            }]);

        // 筛选条件
        $query->when(isset($params['is_completed']) && $params['is_completed'], function ($q) use ($userId) {
            // 只显示已完成的成就
            $q->whereHas('userAchievements', function ($subQuery) use ($userId) {
                $subQuery->where('user_id', $userId)->where('is_completed', true);
            });
        })->when(isset($params['is_completed']) && !$params['is_completed'], function ($q) use ($userId) {
            // 显示进行中的成就：有记录但未完成 或 没有记录
            $q->where(function ($subQuery) use ($userId) {
                $subQuery->whereHas('userAchievements', function ($userSubQuery) use ($userId) {
                    $userSubQuery->where('user_id', $userId)->where('is_completed', false);
                })->orWhereDoesntHave('userAchievements', function ($userSubQuery) use ($userId) {
                    $userSubQuery->where('user_id', $userId);
                });
            });
        })->when(!empty($params['category']), function ($q) use ($params) {
            // 按分类筛选
            $q->byCategory($params['category']);
        })->when(empty($params['show_hidden']), function ($q) {
            // 不显示隐藏成就（默认行为）
            $q->visible();
        });

        // 排序
        $sortBy = $params['sort_by'] ?? 'display_order';
        $sortOrder = $params['sort_order'] ?? 'asc';

        $query->when($sortBy === 'completed_at', function ($q) use ($userId, $sortOrder) {
            // 优先显示已完成的，然后按完成时间排序，未完成的排在最后
            $q->orderBy(function ($query) use ($userId) {
                $query->selectRaw('COALESCE(ua.completed_at, "1970-01-01 00:00:00")')
                    ->from('user_achievements as ua')
                    ->whereColumn('ua.achievement_id', 'achievements.id')
                    ->where('ua.user_id', $userId)
                    ->limit(1);
            }, $sortOrder);
        })->when($sortBy === 'progress', function ($q) use ($userId, $sortOrder) {
            // 按进度百分比排序
            $q->orderBy(function ($query) use ($userId) {
                $query->selectRaw('COALESCE(ua.progress_percentage, 0)')
                    ->from('user_achievements as ua')
                    ->whereColumn('ua.achievement_id', 'achievements.id')
                    ->where('ua.user_id', $userId)
                    ->limit(1);
            }, $sortOrder);
        })->when($sortBy === 'display_order' || !in_array($sortBy, ['completed_at', 'progress']), function ($q) {
            // 默认按显示顺序排序
            $q->byDisplayOrder();
        });

        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 20;

        $achievements = $query->paginate($perPage, ['*'], 'page', $page);

        // 处理数据
        $data = $achievements->toArray();
        $data['data'] = collect($data['data'])->map(function ($achievement) use ($userId) {
            $userAchievement = $achievement['user_achievements'][0] ?? null;

            return [
                'id' => $userAchievement['id'] ?? null,
                'achievement_id' => $achievement['id'],
                'achievement_code' => $achievement['code'],
                'progress' => $userAchievement['progress'] ?? 0,
                'target_value' => $userAchievement['target_value'] ?? $achievement['condition_value'],
                'progress_percentage' => $userAchievement['progress_percentage'] ?? 0.00,
                'is_completed' => $userAchievement['is_completed'] ?? false,
                'completed_at' => $userAchievement['completed_at'] ?? null,
                'completion_count' => $userAchievement['completion_count'] ?? 0,
                'achievement' => [
                    'id' => $achievement['id'],
                    'code' => $achievement['code'],
                    'name' => $achievement['name'],
                    'description' => $achievement['description'],
                    'icon' => $achievement['icon'],
                    'badge_image' => $achievement['badge_image'],
                    'category' => $achievement['category'],
                    'difficulty' => $achievement['difficulty'],
                    'reward_points' => $achievement['reward_points'],
                    'tips' => $achievement['tips'],
                ],
            ];
        })->toArray();

        return $data;
    }


    /**
     * 获取成就详情和用户进度
     */
    public function getAchievementDetailWithUserProgress(int $userId, int $achievementId): array
    {
        $achievement = AchievementModel::find($achievementId);
        if (!$achievement) {
            throw new AppException(StatusCode::ERR_SERVER, '成就不存在');
        }

        $userAchievement = UserAchievementModel::byUser($userId)
            ->byAchievement($achievementId)
            ->first();

        $result = [
            'id' => $achievement->id,
            'code' => $achievement->code,
            'name' => $achievement->name,
            'description' => $achievement->description,
            'icon' => $achievement->icon,
            'badge_image' => $achievement->badge_image,
            'category' => $achievement->category,
            'difficulty' => $achievement->difficulty,
            'condition_type' => $achievement->condition_type,
            'condition_value' => $achievement->condition_value,
            'condition_config' => $achievement->condition_config,
            'reward_points' => $achievement->reward_points,
            'reward_items' => $achievement->reward_items,
            'tips' => $achievement->tips,
            'is_hidden' => $achievement->is_hidden,
            'is_repeatable' => $achievement->is_repeatable,
            'unlock_level' => $achievement->unlock_level,
            'prerequisite_achievements' => $achievement->prerequisite_achievements,
            'valid_from' => $achievement->valid_from,
            'valid_to' => $achievement->valid_to,
            'user_progress' => $userAchievement ? [
                'progress' => $userAchievement->progress,
                'target_value' => $userAchievement->target_value,
                'progress_percentage' => $userAchievement->progress_percentage,
                'is_completed' => $userAchievement->is_completed,
                'completed_at' => $userAchievement->completed_at,
                'completion_count' => $userAchievement->completion_count,
                'last_updated_at' => $userAchievement->last_updated_at,
                'metadata' => $userAchievement->metadata,
            ] : [
                'progress' => 0,
                'target_value' => $achievement->condition_value,
                'progress_percentage' => 0.00,
                'is_completed' => false,
                'completed_at' => null,
                'completion_count' => 0,
                'last_updated_at' => null,
                'metadata' => null,
            ],
        ];

        return $result;
    }

    /**
     * 获取成就排行榜
     */
    public function getAchievementRankings(array $params = []): array
    {
        $type = $params['type'] ?? 'total'; // total, monthly, weekly
        $category = $params['category'] ?? null;
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 20;

        $query = Db::table('user_achievements as ua')
            ->join('achievements as a', 'ua.achievement_id', '=', 'a.id')
            ->join('users as u', 'ua.user_id', '=', 'u.id')
            ->where('ua.is_completed', true)
            ->where('a.is_active', true);

        if ($category) {
            $query->where('a.category', $category);
        }

        // 根据类型筛选时间范围
        if ($type === 'monthly') {
            $query->whereMonth('ua.completed_at', Carbon::now()->month)
                  ->whereYear('ua.completed_at', Carbon::now()->year);
        } elseif ($type === 'weekly') {
            $query->whereBetween('ua.completed_at', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ]);
        }

        // 统计用户成就数据
        $rankings = $query->select([
                'ua.user_id',
                'u.name as user_name',
                'u.avatar as user_avatar',
                Db::raw('COUNT(ua.id) as achievement_count'),
                Db::raw('SUM(a.reward_points) as total_points'),
                Db::raw('AVG(a.difficulty) as avg_difficulty'),
                Db::raw('MAX(ua.completed_at) as latest_completed_at')
            ])
            ->groupBy('ua.user_id', 'u.name', 'u.avatar')
            ->orderBy('achievement_count', 'desc')
            ->orderBy('total_points', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return $rankings->toArray();
    }

    /**
     * 获取成就分类
     */
    public function getAchievementCategories(): array
    {
        $categories = AchievementModel::active()
            ->select('category')
            ->distinct()
            ->get()
            ->pluck('category')
            ->toArray();

        $categoryMap = [
            'article' => '文档创作',
            'interaction' => '互动交流',
            'training' => '学习培训',
            'special' => '特殊成就',
            'milestone' => '里程碑',
            'general' => '通用成就'
        ];

        $result = [];
        foreach ($categories as $category) {
            $count = AchievementModel::active()->byCategory($category)->count();
            $result[] = [
                'code' => $category,
                'name' => $categoryMap[$category] ?? $category,
                'count' => $count
            ];
        }

        return $result;
    }

    /**
     * 检查用户成就
     */
    public function checkUserAchievements(int $userId): array
    {
        $achievements = AchievementModel::active()->valid()->get();
        $unlockedAchievements = [];

        foreach ($achievements as $achievement) {
            $result = $this->checkSingleAchievement($userId, $achievement);
            if ($result['unlocked']) {
                $unlockedAchievements[] = $result;
            }
        }

        return [
            'user_id' => $userId,
            'checked_count' => $achievements->count(),
            'unlocked_count' => count($unlockedAchievements),
            'unlocked_achievements' => $unlockedAchievements
        ];
    }


    /**
     * 获取成就统计信息
     */
    public function getAchievementStatistics(): array
    {
        $totalAchievements = AchievementModel::count();
        $activeAchievements = AchievementModel::active()->count();
        $totalUserAchievements = UserAchievementModel::count();
        $completedUserAchievements = UserAchievementModel::completed()->count();

        // 按分类统计
        $categoryStats = AchievementModel::active()
            ->select('category', Db::raw('COUNT(*) as count'))
            ->groupBy('category')
            ->get()
            ->toArray();

        // 按难度统计
        $difficultyStats = AchievementModel::active()
            ->select('difficulty', Db::raw('COUNT(*) as count'))
            ->groupBy('difficulty')
            ->get()
            ->toArray();

        // 最受欢迎的成就（完成人数最多）
        $popularAchievements = Db::table('user_achievements as ua')
            ->join('achievements as a', 'ua.achievement_id', '=', 'a.id')
            ->where('ua.is_completed', true)
            ->select('a.id', 'a.name', 'a.code', Db::raw('COUNT(ua.id) as completion_count'))
            ->groupBy('a.id', 'a.name', 'a.code')
            ->orderBy('completion_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();

        // 最近完成的成就
        $recentCompletions = UserAchievementModel::completed()
            ->with(['achievement:id,name,code,icon', 'user:id,name'])
            ->orderBy('completed_at', 'desc')
            ->limit(10)
            ->get()
            ->toArray();

        return [
            'overview' => [
                'total_achievements' => $totalAchievements,
                'active_achievements' => $activeAchievements,
                'total_user_achievements' => $totalUserAchievements,
                'completed_user_achievements' => $completedUserAchievements,
                'completion_rate' => $totalUserAchievements > 0 ?
                    round(($completedUserAchievements / $totalUserAchievements) * 100, 2) : 0,
            ],
            'category_stats' => $categoryStats,
            'difficulty_stats' => $difficultyStats,
            'popular_achievements' => $popularAchievements,
            'recent_completions' => $recentCompletions,
        ];
    }

    /**
     * 创建成就
     */
    public function createAchievement(array $params): AchievementModel
    {
        return Db::transaction(function () use ($params) {
            $achievement = AchievementModel::create($params);
            
            // 为所有用户同步新成就
            $this->syncAchievementsForAllUsers([$achievement->id]);
            
            return $achievement;
        });
    }

    /**
     * 更新成就
     */
    public function updateAchievement(int $id, array $params): AchievementModel
    {
        return Db::transaction(function () use ($id, $params) {
            $achievement = AchievementModel::find($id);
            if (!$achievement) {
                throw new AppException(StatusCode::ERR_SERVER, '成就不存在');
            }

            // 检查是否需要同步用户数据
            $needsSync = false;
            if (isset($params['condition_value']) && $params['condition_value'] != $achievement->condition_value) {
                $needsSync = true;
            }
            if (isset($params['is_active']) && $params['is_active'] != $achievement->is_active) {
                $needsSync = true;
            }

            $achievement->update($params);
            
            // 如果条件值或激活状态发生变化，同步所有用户数据
            if ($needsSync) {
                $this->syncAchievementsForAllUsers([$achievement->id]);
            }
            
            return $achievement->fresh();
        });
    }

    /**
     * 删除成就
     */
    public function deleteAchievement(int $id): bool
    {
        $achievement = AchievementModel::find($id);
        if (!$achievement) {
            throw new AppException(StatusCode::ERR_SERVER, '成就不存在');
        }

        // 检查是否有用户已获得该成就
        $hasUserAchievements = UserAchievementModel::byAchievement($id)->exists();
        if ($hasUserAchievements) {
            throw new AppException(StatusCode::ERR_SERVER, '该成就已有用户获得，无法删除');
        }

        return $achievement->delete();
    }

    /**
     * 获取管理员成就列表
     */
    public function getAdminAchievementList(array $params = []): array
    {
        $query = AchievementModel::query()
            ->withCount(['userAchievements', 'userAchievements as completed_count' => function ($query) {
                $query->where('is_completed', true);
            }]);

        // 筛选条件
        if (!empty($params['category'])) {
            $query->byCategory($params['category']);
        }

        if (!empty($params['difficulty'])) {
            $query->byDifficulty((int)$params['difficulty']);
        }

        if (isset($params['is_active'])) {
            $query->where('is_active', (bool)$params['is_active']);
        }

        if (isset($params['is_hidden'])) {
            $query->where('is_hidden', (bool)$params['is_hidden']);
        }

        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->where(function ($subQuery) use ($search) {
                $subQuery->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 排序
        $sortBy = $params['sort_by'] ?? 'display_order';
        $sortOrder = $params['sort_order'] ?? 'asc';

        if ($sortBy === 'display_order') {
            $query->byDisplayOrder();
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }

        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 20;

        return $query->paginate($perPage, ['*'], 'page', $page)->toArray();
    }

    /**
     * 检查单个成就是否解锁，并更新进度信息
     * 
     * @param int $userId 用户ID
     * @param AchievementModel $achievement 成就模型
     * @return array
     */
    private function checkSingleAchievement(int $userId, AchievementModel $achievement): array
    {
        // 这里应该根据成就的条件类型来检查是否满足条件

        $userAchievement = UserAchievementModel::byUser($userId)
            ->byAchievement($achievement->id)
            ->first();

        // 如果已经完成且不可重复，则跳过
        if ($userAchievement && $userAchievement->is_completed && !$achievement->is_repeatable) {
            return ['unlocked' => false, 'reason' => 'already_completed'];
        }

        // 根据条件类型检查进度
        $progress = $this->calculateAchievementProgress($userId, $achievement);

        $isCompleted = $progress >= $achievement->condition_value;
        $progress = $progress > $achievement->condition_value ? $achievement->condition_value : $progress;
        $progressPercentage = $achievement->condition_value > 0 
            ? round(($progress / $achievement->condition_value) * 100, 2) 
            : 0;

        if ($isCompleted) {
            // 创建或更新用户成就记录
            if ($userAchievement) {
                $userAchievement->update([
                    'progress' => $progress,
                    'progress_percentage' => 100.00,
                    'is_completed' => true,
                    'completed_at' => Carbon::now(),
                    'completion_count' => $userAchievement->completion_count + 1,
                    'last_updated_at' => Carbon::now(),
                ]);
            } else {
                $userAchievement = UserAchievementModel::create([
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'achievement_code' => $achievement->code,
                    'progress' => $progress,
                    'target_value' => $achievement->condition_value,
                    'progress_percentage' => 100.00,
                    'is_completed' => true,
                    'completed_at' => Carbon::now(),
                    'completion_count' => 1,
                    'last_updated_at' => Carbon::now(),
                ]);
            }

            // 奖励积分
            if ($achievement->reward_points > 0) {
                $this->addPointsForAchievement($userId, $achievement);
            }

            return [
                'unlocked' => true,
                'achievement' => $achievement,
                'user_achievement' => $userAchievement,
                'newly_created' => !$userAchievement || $userAchievement->id === null,
            ];
        } else {
            // 即使未完成，也要更新进度信息
            $newlyCreated = false;
            if ($userAchievement) {
                $userAchievement->update([
                    'progress' => $progress,
                    'progress_percentage' => $progressPercentage,
                    'last_updated_at' => Carbon::now(),
                ]);
            } else {
                $userAchievement = UserAchievementModel::create([
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'achievement_code' => $achievement->code,
                    'progress' => $progress,
                    'target_value' => $achievement->condition_value,
                    'progress_percentage' => $progressPercentage,
                    'is_completed' => false,
                    'completed_at' => null,
                    'completion_count' => 0,
                    'last_updated_at' => Carbon::now(),
                ]);
                $newlyCreated = true;
            }

            return [
                'unlocked' => false, 
                'reason' => 'conditions_not_met',
                'user_achievement' => $userAchievement,
                'progress' => $progress,
                'progress_percentage' => $progressPercentage,
                'newly_created' => $newlyCreated,
            ];
        }
    }

    /**
     * 为成就奖励积分
     */
    private function addPointsForAchievement(int $userId, AchievementModel $achievement, string $reason = ''): void
    {
        $description = $reason ?: "获得成就：{$achievement->name}";
        
        // 注入PointService来处理积分添加
        $pointService = make(PointService::class);
        $pointService->addPoints(
            $userId,
            'achievement_reward',
            'achievement',
            $achievement->id,
            $description,
            ['source_title' => $achievement->name, 'achievement_code' => $achievement->code]
        );
        $this->loggerFactory->get('achievement')->info('为成就奖励积分', [
            'user_id' => $userId,
            'achievement_id' => $achievement->id,
            'achievement_code' => $achievement->code,
            'achievement_name' => $achievement->name,
            'reward_points' => $achievement->reward_points,
        ]);
    }

    /**
     * 初始化用户成就列表
     * 为指定用户或所有用户创建缺失的成就记录
     *
     * @param int|null $userId 用户ID，为null时处理所有用户
     * @param bool $forceUpdate 是否强制更新已存在的记录
     * @return array 初始化结果统计
     */
    public function initializeUserAchievements(?int $userId = null, bool $forceUpdate = false): array
    {
        try {
            // 获取目标用户列表
            if ($userId) {
                $users = [\App\Model\TchipBi\UserModel::find($userId)];
                if (!$users[0]) {
                    throw new AppException(StatusCode::ERR_SERVER, '用户不存在');
                }
            } else {
                // 获取所有活跃用户
                $users = \App\Model\TchipBi\UserModel::where('status', 1)->get();
            }

            // 获取所有活跃且有效的成就
            $achievements = AchievementModel::query()->scopes(['active', 'valid'])->get();

            $stats = [
                'total_users' => count($users),
                'total_achievements' => $achievements->count(),
                'created_records' => 0,
                'updated_records' => 0,
                'skipped_records' => 0,
                'errors' => [],
            ];

            foreach ($users as $user) {
                try {
                    $result = $this->initializeUserAchievementsForSingleUser($user->id, $achievements, $forceUpdate);
                    $stats['created_records'] += $result['created'];
                    $stats['updated_records'] += $result['updated'];
                    $stats['skipped_records'] += $result['skipped'];
                } catch (\Exception $e) {
                    $stats['errors'][] = [
                        'user_id' => $user->id,
                        'user_name' => $user->name ?? '',
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $stats;
        } catch (\Exception $e) {
            throw new AppException(StatusCode::ERR_SERVER, '初始化用户成就失败：' . $e->getMessage());
        }
    }

    /**
     * 为单个用户初始化成就记录
     *
     * @param int $userId 用户ID
     * @param \Illuminate\Support\Collection $achievements 成就列表
     * @param bool $forceUpdate 是否强制更新
     * @return array 处理结果统计
     */
    private function initializeUserAchievementsForSingleUser(int $userId, $achievements, bool $forceUpdate = false): array
    {
        $stats = ['created' => 0, 'updated' => 0, 'skipped' => 0, 'completed' => 0];

        // 获取用户当前等级
        $pointService = make(PointService::class);
        $userPoints = $pointService->getUserPoints($userId);
        $userLevel = $userPoints ? $userPoints->level : 1;

        foreach ($achievements as $achievement) {
            // 检查等级限制
            if ($achievement->unlock_level > $userLevel) {
                $stats['skipped']++;
                continue;
            }

            try {
                // 复用checkSingleAchievement方法
                $result = $this->checkSingleAchievement($userId, $achievement, true);
                
                if ($result['unlocked']) {
                    $stats['completed']++;
                    // 完成的成就可能是新创建的记录
                    if (isset($result['newly_created']) && $result['newly_created']) {
                        $stats['created']++;
                    } else {
                        $stats['updated']++;
                    }
                } else {
                    // 根据返回结果判断是创建还是更新
                    if (isset($result['newly_created']) && $result['newly_created']) {
                        $stats['created']++;
                    } else {
                        $stats['updated']++;
                    }
                }
            } catch (\Exception $e) {
                // 记录错误但继续处理其他成就
                $this->loggerFactory->get('achievement')->error('初始化用户成就失败', [
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'achievement_code' => $achievement->code,
                    'error' => $e->getMessage()
                ]);
                $stats['skipped']++;
            }
        }

        return $stats;
    }

    /**
     * 检查用户是否需要初始化成就
     *
     * @param int $userId 用户ID
     * @return bool 是否需要初始化
     */
    public function needsInitialization(int $userId): bool
    {
        // 获取用户当前等级
        $pointService = make(PointService::class);
        $userPoints = $pointService->getUserPoints($userId);
        $userLevel = $userPoints ? $userPoints->level : 1;

        // 获取用户可解锁的成就数量
        $availableAchievementsCount = AchievementModel::scopes(['active', 'valid', 'unlockableForLevel' => $userLevel])->count();

        // 获取用户已有的成就记录数量
        $userAchievementsCount = UserAchievementModel::scopes(['byUser' => $userId])->count();

        return $userAchievementsCount < $availableAchievementsCount;
    }

    /**
     * 同步所有用户的成就记录
     * 主要用于成就新增或修改后的批量同步
     *
     * @param array $achievementIds 指定的成就ID列表，为空时处理所有成就
     * @return array 同步结果统计
     */
    public function syncAchievementsForAllUsers(array $achievementIds = []): array
    {
        try {
            // 获取所有活跃用户
            $users = \App\Model\TchipBi\UserModel::where('status', 1)->pluck('id');

            // 获取要同步的成就
            $query = AchievementModel::scopes(['active', 'valid']);
            if (!empty($achievementIds)) {
                $query->whereIn('id', $achievementIds);
            }
            $achievements = $query->get();

            $stats = [
                'total_users' => $users->count(),
                'total_achievements' => $achievements->count(),
                'processed_users' => 0,
                'created_records' => 0,
                'updated_records' => 0,
                'errors' => [],
            ];

            foreach ($users as $userId) {
                try {
                    $result = $this->syncAchievementsForSingleUser($userId, $achievements);
                    $stats['created_records'] += $result['created'];
                    $stats['updated_records'] += $result['updated'];
                    $stats['processed_users']++;
                } catch (\Exception $e) {
                    $stats['errors'][] = [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $stats;
        } catch (\Exception $e) {
            throw new AppException(StatusCode::ERR_SERVER, '同步用户成就失败：' . $e->getMessage());
        }
    }

    /**
     * 为单个用户同步指定成就
     *
     * @param int $userId 用户ID
     * @param \Illuminate\Support\Collection $achievements 成就列表
     * @return array 处理结果统计
     */
    private function syncAchievementsForSingleUser(int $userId, $achievements): array
    {
        $stats = ['created' => 0, 'updated' => 0];

        // 获取用户当前等级
        $pointService = make(PointService::class);
        $userPoints = $pointService->getUserPoints($userId);
        $userLevel = $userPoints ? $userPoints->level : 1;

        foreach ($achievements as $achievement) {
            // 检查等级限制
            if ($achievement->unlock_level > $userLevel) {
                continue;
            }

            $existingRecord = UserAchievementModel::
                scopes(['byUser' => $userId, 'byAchievement' => $achievement->id])
                ->first();

            if (!$existingRecord) {
                // 创建新记录
                UserAchievementModel::create([
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'achievement_code' => $achievement->code,
                    'progress' => 0,
                    'target_value' => $achievement->condition_value,
                    'progress_percentage' => 0.00,
                    'is_completed' => false,
                    'completed_at' => null,
                    'notified_at' => null,
                    'is_displayed' => true,
                    'completion_count' => 0,
                    'last_updated_at' => Carbon::now(),
                    'metadata' => null,
                ]);
                $stats['created']++;
            } elseif ($existingRecord->target_value != $achievement->condition_value) {
                // 更新目标值
                $existingRecord->update([
                    'target_value' => $achievement->condition_value,
                    'achievement_code' => $achievement->code,
                    'last_updated_at' => Carbon::now(),
                ]);
                $stats['updated']++;
            }
        }

        return $stats;
    }

    /**
     * 按条件类型检查用户成就
     */
    public function checkUserAchievementsByCondition(int $userId, string $conditionType): array
    {
        // 获取指定条件类型的成就
        $achievements = AchievementModel::active()
            ->where('condition_type', $conditionType)
            ->get();

        $results = [];
        
        foreach ($achievements as $achievement) {
            $result = $this->checkSingleAchievement($userId, $achievement);
            
            if ($result['unlocked']) {
                $results[] = [
                    'newly_unlocked' => true,
                    'achievement_id' => $achievement->id,
                    'achievement_code' => $achievement->code,
                    'achievement_name' => $achievement->name,
                    'reward_points' => $achievement->reward_points,
                    'unlocked_at' => $result['user_achievement']->completed_at->toDateTimeString(),
                    'user_achievement' => $result['user_achievement'],
                    'achievement' => $achievement
                ];
            }
        }

        return $results;
    }

    /**
     * 增强的计算成就进度方法
     */
    private function calculateAchievementProgress(int $userId, AchievementModel $achievement): int
    {
        try {
            switch ($achievement->condition_type) {
                case 'article_count':
                    // 计算发布文档数量
                    return $this->calculateArticleCount($userId);
                
                case 'like_count':
                    // 计算获得点赞数量
                    return $this->calculateLikeCount($userId);
                
                case 'essence_count':
                    // 计算精华文档数量（被置顶的文档）
                    return $this->calculateEssenceCount($userId);
                
                case 'total_views':
                    // 计算文档总阅读量
                    return $this->calculateTotalViews($userId);
                
                case 'total_points':
                    // 计算总积分（基于知识豆）
                    $pointService = make(PointService::class);
                    $userPoints = $pointService->getUserPoints($userId);
                    return $userPoints ? $userPoints->knowledge_beans : 0;
                
                case 'consecutive_days':
                    // 计算连续登录天数
                    return $this->calculateConsecutiveDays($userId);
                
                case 'daily_points':
                    // 计算日积分
                    return $this->calculateDailyPoints($userId);
                
                case 'weekly_points':
                    // 计算周积分
                    return $this->calculateWeeklyPoints($userId);
                
                case 'monthly_points':
                    // 计算月积分
                    return $this->calculateMonthlyPoints($userId);
                
                default:
                    return 0;
            }
        } catch (\Exception $e) {
            // 记录错误但不中断流程
            $logger = make(\Psr\Log\LoggerInterface::class);
            $logger->error('计算成就进度失败', [
                'user_id' => $userId,
                'achievement_id' => $achievement->id,
                'condition_type' => $achievement->condition_type,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 计算用户发布的文档数量
     */
    private function calculateArticleCount(int $userId): int
    {
        return Db::table('wiki_documents')
            ->where('created_by', $userId)
            ->whereNull('deleted_at')  // 使用软删除字段，而不是 is_deleted
            ->count();
    }

    /**
     * 计算用户获得的点赞数量
     */
    private function calculateLikeCount(int $userId): int
    {
        return Db::table('wiki_documents as wd')
            ->join('wiki_likes as wl', 'wd.doc_id', '=', 'wl.doc_id')
            ->where('wd.created_by', $userId)
            ->whereNull('wd.deleted_at')  // 只统计未删除文档的点赞
            ->whereNull('wl.deleted_at')  // 只统计未删除的点赞记录
            ->count();
    }

    /**
     * 计算用户精华文档数量（被置顶的文档）
     * 包括当前置顶的文档和曾经被置顶超过1天的文档
     */
    private function calculateEssenceCount(int $userId): int
    {
        return Db::table('wiki_documents')
            ->where('created_by', $userId)
            ->where(function ($query) {
                $query->where('is_pinned', 1)  // 当前置顶的文档
                      ->orWhere('ever_pinned', 1);  // 曾经被置顶超过1天的文档
            })
            ->whereNull('deleted_at')  // 只统计未删除的文档
            ->count();
    }

    /**
     * 计算用户文档总阅读量
     */
    private function calculateTotalViews(int $userId): int
    {
        $sum = Db::table('wiki_documents')
            ->where('created_by', $userId)
            ->whereNull('deleted_at')
            ->sum('view_count');

        return (int) ($sum ?? 0);
    }

    /**
     * 计算连续登录天数
     */
    private function calculateConsecutiveDays(int $userId): int
    {
        // 这里需要根据用户登录记录来计算
        // 暂时返回0，需要根据具体的用户登录日志表来实现
        return 0;
    }

    /**
     * 计算日积分
     */
    private function calculateDailyPoints(int $userId): int
    {
        return Db::table('point_records')
            ->where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereDate('created_at', Carbon::today())
            ->sum('point_change') ?? 0;
    }

    /**
     * 计算周积分
     */
    private function calculateWeeklyPoints(int $userId): int
    {
        return Db::table('point_records')
            ->where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->sum('point_change') ?? 0;
    }

    /**
     * 计算月积分
     */
    private function calculateMonthlyPoints(int $userId): int
    {
        return Db::table('point_records')
            ->where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('point_change') ?? 0;
    }

}