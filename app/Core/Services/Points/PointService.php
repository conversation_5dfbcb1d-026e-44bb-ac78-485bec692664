<?php

declare(strict_types=1);

namespace App\Core\Services\Points;

use App\Core\Utils\Log;
use App\Model\Points\AchievementModel;
use App\Model\Points\UserPointsModel;
use App\Model\Points\PointRecordModel;
use App\Model\Points\PointConfigModel;
use App\Model\Points\PointOperationLogModel;
use App\Core\Services\BaseService;
use App\Core\Utils\PointTypeManager;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WikiDocumentModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Hyperf\Logger\LoggerFactory;
use Psr\EventDispatcher\EventDispatcherInterface;
use App\Event\Points\PointsChangedEvent;
use Psr\Container\ContainerInterface;
use App\Core\Services\Points\LevelService;

/**
 * 积分服务类
 */
class PointService extends BaseService
{
    /**
     * @Inject
     * @var LoggerFactory
     */
    protected LoggerFactory $loggerFactory;

    /**
     * @Inject
     * @var ContainerInterface
     */
    protected ContainerInterface $container;



    /**
     * 为用户添加积分
     */
    public function addPoints(
        int $userId,
        string $pointType,
        string $sourceType,
        ?int $sourceId = null,
        string $description = '',
        array $context = []
    ): array {
        return Db::transaction(function () use ($userId, $pointType, $sourceType, $sourceId, $description, $context) {
            // 获取积分配置
            $config = PointConfigModel::where('action_type', $pointType)
                ->where('is_active', true)
                ->first();

            if (!$config) {
                throw new AppException(StatusCode::ERR_SERVER, "积分配置不存在: {$pointType}");
            }

            // 检查是否达到每日限制
            if ($this->isReachedDailyLimit($config, $userId)) {
                $this->loggerFactory->get('points')->error('已达到今日积分获取限制', [
                    'user_id' => $userId,
                    'point_type' => $pointType,
                    'source_type' => $sourceType,
                    'source_id' => $sourceId,
                    'description' => $description,
                    'context' => $context,
                    'error' => '已达到今日积分获取限制'
                ]);
                return [
                    'success' => false,
                    'message' => '已达到今日积分获取限制',
                    'points_added' => 0
                ];
            }

            // 获取或创建用户积分记录
            $userPoints = $this->getUserPoints($userId, true);

            // 计算实际积分
            $pointData = $this->calculateActualPoints($config, $userId, $context);
            $finalPoints = $pointData['final_points'];

            if ($finalPoints <= 0) {
                $this->loggerFactory->get('points')->error('积分计算结果为0或负数，本次操作积分不变', [
                    'user_id' => $userId,
                    'point_type' => $pointType,
                    'source_type' => $sourceType,
                    'source_id' => $sourceId,
                    'description' => $description,
                    'context' => $context,
                    'error' => '积分计算结果为0或负数'
                ]);
                return [
                    'success' => false,
                    'message' => '积分计算结果为0或负数',
                    'points_added' => 0
                ];
            }

            // 检查并重置周期性积分
            $this->resetPeriodPointsIfNeeded($userPoints);
            
            // 重新获取用户积分信息（因为可能已被重置）
            $userPoints->refresh();
            
            // 更新用户积分
            $oldPoints = $userPoints->current_points;
            $newPoints = $oldPoints + $finalPoints;

            $userPoints->increment('current_points', $finalPoints);
            $userPoints->increment('total_points', $finalPoints);
            $userPoints->increment('daily_points', $finalPoints);
            $userPoints->increment('weekly_points', $finalPoints);
            $userPoints->increment('monthly_points', $finalPoints);
            $userPoints->increment('year_points', $finalPoints);
            $userPoints->update([
                'last_points_at' => Carbon::now(),
                'version' => $userPoints->version + 1
            ]);

            // 创建积分记录
            $pointRecord = PointRecordModel::create([
                'user_id' => $userId,
                'point_type' => $pointType,
                'point_change' => $finalPoints,
                'current_points' => $newPoints,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'source_title' => $context['source_title'] ?? '',
                'description' => $description,
                'multiplier' => $pointData['multiplier'],
                'bonus_reason' => $pointData['bonus_reason'],
                'is_daily_first' => $this->isDailyFirst($config, $userId),
                'created_by' => $context['operator_id'] ?? null,
            ]);

            // 记录操作日志
            PointOperationLogModel::create([
                'user_id' => $userId,
                'operation_type' => 'add',
                'before_points' => $oldPoints,
                'after_points' => $newPoints,
                'point_change' => $finalPoints,
                'operation_reason' => $description,
                'operator_type' => !empty($context['operator_id']) ? 'user' : 'system',
                'operator_id' => !empty($context['operator_id']) ? $context['operator_id'] : null,
                'related_record_id' => $pointRecord->id,
            ]);

            make(\Psr\EventDispatcher\EventDispatcherInterface::class)->dispatch(
                new PointsChangedEvent(
                    $userId,
                    $pointType,
                    $finalPoints,
                    $sourceType,
                    $sourceId,
                    $description,
                    $context
                )
            );

            return [
                'success' => true,
                'message' => '积分添加成功',
                'points_added' => $finalPoints,
                'current_points' => $newPoints,
                'point_record_id' => $pointRecord->id
            ];
        });
    }

    /**
     * 扣除用户积分（增强版：应用配置规则和时效限制）
     */
    public function deductPoints(
        int $userId,
        int $points,
        string $reason,
        string $sourceType = 'manual',
        ?int $sourceId = null,
        ?int $operatorId = null,
        string $pointType = 'deduct_points',
        bool $validateSource = false,
        ?string $originalSourceType = null,
        ?int $originalSourceId = null
    ): array {
        return Db::transaction(function () use ($userId, $points, $reason, $sourceType, $sourceId, $operatorId, $pointType, $validateSource, $originalSourceType, $originalSourceId) {
            $points = abs($points);
            $userPoints = $this->getUserPoints($userId, true);

            // 验证积分类型是否存在
            if (!PointTypeManager::isValidPointType($pointType)) {
                $this->loggerFactory->get('points')->error('无效的积分类型', [
                    'point_type' => $pointType,
                    'user_id' => $userId,
                    'operator_id' => $operatorId
                ]);
                throw new AppException(StatusCode::ERR_SERVER, "无效的积分类型: {$pointType}");
            }

            // 源验证逻辑
            $originalPointRecord = null;
            if ($validateSource) {
                $originalPointRecord = $this->validateAndFindOriginalRecord(
                    $userId, 
                    $points, 
                    $originalSourceType, 
                    $originalSourceId, 
                    $pointType
                );
            }
            
            // 记录详细的操作日志
            $this->loggerFactory->get('points')->info('积分扣除操作开始', [
                'user_id' => $userId,
                'points' => $points,
                'point_type' => $pointType,
                'point_type_name' => PointTypeManager::getPointTypeName($pointType),
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'reason' => $reason,
                'operator_id' => $operatorId,
                'current_points_before' => $userPoints->current_points,
            ]);

            $oldPoints = $userPoints->current_points;
            $newPoints = $oldPoints - $points;

            // 检查并重置周期性积分
            $this->resetPeriodPointsIfNeeded($userPoints);
            
            // 重新获取用户积分信息（因为可能已被重置）
            $userPoints->refresh();

            // 更新用户积分

            $userPoints->decrement('current_points', $points);
            $userPoints->increment('consumed_points', $points);
            $userPoints->decrement('daily_points', $points);
            $userPoints->decrement('weekly_points', $points);
            $userPoints->decrement('monthly_points', $points);
            $userPoints->decrement('year_points', $points);
            $userPoints->update([
                'version' => $userPoints->version + 1,
                'last_points_at' => Carbon::now()
            ]);

            // 创建积分记录，包含撤销配置信息
            $pointRecord = PointRecordModel::create([
                'user_id' => $userId,
                'point_type' => $pointType,
                'point_change' => -$points,
                'current_points' => $newPoints,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'source_title' => $this->extractSourceTitle($sourceType, $sourceId, $reason),
                'description' => $reason,
                'created_by' => $operatorId,
                'is_reversed' => false,
                'multiplier' => 1.0,
            ]);

            // 记录详细的操作日志
            PointOperationLogModel::create([
                'user_id' => $userId,
                'operation_type' => 'subtract',
                'before_points' => $oldPoints,
                'after_points' => $newPoints,
                'point_change' => -$points,
                'operation_reason' => $reason,
                'operator_type' => $operatorId ? 'admin' : 'system',
                'operator_id' => $operatorId,
                'related_record_id' => $pointRecord->id,
            ]);

            // 如果启用了源验证，标记原始记录为已撤销
            if ($validateSource && $originalPointRecord) {
                $originalPointRecord->update([
                    'is_reversed' => true,
                    'reversed_at' => Carbon::now(),
                    'reversed_reason' => "积分被撤销：{$reason}"
                ]);

                $this->loggerFactory->get('points')->info('原始积分记录已标记为撤销', [
                    'original_record_id' => $originalPointRecord->id,
                    'deduction_record_id' => $pointRecord->id,
                    'user_id' => $userId,
                    'points' => $points,
                    'reason' => $reason
                ]);
            }

            // 记录成功的操作日志
            $this->loggerFactory->get('points')->info('积分扣除操作成功', [
                'user_id' => $userId,
                'points_deducted' => $points,
                'point_type' => $pointType,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'point_record_id' => $pointRecord->id,
                'before_points' => $oldPoints,
                'after_points' => $newPoints,
                'operator_id' => $operatorId,
            ]);

            // 记录特殊情况的额外日志
            if ($newPoints < 0) {
                $this->loggerFactory->get('points')->warning('用户积分变为负数', [
                    'user_id' => $userId,
                    'negative_points' => $newPoints,
                    'deduction_reason' => $reason,
                    'point_type' => $pointType,
                    'operator_id' => $operatorId
                ]);
            }

            make(\Psr\EventDispatcher\EventDispatcherInterface::class)->dispatch(
                new PointsChangedEvent(
                    $userId,
                    $pointType,
                    -$points, // 负数表示扣除
                    $sourceType,
                    $sourceId,
                    $reason,
                    ['operator_id' => $operatorId]
                )
            );

            return [
                'success' => true,
                'message' => '积分扣除成功',
                'points_deducted' => $points,
                'current_points' => $newPoints,
                'point_record_id' => $pointRecord->id,
            ];
        });
    }

    /**
     * 获取用户积分信息
     */
    public function getUserPoints(int $userId, bool $createIfNotExists = false): ?UserPointsModel
    {
        $userPoints = UserPointsModel::where('user_id', $userId)->first();

        if (!$userPoints && $createIfNotExists) {
            $userPoints = UserPointsModel::create([
                'user_id' => $userId,
                'current_points' => 0,
                'total_points' => 0,
                'consumed_points' => 0,
                'level' => 1,
                'level_name' => '青铜智者',
            ]);
            $this->calculateHistoryPoints($userId);
        }
        return $userPoints;
    }

    /**
     * 获取用户积分记录
     */
    public function getUserPointRecords(int $userId, array $params = []): array
    {
        $query = PointRecordModel::where('user_id', $userId)
            // ->where('is_reversed', false)
            ->latest('id');

        // 筛选条件
        if (!empty($params['point_type'])) {
            $query->where('point_type', $params['point_type']);
        }

        if (!empty($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 20;

        return $query->paginate($perPage, ['*'], 'page', $page)->toArray();
    }

    /**
     * 获取用户积分统计
     */
    public function getUserPointStatistics(int $userId): array
    {
        $userPoints = $this->getUserPoints($userId);
        if (!$userPoints) {
            return [
                'current_points' => 0,
                'total_points' => 0,
                'consumed_points' => 0,
                'today_points' => 0,
                'week_points' => 0,
                'month_points' => 0,
                'year_points' => 0,
                'rank' => 0,
                'level' => 1,
                'level_name' => '青铜智者'
            ];
        }

        // 计算排名
        $rank = UserPointsModel::where('current_points', '>', $userPoints->current_points)->count() + 1;

        return [
            'current_points' => $userPoints->current_points,
            'total_points' => $userPoints->total_points,
            'consumed_points' => $userPoints->consumed_points,
            'today_points' => $userPoints->daily_points,
            'week_points' => $userPoints->weekly_points,
            'month_points' => $userPoints->monthly_points,
            'year_points' => $userPoints->year_points,
            'rank' => $rank,
            'level' => $userPoints->level,
            'level_name' => $userPoints->level_name
        ];
    }

    /**
     * 获取积分排行榜
     */
    public function getPointRankings(array $params = []): array
    {
        $type = $params['type'] ?? 'current'; // current, total, daily, weekly, monthly


        $query = UserPointsModel::with('user:id,name,avatar');

        // 排序逻辑
        $pointField = $this->getPointFieldByType($type);
        $query->when($type === 'total', function ($q) {
            $q->orderBy('total_points', 'desc');
        })->when($type === 'daily', function ($q) {
            $q->orderBy('daily_points', 'desc');
        })->when($type === 'weekly', function ($q) {
            $q->orderBy('weekly_points', 'desc');
        })->when($type === 'monthly', function ($q) {
            $q->orderBy('monthly_points', 'desc');
        })->when(!in_array($type, ['total', 'daily', 'weekly', 'monthly']), function ($q) {
            $q->orderBy('current_points', 'desc');
        });

        // 添加次要排序确保排名稳定性
        $query->orderBy('user_id', 'asc');

        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 20;
        $rankings = $query->paginate($perPage, ['*'], 'page', $page);

        // 处理数据并计算正确的全局排名
        $data = $rankings->toArray();
        $data['data'] = collect($data['data'])->map(function ($item, $index) use ($type, $params, $pointField) {
            return [
                'rank' => ($params['page'] - 1)*$params['per_page'] + $index + 1, // 计算全局排名
                'user_id' => $item['user_id'],
                'username' => $item['user']['name'] ?? '',
                'avatar' => $item['user']['avatar'] ?? '',
                'points' => $item[$pointField],
                'level' => $item['level'],
                'level_name' => $item['level_name']
            ];
        })->toArray();

        return $data;
    }

    /**
     * 根据类型获取对应的积分字段名
     */
    private function getPointFieldByType(string $type): string
    {
        switch ($type) {
            case 'total':
                return 'total_points';
            case 'daily':
                return 'daily_points';
            case 'weekly':
                return 'weekly_points';
            case 'monthly':
                return 'monthly_points';
            default:
                return 'current_points';
        }
    }


    /**
     * 批量初始化用户积分
     */
    public function batchInitUserPoints(array $userIds): array
    {
        $results = [];
        
        foreach ($userIds as $userId) {
            try {
                $userPoints = $this->getUserPoints($userId, true);
                $results[] = [
                    'user_id' => $userId,
                    'success' => true,
                    'points' => $userPoints->current_points
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取我的积分详细信息
     */
    public function getMyPointsDetail(int $userId): array
    {
        $userPoints = $this->getUserPoints($userId);
        if (!$userPoints) {
            $this->batchInitUserPoints([$userId]);
            $userPoints = $this->getUserPoints($userId);
        }

        $levelService = $this->container->get(LevelService::class);
        $levelInfo = $levelService->getUserLevelInfo($userId);
        $statistics = $this->getUserPointStatistics($userId);

        return [
            'current_points' => $userPoints->current_points,
            'total_points' => $userPoints->total_points,
            'consumed_points' => $userPoints->consumed_points,
            'daily_points' => $userPoints->daily_points,
            'weekly_points' => $userPoints->weekly_points,
            'monthly_points' => $userPoints->monthly_points,
            'year_points' => $userPoints->year_points,
            'level' => $levelInfo['level'],
            'level_name' => $levelInfo['level_name'],
            'level_title' => $levelInfo['level_title'],
            'level_progress' => $levelInfo['level_progress'],
            'next_level_points' => $levelInfo['next_level_points'],
            'next_level_name' => $levelInfo['next_level_name'],
            'rank' => $statistics['rank'],
            'color' => $levelInfo['color'],
            'icon' => $levelInfo['icon'],
            'badge_image' => $levelInfo['badge_image'],
            'privileges' => $levelInfo['privileges']
        ];
    }

    /**
     * 管理员添加积分
     */
    public function addPointsByAdmin(int $userId, int $points, string $reason, int $operatorId, string $pointType = 'manual_add'): array
    {
        return $this->addPoints(
            $userId,
            $pointType,
            'manual',
            null,
            $reason,
            ['operator_id' => $operatorId, 'points' => $points]
        );
        
    }

    /**
     * 管理员扣除积分
     */
    public function deductPointsByAdmin(int $userId, int $points, string $reason, int $operatorId): array
    {
        return $this->deductPoints($userId, $points, $reason, 'manual', null, $operatorId, 'manual_deduct');
    }

    /**
     * 获取积分配置列表
     */
    public function getPointConfigs(): array
    {
        return PointConfigModel::byPriority()
            ->get()
            ->map(function ($config) {
                return [
                    'id' => $config->id,
                    'action_type' => $config->action_type,
                    'action_name' => $config->action_name,
                    'points' => $config->points,
                    'max_daily_count' => $config->max_daily_count,
                    'max_daily_points' => $config->max_daily_points,
                    'first_time_bonus' => $config->first_time_bonus,
                    'category' => $config->category,
                    'priority' => $config->priority,
                    'description' => $config->description,
                    'is_active' => $config->is_active
                ];
            })
            ->toArray();
    }

    /**
     * 更新积分配置
     */
    public function updatePointConfig(int $id, array $params): array
    {
        $config = PointConfigModel::find($id);
        if (!$config) {
            throw new AppException(StatusCode::ERR_SERVER, '积分配置不存在');
        }

        $config->update($params);
        return $config->toArray();
    }

    /**
     * 获取用户积分详情（管理员功能）
     */
    public function getUserPointDetail(int $userId): array
    {
        $userPoints = $this->getUserPoints($userId);
        if (!$userPoints) {
            throw new AppException(StatusCode::ERR_SERVER, '用户积分信息不存在');
        }

        $levelService = $this->container->get(LevelService::class);
        $levelInfo = $levelService->getUserLevelInfo($userId);
        $statistics = $this->getUserPointStatistics($userId);
        
        // 获取最近的积分记录
        $recentRecords = $this->getUserPointRecords($userId, [
            'page' => 1,
            'per_page' => 10
        ]);

        return [
            'user_points' => $userPoints->toArray(),
            'level_info' => $levelInfo,
            'statistics' => $statistics,
            'recent_records' => $recentRecords
        ];
    }

    /**
     * 获取管理员积分统计信息
     */
    public function getAdminPointStatistics(array $params = []): array
    {
        // 分页参数
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $pageSize = isset($params['pageSize']) ? (int)$params['pageSize'] : 20;
        
        // 以用户表为主表，左连接积分表
        $query = UserModel::query()
            ->where('user.status', 1)
            ->leftJoin('user_points as up', 'user.id', '=', 'up.user_id')
            ->select([
                'user.id as user_id',
                'user.name as username',
                'user.avatar',
                // 对积分表字段进行空值处理，提供默认值
                Db::raw('COALESCE(bi_up.current_points, 0) as current_points'),
                Db::raw('COALESCE(bi_up.total_points, 0) as total_points'),
                Db::raw('COALESCE(bi_up.consumed_points, 0) as consumed_points'),
                Db::raw('COALESCE(bi_up.weekly_points, 0) as weekly_points'),
                Db::raw('COALESCE(bi_up.monthly_points, 0) as monthly_points'),
                Db::raw('COALESCE(bi_up.year_points, 0) as year_points'),
                Db::raw('COALESCE(bi_up.daily_points, 0) as daily_points'),
                Db::raw('COALESCE(bi_up.level, 1) as level'),
                Db::raw("COALESCE(bi_up.level_name, '青铜智者') as level_name"),
                'up.last_points_at'
            ]);
        
        // 用户名搜索
        if (!empty($params['username'])) {
            $query->where('user.name', 'like', '%' . $params['username'] . '%');
        }
        
        // 积分范围筛选（需要处理空值情况）
        if (!empty($params['minPoints'])) {
            $query->where(Db::raw('COALESCE(bi_up.current_points, 0)'), '>=', (int)$params['minPoints']);
        }
        if (!empty($params['maxPoints'])) {
            $query->where(Db::raw('COALESCE(bi_up.current_points, 0)'), '<=', (int)$params['maxPoints']);
        }
        
        // 等级筛选（需要处理空值情况）  
        if (!empty($params['level'])) {
            $query->where(Db::raw('COALESCE(bi_up.level, 1)'), '=', (int)$params['level']);
        }
        
        // 处理排序
        $this->applySorting($query, $params);
        
        // 分页查询
        $result = $query->paginate($pageSize, ['*'], 'page', $page);
        
        // 处理用户数据
        $users = $result->getCollection()->map(function ($user, $index) use ($page, $pageSize) {
            return [
                'user_id' => $user->user_id,
                'username' => $user->username ?: '用户' . $user->user_id,
                'avatar' => $user->avatar ?: '',
                'current_points' => (int)$user->current_points,
                'total_points' => (int)$user->total_points,
                'consumed_points' => (int)$user->consumed_points,
                'weekly_points' => (int)$user->weekly_points,
                'monthly_points' => (int)$user->monthly_points,
                'year_points' => (int)$user->year_points,
                'level' => (int)$user->level,
                'level_name' => $user->level_name,
                'daily_points' => (int)$user->daily_points,
                'last_points_at' => $user->last_points_at ? Carbon::parse($user->last_points_at)->toDateTimeString() : null,
                'rank' => ($page - 1) * $pageSize + $index + 1, // 计算全局排名
            ];
        });
        
        // 统计信息（如果是第一页才计算，减少查询负担）
        $overview = null;
        if ($page === 1) {
            // 统计所有用户（包括没有积分记录的）
            $totalUsers = UserModel::query()->where('status', 1)->count();
            
            // 统计积分相关数据（只计算有积分记录的用户）
            $totalCurrentPoints = UserPointsModel::sum('current_points');
            $totalWeeklyPoints = UserPointsModel::sum('weekly_points');
            $totalMonthlyPoints = UserPointsModel::sum('monthly_points');
            $totalYearPoints = UserPointsModel::sum('year_points');
            
            // 今日积分变动
            $todayRecords = PointRecordModel::whereDate('created_at', Carbon::now()->toDateString())
                ->get();
            
            $todayEarned = $todayRecords->where('point_change', '>', 0)->sum('point_change');
            $todayConsumed = abs($todayRecords->where('point_change', '<', 0)->sum('point_change'));
            
            $overview = [
                'total_users' => $totalUsers,
                'total_current_points' => $totalCurrentPoints,
                'total_weekly_points' => $totalWeeklyPoints,
                'total_monthly_points' => $totalMonthlyPoints,
                'total_year_points' => $totalYearPoints,
                'today_earned' => $todayEarned,
                'today_consumed' => $todayConsumed
            ];
        }
        
        return [
            'active_users' => $users->toArray(),
            'pagination' => [
                'current_page' => $result->currentPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
                'last_page' => $result->lastPage(),
                'from' => $result->firstItem(),
                'to' => $result->lastItem(),
            ],
            'overview' => $overview,
        ];
    }

    /**
     * 应用排序规则
     */
    private function applySorting($query, array $params): void
    {
        // 获取排序字段和方向
        $sortBy = $params['sortBy'] ?? 'current_points';
        $sortOrder = $params['sortOrder'] ?? 'desc';
        
        // 确保排序方向只能是 asc 或 desc
        $sortOrder = in_array(strtolower($sortOrder), ['asc', 'desc']) ? strtolower($sortOrder) : 'desc';
        
        // 定义允许的排序字段映射
        $sortableFields = [
            'currentPoints' => 'COALESCE(bi_up.current_points, 0)',
            'weeklyPoints' => 'COALESCE(bi_up.weekly_points, 0)',
            'monthlyPoints' => 'COALESCE(bi_up.monthly_points, 0)',
            'yearPoints' => 'COALESCE(bi_up.year_points, 0)',
            'rank' => 'COALESCE(bi_up.current_points, 0)', // rank按current_points排序
        ];
        
        // 如果是有效的排序字段，应用排序
        if (isset($sortableFields[$sortBy])) {
            $query->orderBy(Db::raw($sortableFields[$sortBy]), $sortOrder);
            
            // 添加次要排序条件保证排序稳定性
            if ($sortBy !== 'currentPoints' && $sortBy !== 'rank') {
                $query->orderBy(Db::raw('COALESCE(bi_up.current_points, 0)'), 'desc');
            }
            $query->orderBy('user.id', 'asc');
        } else {
            // 默认排序：按积分降序
            $query->orderBy(Db::raw('COALESCE(bi_up.current_points, 0)'), 'desc')
                  ->orderBy('user.id', 'asc');
        }
    }

    
    // /**
    //  * 获取积分分布情况
    //  * 注释原因：移除积分概览功能，该方法暂时不需要
    //  */
    // private function getPointDistribution(): array
    // {
    //     $ranges = [
    //         ['min' => 0, 'max' => 100, 'label' => '0-100'],
    //         ['min' => 101, 'max' => 500, 'label' => '101-500'],
    //         ['min' => 501, 'max' => 1000, 'label' => '501-1000'],
    //         ['min' => 1001, 'max' => 5000, 'label' => '1001-5000'],
    //         ['min' => 5001, 'max' => null, 'label' => '5000+']
    //     ];

    //     $distribution = [];
    //     foreach ($ranges as $range) {
    //         $query = UserPointsModel::where('current_points', '>=', $range['min']);
    //         if ($range['max']) {
    //             $query->where('current_points', '<=', $range['max']);
    //         }
            
    //         $distribution[] = [
    //             'label' => $range['label'],
    //             'count' => $query->count()
    //         ];
    //     }

    //     return $distribution;
    // }

   /**
    * 重置周期性积分（如果需要）
    */
   private function resetPeriodPointsIfNeeded(UserPointsModel $userPoints): void
   {
       $now = Carbon::now();
       $updates = [];
       
       // 检查并重置日积分
       $lastDailyReset = $userPoints->last_daily_reset ? Carbon::parse($userPoints->last_daily_reset) : null;
       if (!$lastDailyReset || $lastDailyReset->toDateString() !== $now->toDateString()) {
           $updates['daily_points'] = 0;
           $updates['last_daily_reset'] = $now->toDateString();
       }
       
       // 检查并重置周积分
       $lastWeeklyReset = $userPoints->last_weekly_reset ? Carbon::parse($userPoints->last_weekly_reset) : null;
       if (!$lastWeeklyReset || $lastWeeklyReset->week !== $now->week || $lastWeeklyReset->year !== $now->year) {
           $updates['weekly_points'] = 0;
           $updates['last_weekly_reset'] = $now->toDateString();
       }
       
       // 检查并重置月积分
       $lastMonthlyReset = $userPoints->last_monthly_reset ? Carbon::parse($userPoints->last_monthly_reset) : null;
       if (!$lastMonthlyReset || $lastMonthlyReset->month !== $now->month || $lastMonthlyReset->year !== $now->year) {
           $updates['monthly_points'] = 0;
           $updates['last_monthly_reset'] = $now->toDateString();
       }
       
       // 检查并重置年积分
       $lastYearlyReset = $userPoints->last_yearly_reset ? Carbon::parse($userPoints->last_yearly_reset) : null;
       if (!$lastYearlyReset || $lastYearlyReset->year !== $now->year) {
           $updates['year_points'] = 0;
           $updates['last_yearly_reset'] = $now->toDateString();
       }
       
       // 如果有需要更新的字段，则执行更新
       if (!empty($updates)) {
           $userPoints->update($updates);
           
           // 记录重置操作日志
           $this->loggerFactory->get('points')->info('周期积分重置', [
               'user_id' => $userPoints->user_id,
               'reset_fields' => array_keys($updates),
               'reset_date' => $now->toDateString()
           ]);
       }
   }

    /**
     * 检查用户是否达到每日限制
     */
    private function isReachedDailyLimit(PointConfigModel $config, int $userId): bool
    {
        if (!$config->max_daily_count && !$config->max_daily_points) {
            return false;
        }

        $today =Carbon::now()->toDateString();

        // 获取今日该类型积分记录
        $todayRecords = PointRecordModel::where('user_id', $userId)
            ->where('point_type', $config->action_type)
            ->whereDate('created_at', $today)
            // ->where('is_reversed', false)
            ->get();

        // 检查次数限制
        if ($config->max_daily_count && $todayRecords->count() >= $config->max_daily_count) {
            return true;
        }

        // 检查积分限制
        if ($config->max_daily_points) {
            $todayPoints = $todayRecords->where('point_change', '>', 0)->sum('point_change');
            if ($todayPoints >= $config->max_daily_points) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为首次操作
     */
    private function isDailyFirst(PointConfigModel $config, int $userId): bool
    {
        $today =Carbon::now()->toDateString();

        return !PointRecordModel::where('user_id', $userId)
            ->where('point_type', $config->action_type)
            ->whereDate('created_at', $today)
            // ->where('is_reversed', false)
            ->exists();
    }

    /**
     * 计算实际积分
     */
    private function calculateActualPoints(PointConfigModel $config, int $userId, array $context = []): array
    {
        $basePoints = $config->points;
        $multiplier = 1.0;
        $bonusReason = '';

        // 成就的积分由成就表中reward_points决定
        if ($config->action_type == 'achievement_reward') {
            $achievementCode = $context['achievement_code'] ?? '';
            if (!$achievementCode) {
                throw new AppException(StatusCode::ERR_SERVER, '成就代码不能为空');
            }

            $achievement = AchievementModel::where('code', $achievementCode)->first();
            if (!$achievement) {
                throw new AppException(StatusCode::ERR_SERVER, '成就不存在');
            }

            if (!$achievement->is_active) {
                throw new AppException(StatusCode::ERR_SERVER, '成就未启用');
            }

            $basePoints = $achievement->reward_points;
        }


//        // 首次奖励
        $isFirstTime = $this->isDailyFirst($config, $userId);
//        if ($isFirstTime && $config->first_time_bonus > 0) {
//            $basePoints += $config->first_time_bonus;
//            $bonusReason .= '首次奖励+' . $config->first_time_bonus . ';';
//        }

        // 质量倍数（如果配置了）
        if (!empty($config->quality_multiplier) && isset($context['quality_score'])) {
            $qualityScore = $context['quality_score'];
            foreach ($config->quality_multiplier as $threshold => $mult) {
                if ($qualityScore >= $threshold) {
                    $multiplier = max($multiplier, $mult);
                }
            }
            if ($multiplier > 1) {
                $bonusReason .= "质量倍数x{$multiplier};";
            }
        }

        // 连续操作奖励（如果配置了）
        if (!empty($config->consecutive_bonus) && isset($context['consecutive_days'])) {
            $consecutiveDays = $context['consecutive_days'];
            foreach ($config->consecutive_bonus as $days => $bonus) {
                if ($consecutiveDays >= $days) {
                    $basePoints += $bonus;
                    $bonusReason .= "连续{$days}天奖励+{$bonus};";
                    break;
                }
            }
        }

        // 管理员手动指定积分
        if (isset($context['points']) && is_numeric($context['points'])) {
            $basePoints = (int) $context['points'];
            $multiplier = 1.0;
            $bonusReason = '管理员指定';
        }

        $finalPoints = (int) round($basePoints * $multiplier);

        return [
            'base_points' => $config->points,
            'bonus_points' => $basePoints - $config->points,
            'multiplier' => $multiplier,
            'final_points' => $finalPoints,
            'bonus_reason' => trim($bonusReason, ';'),
            'is_first_time' => $isFirstTime
        ];
    }

    /**
     * 检查积分撤销时效限制（使用 PointTypeManager）
     */
    private function checkReverseTimeLimit(PointRecordModel $record, ?int $operatorId = null): void
    {
        $pointType = $record->point_type;

        // 使用 PointTypeManager 验证积分类型并获取时效限制
        if (!PointTypeManager::isValidPointType($pointType)) {
            throw new AppException(StatusCode::ERR_SERVER, "无效的积分类型：{$pointType}");
        }

        // 获取时效限制（小时）
        $timeLimit = PointTypeManager::getReverseTimeLimit($pointType);

        // 如果时效限制为0，表示不可撤销
        if ($timeLimit === 0) {
            $pointTypeName = PointTypeManager::getPointTypeName($pointType);
            throw new AppException(StatusCode::ERR_SERVER, "该类型积分操作不支持撤销：{$pointTypeName}（{$pointType}）");
        }

        // 检查用户权限，管理员可能有特殊权限
        $userPermissions = $this->getUserReversePermissions($operatorId);
        if ($userPermissions['ignore_time_limits']) {
            $this->loggerFactory->get('points')->info('管理员忽略时效限制执行撤销', [
                'point_type' => $pointType,
                'record_id' => $record->id,
                'operator_id' => $operatorId,
                'time_limit_hours' => $timeLimit
            ]);
            return; // 管理员可以忽略时效限制
        }

        // 计算时间差
        $createdAt = $record->created_at;
        $now = Carbon::now();
        $hoursDiff = $now->diffInHours($createdAt);

        if ($hoursDiff > $timeLimit) {
            $timeLimitText = $this->formatTimeLimit($timeLimit);
            
            $this->loggerFactory->get('points')->warning('积分撤销超时', [
                'point_type' => $pointType,
                'point_type_name' => PointTypeManager::getPointTypeName($pointType),
                'record_id' => $record->id,
                'created_at' => $createdAt,
                'hours_passed' => $hoursDiff,
                'time_limit_hours' => $timeLimit,
                'operator_id' => $operatorId
            ]);

            throw new AppException(
                StatusCode::ERR_SERVER,
                "积分撤销已超时，该操作只能在{$timeLimitText}内撤销"
            );
        }
    }

    /**
     * 检查积分撤销权限和防滥用
     */
    private function checkReversePermissions(int $userId, string $pointType, ?int $operatorId = null): void
    {
        $antiAbuseConfig = config('points.anti_abuse', []);
        $userPermissions = $this->getUserReversePermissions($operatorId);

        // 检查是否有权限撤销该类型积分
        if (!$userPermissions['can_reverse_any_type']) {
            $allowedCategories = config('points.point_categories', []);
            $isAllowed = false;

            foreach ($allowedCategories as $category => $categoryConfig) {
                if (in_array($pointType, $categoryConfig['types'] ?? [])) {
                    $isAllowed = true;
                    break;
                }
            }

            if (!$isAllowed) {
                throw new AppException(StatusCode::ERR_SERVER, '您没有权限撤销该类型的积分操作');
            }
        }

        // 检查每日撤销次数限制
        $maxDailyReverses = $userPermissions['max_daily_reverses'];
        if ($maxDailyReverses > 0) {
            $todayReverses = PointRecordModel::where('created_by', $operatorId)
                // ->where('is_reversed', true)
                ->whereDate('reversed_at', Carbon::today())
                ->count();

            if ($todayReverses >= $maxDailyReverses) {
                throw new AppException(
                    StatusCode::ERR_SERVER,
                    "今日撤销次数已达上限（{$maxDailyReverses}次）"
                );
            }
        }
    }

    /**
     * 获取用户撤销权限
     */
    private function getUserReversePermissions(?int $operatorId = null): array
    {
        $permissions = config('points.user_permissions', []);

        if (!$operatorId) {
            return $permissions['regular'] ?? [
                'can_reverse_any_type' => false,
                'ignore_time_limits' => false,
                'max_daily_reverses' => 20,
            ];
        }

        // 这里应该根据实际的用户角色系统来判断
        // 暂时简化处理，可以根据用户ID或其他方式判断角色

        // 示例：假设用户ID小于10的是管理员
        if ($operatorId <= 10) {
            return $permissions['admin'] ?? [
                'can_reverse_any_type' => true,
                'ignore_time_limits' => true,
                'max_daily_reverses' => 0,
            ];
        }

        // 示例：假设用户ID在11-100之间的是版主
        if ($operatorId <= 100) {
            return $permissions['moderator'] ?? [
                'can_reverse_any_type' => true,
                'ignore_time_limits' => false,
                'max_daily_reverses' => 100,
            ];
        }

        // 普通用户
        return $permissions['regular'] ?? [
            'can_reverse_any_type' => false,
            'ignore_time_limits' => false,
            'max_daily_reverses' => 20,
        ];
    }

    /**
     * 检查积分记录是否可以撤销
     */
    public function canReversePointRecord(int $recordId, ?int $operatorId = null): array
    {
        try {
            $record = PointRecordModel::find($recordId);
            if (!$record) {
                return [
                    'can_reverse' => false,
                    'reason' => '积分记录不存在'
                ];
            }

            if ($record->is_reversed) {
                return [
                    'can_reverse' => false,
                    'reason' => '该积分记录已被撤销'
                ];
            }

            // 检查时效限制
            $this->checkReverseTimeLimit($record, $operatorId);

            // 检查权限
            $this->checkReversePermissions($record->user_id, $record->point_type, $operatorId);

            return [
                'can_reverse' => true,
                'reason' => '可以撤销'
            ];

        } catch (AppException $e) {
            return [
                'can_reverse' => false,
                'reason' => $e->getMessage()
            ];
        }
    }

    /**
     * 提取源标题的辅助方法
     */
    private function extractSourceTitle(string $sourceType, ?int $sourceId, string $reason): string
    {
        // 根据源类型和ID尝试提取有意义的标题
        switch ($sourceType) {
            case 'wiki_like':
            case 'wiki_unlike':
                return "Wiki文档操作";
            case 'wiki_pin':
            case 'wiki_unpin':
                return "Wiki置顶操作";
            case 'wiki_essence':
                return "Wiki加精操作";
            case 'comment':
                return "评论操作";
            case 'manual':
                return "手动操作";
            default:
                // 从reason中提取或使用默认值
                if (mb_strlen($reason) > 20) {
                    return mb_substr($reason, 0, 20) . '...';
                }
                return $reason ?: $sourceType;
        }
    }

    /**
     * 格式化时间限制显示
     */
    private function formatTimeLimit(int $hours): string
    {
        if ($hours === 0) {
            return '不可撤销';
        }
        
        $days = floor($hours / 24);
        $remainingHours = $hours % 24;
        
        $result = '';
        if ($days > 0) {
            $result .= "{$days}天";
        }
        if ($remainingHours > 0) {
            $result .= "{$remainingHours}小时";
        }
        
        return $result ?: '0小时';
    }

    /**
     * 验证并查找原始积分记录
     */
    private function validateAndFindOriginalRecord(
        int $userId,
        int $points,
        ?string $originalSourceType,
        ?int $originalSourceId,
        string $pointType
    ): PointRecordModel {
        // 验证必需的参数
        if (!$originalSourceType || !$originalSourceId) {
            $this->loggerFactory->get('points')->error('源验证失败：缺少必需的源信息', [
                'user_id' => $userId,
                'original_source_type' => $originalSourceType,
                'original_source_id' => $originalSourceId,
                'point_type' => $pointType
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '源验证失败：缺少必需的源信息');
        }

        // 查找对应的原始积分记录
        $originalRecord = PointRecordModel::where('user_id', $userId)
            ->where('source_type', $originalSourceType)
            ->where('source_id', $originalSourceId)
            ->where('point_change', '>', 0) // 只查找正积分记录
            ->where('is_reversed', false) // 未被撤销的记录
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$originalRecord) {
            $this->loggerFactory->get('points')->error('源验证失败：找不到对应的原始积分记录', [
                'user_id' => $userId,
                'original_source_type' => $originalSourceType,
                'original_source_id' => $originalSourceId,
                'point_type' => $pointType,
                'points_to_deduct' => $points
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '源验证失败：找不到对应的原始积分记录');
        }

//        // 验证积分匹配
//        if ($originalRecord->point_change != $points) {
//            $this->loggerFactory->get('points')->error('源验证失败：积分数量不匹配', [
//                'user_id' => $userId,
//                'original_record_id' => $originalRecord->id,
//                'original_points' => $originalRecord->point_change,
//                'points_to_deduct' => $points,
//                'original_source_type' => $originalSourceType,
//                'original_source_id' => $originalSourceId
//            ]);
//            throw new AppException(StatusCode::ERR_SERVER, "源验证失败：积分数量不匹配，原始积分{$originalRecord->point_change}，要扣除{$points}");
//        }

        // 验证是否已被撤销
        if ($originalRecord->is_reversed) {
            $this->loggerFactory->get('points')->error('源验证失败：原始积分记录已被撤销', [
                'user_id' => $userId,
                'original_record_id' => $originalRecord->id,
                'reversed_at' => $originalRecord->reversed_at,
                'reversed_reason' => $originalRecord->reversed_reason
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '源验证失败：该积分记录已被撤销');
        }

        $this->loggerFactory->get('points')->info('源验证成功', [
            'user_id' => $userId,
            'original_record_id' => $originalRecord->id,
            'original_points' => $originalRecord->point_change,
            'original_source_type' => $originalSourceType,
            'original_source_id' => $originalSourceId,
            'point_type' => $pointType
        ]);

        return $originalRecord;
    }

    /**
     * 验证并修复用户周期积分数据一致性
     * 可用于系统维护或数据修复
     */
    public function validateAndFixPeriodPoints(int $userId): array
    {
        $userPoints = $this->getUserPoints($userId);
        if (!$userPoints) {
            return ['success' => false, 'message' => '用户积分记录不存在'];
        }

        $now = Carbon::now();
        $fixes = [];

        // 计算实际的周期积分
        $actualDailyPoints = PointRecordModel::where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereDate('created_at', $now->toDateString())
            ->sum('point_change');

        $actualWeeklyPoints = PointRecordModel::where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereBetween('created_at', [
                $now->startOfWeek(),
                $now->endOfWeek()
            ])
            ->sum('point_change');

        $actualMonthlyPoints = PointRecordModel::where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereBetween('created_at', [
                $now->startOfMonth(),
                $now->endOfMonth()
            ])
            ->sum('point_change');

        $actualYearlyPoints = PointRecordModel::where('user_id', $userId)
            ->where('point_change', '>', 0)
            ->whereBetween('created_at', [
                $now->startOfYear(),
                $now->endOfYear()
            ])
            ->sum('point_change');

        // 检查并修复差异
        $updates = [];
        
        if ($userPoints->daily_points != $actualDailyPoints) {
            $updates['daily_points'] = $actualDailyPoints;
            $fixes[] = "日积分: {$userPoints->daily_points} -> {$actualDailyPoints}";
        }

        if ($userPoints->weekly_points != $actualWeeklyPoints) {
            $updates['weekly_points'] = $actualWeeklyPoints;
            $fixes[] = "周积分: {$userPoints->weekly_points} -> {$actualWeeklyPoints}";
        }

        if ($userPoints->monthly_points != $actualMonthlyPoints) {
            $updates['monthly_points'] = $actualMonthlyPoints;
            $fixes[] = "月积分: {$userPoints->monthly_points} -> {$actualMonthlyPoints}";
        }

        if ($userPoints->year_points != $actualYearlyPoints) {
            $updates['year_points'] = $actualYearlyPoints;
            $fixes[] = "年积分: {$userPoints->year_points} -> {$actualYearlyPoints}";
        }

        // 执行修复
        if (!empty($updates)) {
            $userPoints->update($updates);
            
            $this->loggerFactory->get('points')->info('周期积分数据修复', [
                'user_id' => $userId,
                'fixes' => $fixes,
                'updated_fields' => array_keys($updates)
            ]);

            return [
                'success' => true,
                'message' => '数据已修复',
                'fixes' => $fixes
            ];
        }

        return [
            'success' => true,
            'message' => '数据一致，无需修复',
            'fixes' => []
        ];
    }

    /**
     * 批量验证并修复所有用户的周期积分数据
     * 用于系统维护
     */
    public function batchValidateAndFixPeriodPoints(int $limit = 100): array
    {
        $users = UserPointsModel::limit($limit)->get();
        $results = [];

        foreach ($users as $userPoints) {
            try {
                $result = $this->validateAndFixPeriodPoints($userPoints->user_id);
                $results[] = [
                    'user_id' => $userPoints->user_id,
                    'success' => $result['success'],
                    'fixes_count' => count($result['fixes']),
                    'fixes' => $result['fixes']
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userPoints->user_id,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 批量重置所有用户的周期积分
     * 用于定时任务调用
     */
    public function batchResetAllUsersPeriodPoints(int $batchSize = 100): array
    {
        $statistics = [
            'total_processed' => 0,
            'reset_count' => 0,
            'error_count' => 0,
            'errors' => []
        ];

        try {
            // 分批处理所有用户
            UserPointsModel::chunk($batchSize, function ($userPointsList) use (&$statistics) {
                foreach ($userPointsList as $userPoints) {
                    $statistics['total_processed']++;
                    
                    try {
                        // 记录重置前的状态
                        $beforeReset = [
                            'daily' => $userPoints->daily_points,
                            'weekly' => $userPoints->weekly_points,
                            'monthly' => $userPoints->monthly_points,
                            'yearly' => $userPoints->year_points
                        ];

                        // 执行重置
                        $this->resetPeriodPointsIfNeeded($userPoints);
                        
                        // 刷新获取最新数据
                        $userPoints->refresh();
                        
                        // 检查是否有变化
                        $afterReset = [
                            'daily' => $userPoints->daily_points,
                            'weekly' => $userPoints->weekly_points,
                            'monthly' => $userPoints->monthly_points,
                            'yearly' => $userPoints->year_points
                        ];

                        if ($beforeReset !== $afterReset) {
                            $statistics['reset_count']++;
                            
                            $this->loggerFactory->get('points')->debug('用户周期积分已重置', [
                                'user_id' => $userPoints->user_id,
                                'before' => $beforeReset,
                                'after' => $afterReset
                            ]);
                        }

                    } catch (\Exception $e) {
                        $statistics['error_count']++;
                        $statistics['errors'][] = [
                            'user_id' => $userPoints->user_id,
                            'error' => $e->getMessage()
                        ];
                        
                        $this->loggerFactory->get('points')->error('批量重置用户周期积分失败', [
                            'user_id' => $userPoints->user_id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            });

            $this->loggerFactory->get('points')->info('批量重置周期积分完成', $statistics);

        } catch (\Exception $e) {
            $this->loggerFactory->get('points')->error('批量重置周期积分执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'statistics' => $statistics
            ]);
            
            throw $e;
        }

        return $statistics;
    }

    /**
     * 获取需要重置周期积分的用户数量
     * 用于统计和监控
     */
    public function countUsersNeedingPeriodReset(): array
    {
        $now = Carbon::now();
        $counts = [
            'daily_reset_needed' => 0,
            'weekly_reset_needed' => 0,
            'monthly_reset_needed' => 0,
            'yearly_reset_needed' => 0,
            'total_users' => UserPointsModel::count()
        ];

        UserPointsModel::chunk(1000, function ($userPointsList) use (&$counts, $now) {
            foreach ($userPointsList as $userPoints) {
                // 检查日积分重置
                $lastDailyReset = $userPoints->last_daily_reset ? Carbon::parse($userPoints->last_daily_reset) : null;
                if (!$lastDailyReset || $lastDailyReset->toDateString() !== $now->toDateString()) {
                    $counts['daily_reset_needed']++;
                }
                
                // 检查周积分重置
                $lastWeeklyReset = $userPoints->last_weekly_reset ? Carbon::parse($userPoints->last_weekly_reset) : null;
                if (!$lastWeeklyReset || $lastWeeklyReset->week !== $now->week || $lastWeeklyReset->year !== $now->year) {
                    $counts['weekly_reset_needed']++;
                }
                
                // 检查月积分重置
                $lastMonthlyReset = $userPoints->last_monthly_reset ? Carbon::parse($userPoints->last_monthly_reset) : null;
                if (!$lastMonthlyReset || $lastMonthlyReset->month !== $now->month || $lastMonthlyReset->year !== $now->year) {
                    $counts['monthly_reset_needed']++;
                }
                
                // 检查年积分重置
                $lastYearlyReset = $userPoints->last_yearly_reset ? Carbon::parse($userPoints->last_yearly_reset) : null;
                if (!$lastYearlyReset || $lastYearlyReset->year !== $now->year) {
                    $counts['yearly_reset_needed']++;
                }
            }
        });

        return $counts;
    }

    /**
     * 获取知识豆查阅数据
     */
    public function getPointsViewData(array $params = []): array
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['pageSize'] ?? 20);
        $offset = ($page - 1) * $pageSize;

        // 处理时间范围和季度数据（需要在查询前确定）
        $startDate = $params['startDate'] ?? null;
        $endDate = $params['endDate'] ?? null;
        $quarterYear = $params['quarterYear'] ?? null;
        $quarter = $params['quarter'] ?? null;
        
        // 如果有季度参数，计算季度日期范围
        $quarterStartDate = null;
        $quarterEndDate = null;
        if ($quarterYear && $quarter) {
            $startMonth = ($quarter - 1) * 3 + 1;
            $endMonth = $quarter * 3;
            $quarterStartDate = Carbon::create($quarterYear, $startMonth, 1)->startOfMonth();
            $quarterEndDate = Carbon::create($quarterYear, $endMonth, 1)->endOfMonth();
        } else {
            // 默认当前季度
            $currentQuarter = ceil(Carbon::now()->month / 3);
            $quarterYear = Carbon::now()->year;
            $startMonth = ($currentQuarter - 1) * 3 + 1;
            $endMonth = $currentQuarter * 3;
            $quarterStartDate = Carbon::create($quarterYear, $startMonth, 1)->startOfMonth();
            $quarterEndDate = Carbon::create($quarterYear, $endMonth, 1)->endOfMonth();
        }

        $query = UserModel::query()
            ->where('user.status', 1)
            ->leftJoin('user_points as p', 'user.id', '=', 'p.user_id')
            ->leftJoin('user_department as d', function ($join) {
                $join->whereRaw("JSON_CONTAINS(bi_user.department, CAST(bi_d.id AS JSON), '$')");
            });

        // 获取排序参数
        $sortField = $params['sortField'] ?? '';
        $sortOrder = $params['sortOrder'] ?? 'desc';
        $orderDirection = (strtolower($sortOrder) === 'asc') ? 'asc' : 'desc';

        $query->select([
            'p.*',
            'user.name as username',
            'user.avatar',
            'user.id as user_id',
            'user.department',
            DB::raw('GROUP_CONCAT(bi_d.name ORDER BY bi_d.id) as department_name')
        ]);

        // 部门筛选
        if (!empty($params['departmentIds'])) {
            $departmentIds = is_string($params['departmentIds'])
                ? explode(',', $params['departmentIds'])
                : $params['departmentIds'];

            $query->where(function ($q) use ($departmentIds) {
                foreach ($departmentIds as $id) {
                    $q->orWhereRaw("JSON_CONTAINS(bi_user.department, ?, '$')", [$id]);
                }
            });
        }

        $cloneQuery = clone $query;

        $query->groupBy(['user.id']);

        // 处理排序 - 使用子查询直接在ORDER BY中
        if ($sortField === 'selectedPeriodPoints' && $startDate && $endDate) {
            $query->orderByRaw("(
                SELECT COALESCE(SUM(point_change), 0) 
                FROM bi_point_records 
                WHERE user_id = bi_user.id 
                AND is_reversed = 0 
                AND created_at BETWEEN ? AND ?
            ) {$orderDirection}", [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } elseif ($sortField === 'quarterPoints') {
            $query->orderByRaw("(
                SELECT COALESCE(SUM(point_change), 0) 
                FROM bi_point_records 
                WHERE user_id = bi_user.id 
                AND is_reversed = 0 
                AND created_at BETWEEN ? AND ?
            ) {$orderDirection}", [$quarterStartDate->format('Y-m-d H:i:s'), $quarterEndDate->format('Y-m-d H:i:s')]);
        } elseif ($sortField === 'currentPoints' || $sortField === 'rank') {
            $query->orderBy('p.current_points', $orderDirection);
        } else {
            // 默认排序
            $query->orderBy('p.current_points', 'desc');
        }
        
        // 添加次要排序确保结果稳定
        $query->orderBy('user.id', 'asc');

        $total = $cloneQuery->distinct('user.id')->count('user.id');
        $users = $query->offset($offset)->limit($pageSize)->get();

        $result = [];
        $statistics = [
            'totalPoints' => 0,
            'activeUsers' => 0,
            'avgPoints' => 0,
            'maxPoints' => 0
        ];

        foreach ($users as $index => $user) {
            // 计算选定时间段积分
            $selectedPeriodPoints = 0;
            if ($startDate && $endDate) {
                $selectedPeriodPoints = PointRecordModel::query()
                    ->where('user_id', $user->user_id)
                    ->where('is_reversed', false)
                    ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                    ->sum('point_change');
            }

            // 计算季度积分
            $quarterPoints = PointRecordModel::query()
                ->where('user_id', $user->user_id)
                ->where('is_reversed', false)
                ->whereBetween('created_at', [$quarterStartDate, $quarterEndDate])
                ->sum('point_change');

            $userData = [
                'userId' => $user->user_id,
                'username' => $user->username ?: '用户' . $user->user_id,
                'avatar' => $user->avatar,
                'departmentName' => $user->department_name ?: '未分配部门',
                'selectedPeriodPoints' => (int)$selectedPeriodPoints,
                'quarterPoints' => (int)$quarterPoints,
                'currentPoints' => $user->current_points,
                'level' => $user->level ?? 1,
                'levelName' => $user->level_name ?? '青铜智者',
                'rank' => $index + 1 + $offset, // 简单排名计算
                'lastPointsAt' => $user->last_points_at
            ];

            $result[] = $userData;

            // 统计数据
            $statistics['totalPoints'] += $selectedPeriodPoints ?: $user->current_points;
            if ($user->last_points_at && Carbon::parse($user->last_points_at)->gt(Carbon::now()->subDays(30))) {
                $statistics['activeUsers']++;
            }
            $statistics['maxPoints'] = max($statistics['maxPoints'], $selectedPeriodPoints ?: $user->current_points);
        }

        // 计算平均值
        if (count($result) > 0) {
            $statistics['avgPoints'] = round($statistics['totalPoints'] / count($result));
        }

        return [
            'list' => $result,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => ceil($total / $pageSize)
            ],
            'statistics' => $statistics
        ];
    }

    public function initAllUserPoint()
    {

        $users = UserModel::query()->where('status', 1)->get(['id', 'name', 'status', 'department'])->toArray();
        $userIds = array_column($users, 'id');
        $userNameMap = array_column($users, 'name', 'id');

        $pointsUserIds = UserPointsModel::query()->pluck('user_id')->toArray();

        $needInitUserIds = array_diff($userIds, $pointsUserIds);


        foreach ($needInitUserIds as $userId) {
            Log::get('system', 'system')->info($userNameMap[$userId] . "积分初始化完成");

            $this->getUserPoints($userId, true);
        }

    }

    public function calculateHistoryPoints(int $userId = 288)
    {
        $allDocuments = WikiDocumentModel::query()
            ->where('space_id', '<>', 11) // 初始化时不对 硬件研发中心 文档进行统计
            ->where('created_by', $userId)
            ->get(['doc_id', 'title', 'space_id', 'catalog_id', 'view_count', 'like_count', 'created_by'])
            ->toArray();

         $validDocNums = count($allDocuments);
         $allLikeNums = array_sum(array_column($allDocuments, 'like_count'));

         // 202508 需要审核才发放积分
        // $this->addPoints(
        //     $userId,
        //     'publish_article',
        //     'wiki_document',
        //     null,
        //     '初始化知识库文档相关知识豆--基于文档数量',
        //     ['operator_id' => null, 'points' => $validDocNums * 10]
        // );

        $this->addPoints(
            $userId,
            'get_like',
            'wiki_document',
            null,
            '初始化知识库文档相关知识豆--基于点赞数量',
            ['operator_id' => null, 'points' => $allLikeNums]
        );
    }

    // 检查是否存在积分增加记录
    public function isPointAdded(string $pointType, string $sourceType, int $sourceId): bool
    {
        $record = PointRecordModel::query()
            ->where('point_type', $pointType)
            ->where('source_type', $sourceType)
            ->where('source_id', $sourceId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $record !== null && $record->point_change > 0;

    }

}