<?php

declare(strict_types=1);

namespace App\Core\Services\Points;

use App\Model\Points\UserPointsModel;
use App\Model\Points\PointRecordModel;
use App\Model\Points\UserPointStatisticsModel;
use App\Model\Points\PointConfigModel;
use App\Core\Services\BaseService;
use Hyperf\DbConnection\Db;
use Carbon\Carbon;
use App\Exception\AppException;
use App\Constants\StatusCode;

/**
 * 积分统计服务类
 */
class PointStatisticsService extends BaseService
{
    /**
     * 生成用户积分统计数据
     */
    public function generateUserStatistics(int $userId, string $statType, string $statDate): array
    {
        $startDate = Carbon::parse($statDate);
        $endDate = $this->getEndDateByType($startDate, $statType);

        // 获取期间积分记录
        $records = PointRecordModel::where('user_id', $userId)
            // ->where('is_reversed', false)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        // 计算各项指标
        $pointsEarned = $records->where('point_change', '>', 0)->sum('point_change');
        $pointsConsumed = abs($records->where('point_change', '<', 0)->sum('point_change'));
        $netPoints = $pointsEarned - $pointsConsumed;

        // 统计活动数据
        $articlePublished = $records->where('point_type', 'publish_article')->count();
        $likesReceived = $records->where('point_type', 'get_like')->count();
        $commentsMade = $records->where('point_type', 'comment_article')->count();

        // 获取期间等级信息
        $userPoints = UserPointsModel::where('user_id', $userId)->first();
        $levelAtStart = $this->getUserLevelAtDate($userId, $startDate);
        $levelAtEnd = $userPoints ? $userPoints->level : 1;

        // 计算排名
        $rankInPeriod = $this->getUserRankInPeriod($userId, $statType, $statDate);

        // 统计解锁成就数
        $achievementsUnlocked = Db::table('user_achievements')
            ->where('user_id', $userId)
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->count();

        $statisticsData = [
            'user_id' => $userId,
            'stat_date' => $statDate,
            'stat_type' => $statType,
            'points_earned' => $pointsEarned,
            'points_consumed' => $pointsConsumed,
            'net_points' => $netPoints,
            'article_published' => $articlePublished,
            'likes_received' => $likesReceived,
            'comments_made' => $commentsMade,
            'documents_viewed' => $this->getDocumentsViewedCount($userId, $startDate, $endDate),
            'active_days' => $this->getActiveDaysCount($userId, $startDate, $endDate),
            'rank_in_period' => $rankInPeriod,
            'achievements_unlocked' => $achievementsUnlocked,
            'level_at_period_start' => $levelAtStart,
            'level_at_period_end' => $levelAtEnd,
        ];

        // 保存或更新统计数据
        UserPointStatisticsModel::updateOrCreate([
            'user_id' => $userId,
            'stat_date' => $statDate,
            'stat_type' => $statType
        ], $statisticsData);

        return $statisticsData;
    }

    /**
     * 批量生成统计数据
     */
    public function batchGenerateStatistics(string $statType, string $statDate): array
    {
        $userIds = UserPointsModel::pluck('user_id')->toArray();
        $results = [];

        foreach ($userIds as $userId) {
            try {
                $result = $this->generateUserStatistics($userId, $statType, $statDate);
                $results[] = [
                    'user_id' => $userId,
                    'success' => true,
                    'data' => $result
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取积分趋势数据
     */
    public function getPointsTrend(int $userId, int $days = 30): array
    {
        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subDays($days);

        $dailyStats = UserPointStatisticsModel::where('user_id', $userId)
            ->where('stat_type', 'daily')
            ->whereBetween('stat_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->orderBy('stat_date')
            ->get();

        $trend = [];
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dateStr = $date->toDateString();
            $stat = $dailyStats->firstWhere('stat_date', $dateStr);
            
            $trend[] = [
                'date' => $dateStr,
                'points_earned' => $stat->points_earned ?? 0,
                'points_consumed' => $stat->points_consumed ?? 0,
                'net_points' => $stat->net_points ?? 0,
                'rank' => $stat->rank_in_period ?? 0,
                'level' => $stat->level_at_period_end ?? 1
            ];
        }

        return $trend;
    }

    /**
     * 获取积分分布统计
     */
    public function getPointsDistribution(): array
    {
        $distribution = UserPointsModel::select(
            Db::raw('
                CASE 
                    WHEN current_points = 0 THEN "0"
                    WHEN current_points <= 99 THEN "1-99"
                    WHEN current_points <= 499 THEN "100-499"
                    WHEN current_points <= 999 THEN "500-999"
                    WHEN current_points <= 4999 THEN "1000-4999"
                    ELSE "5000+"
                END as point_range
            '),
            Db::raw('count(*) as user_count')
        )
        ->groupBy('point_range')
        ->get()
        ->keyBy('point_range')
        ->map(function ($item) {
            return $item->user_count;
        })
        ->toArray();

        return [
            '0' => $distribution['0'] ?? 0,
            '1-99' => $distribution['1-99'] ?? 0,
            '100-499' => $distribution['100-499'] ?? 0,
            '500-999' => $distribution['500-999'] ?? 0,
            '1000-4999' => $distribution['1000-4999'] ?? 0,
            '5000+' => $distribution['5000+'] ?? 0,
        ];
    }

    /**
     * 获取积分来源统计
     */
    public function getPointsSourceStatistics(array $params = []): array
    {
        $query = PointRecordModel::
        // where('is_reversed', false)->
            where('point_change', '>', 0);

        if (!empty($params['start_date'])) {
            $query->whereDate('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->whereDate('created_at', '<=', $params['end_date']);
        }

        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        $sourceStats = $query->select('point_type', Db::raw('count(*) as count'), Db::raw('sum(point_change) as total_points'))
            ->groupBy('point_type')
            ->get();

        $configs = PointConfigModel::active()->get()->keyBy('action_type');

        return $sourceStats->map(function ($stat) use ($configs) {
            $config = $configs->get($stat->point_type);
            return [
                'point_type' => $stat->point_type,
                'action_name' => $config->action_name ?? $stat->point_type,
                'category' => $config->category ?? 'general',
                'count' => $stat->count,
                'total_points' => $stat->total_points,
                'average_points' => $stat->count > 0 ? round($stat->total_points / $stat->count, 2) : 0
            ];
        })->toArray();
    }

    /**
     * 获取活跃用户统计
     */
    public function getActiveUserStatistics(string $period = 'week'): array
    {
        switch ($period) {
            case 'day':
                $days = 1;
                break;
            case 'week':
                $days = 7;
                break;
            case 'month':
                $days = 30;
                break;
            default:
                $days = 7;
                break;
        }

        $startDate = Carbon::now()->subDays($days)->startOfDay();
        
        // 有积分变动的用户
        $activeUsers = PointRecordModel::where('created_at', '>=', $startDate)
            ->distinct('user_id')
            ->count();

        // 总用户数
        $totalUsers = UserPointsModel::count();

        // 新增用户（首次获得积分）
        $newUsers = UserPointsModel::where('created_at', '>=', $startDate)->count();

        // 高活跃用户（积分变动超过平均值的用户）
        $averageActivity = PointRecordModel::where('created_at', '>=', $startDate)
            ->selectRaw('count(*) / count(distinct user_id) as avg_records')
            ->value('avg_records') ?? 0;

        $highActiveUsers = PointRecordModel::where('created_at', '>=', $startDate)
            ->select('user_id', Db::raw('count(*) as record_count'))
            ->groupBy('user_id')
            ->having('record_count', '>', $averageActivity)
            ->count();

        return [
            'period' => $period,
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'new_users' => $newUsers,
            'high_active_users' => $highActiveUsers,
            'activity_rate' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0,
            'average_activity' => round($averageActivity, 2)
        ];
    }

    /**
     * 获取积分系统健康度指标
     */
    public function getSystemHealthMetrics(): array
    {
        $totalUsers = UserPointsModel::count();
        $activeUsers = UserPointsModel::active()->count();
        
        // 积分分布是否均衡
        $pointsDistribution = $this->getPointsDistribution();
        $distributionBalance = $this->calculateDistributionBalance($pointsDistribution);

        // 积分配置使用率
        $configUsage = PointConfigModel::active()
            ->leftJoin('point_records', 'point_configs.action_type', '=', 'point_records.point_type')
            ->select('point_configs.action_type', 'point_configs.action_name', Db::raw('count(point_records.id) as usage_count'))
            ->groupBy('point_configs.action_type', 'point_configs.action_name')
            ->get();

        // 成就完成率
        $achievementCompletionRate = Db::table('user_achievements')
            ->selectRaw('count(*) as total_attempts, sum(is_completed) as completions')
            ->first();

        return [
            'user_engagement' => [
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'engagement_rate' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0
            ],
            'points_distribution' => [
                'balance_score' => $distributionBalance,
                'distribution' => $pointsDistribution
            ],
            'config_usage' => $configUsage->toArray(),
            'achievement_metrics' => [
                'total_attempts' => $achievementCompletionRate->total_attempts ?? 0,
                'completions' => $achievementCompletionRate->completions ?? 0,
                'completion_rate' => $achievementCompletionRate->total_attempts > 0 ? 
                    round(($achievementCompletionRate->completions / $achievementCompletionRate->total_attempts) * 100, 2) : 0
            ]
        ];
    }

    /**
     * 根据统计类型获取结束日期
     */
    private function getEndDateByType(Carbon $startDate, string $statType): Carbon
    {
        switch ($statType) {
            case 'daily':
                return $startDate->copy()->endOfDay();
            case 'weekly':
                return $startDate->copy()->endOfWeek();
            case 'monthly':
                return $startDate->copy()->endOfMonth();
            case 'yearly':
                return $startDate->copy()->endOfYear();
            default:
                return $startDate->copy()->endOfDay();
        }
    }

    /**
     * 获取用户在指定日期的等级
     */
    private function getUserLevelAtDate(int $userId, Carbon $date): int
    {
        $record = PointRecordModel::where('user_id', $userId)
            ->where('created_at', '<=', $date)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$record) {
            return 1;
        }

        // 根据当时积分计算等级
        $levelConfig = Db::table('level_configs')
            ->where('min_points', '<=', $record->current_points)
            ->where(function ($query) use ($record) {
                $query->where('max_points', '>=', $record->current_points)
                    ->orWhereNull('max_points');
            })
            ->orderBy('level', 'desc')
            ->first();

        return $levelConfig->level ?? 1;
    }

    /**
     * 获取用户在期间的排名
     */
    private function getUserRankInPeriod(int $userId, string $statType, string $statDate): int
    {
        $stat = UserPointStatisticsModel::where('user_id', $userId)
            ->where('stat_type', $statType)
            ->where('stat_date', $statDate)
            ->first();

        if (!$stat) {
            return 0;
        }

        return UserPointStatisticsModel::where('stat_type', $statType)
            ->where('stat_date', $statDate)
            ->where('points_earned', '>', $stat->points_earned)
            ->count() + 1;
    }

    /**
     * 获取文档浏览数量
     */
    private function getDocumentsViewedCount(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        // 这里需要根据实际的浏览记录表来实现
        return Db::table('user_view_logs')
            ->where('user_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('document_id')
            ->count() ?? 0;
    }

    /**
     * 获取活跃天数
     */
    private function getActiveDaysCount(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        return PointRecordModel::where('user_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('count(distinct date(created_at)) as active_days')
            ->value('active_days') ?? 0;
    }

    /**
     * 计算分布均衡度
     */
    private function calculateDistributionBalance(array $distribution): float
    {
        $total = array_sum($distribution);
        if ($total === 0) {
            return 0;
        }

        $expected = $total / count($distribution);
        $variance = 0;

        foreach ($distribution as $count) {
            $variance += pow($count - $expected, 2);
        }

        $variance /= count($distribution);
        $standardDeviation = sqrt($variance);

        // 均衡度评分（0-100，越高越均衡）
        return max(0, 100 - ($standardDeviation / $expected * 100));
    }
}