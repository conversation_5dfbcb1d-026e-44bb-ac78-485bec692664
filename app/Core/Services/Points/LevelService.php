<?php

declare(strict_types=1);

namespace App\Core\Services\Points;

use App\Model\Points\UserPointsModel;
use App\Model\Points\LevelConfigModel;
use App\Model\Points\PointRecordModel;
use App\Core\Services\BaseService;
use Hyperf\DbConnection\Db;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;

/**
 * 等级服务类
 */
class LevelService extends BaseService
{
    /**
     * @Inject
     * @var PointService
     */
    protected PointService $pointService;
    /**
     * 检查并升级用户等级
     */
    public function checkAndUpgradeLevel(int $userId): array
    {
        $result = Db::transaction(function () use ($userId) {
            $userPoints = UserPointsModel::where('user_id', $userId)->first();
            if (!$userPoints) {
                return [
                    'upgraded' => false,
                    'message' => '用户积分记录不存在'
                ];
            }

            // 根据积分计算应有等级
            $newLevel = $this->calculateLevelByPoints($userPoints->current_points);
            if (!$newLevel) {
                return [
                    'upgraded' => false,
                    'message' => '等级配置不存在'
                ];
            }

            // 检查是否需要升级
            if ($newLevel->level <= $userPoints->level) {
                return [
                    'upgraded' => false,
                    'message' => '当前等级已是最高或无需升级',
                    'current_level' => $userPoints->level
                ];
            }

            $oldLevel = $userPoints->level;
            $oldLevelName = $userPoints->level_name;

            // 更新用户等级
            $userPoints->update([
                'level' => $newLevel->level,
                'level_name' => $newLevel->level_name,
                'level_updated_at' => Carbon::now(),
                'version' => $userPoints->version + 1
            ]);
            
            return [
                'upgraded' => true,
                'message' => '等级升级成功',
                'old_level' => $oldLevel,
                'old_level_name' => $oldLevelName,
                'new_level' => $newLevel->level,
                'new_level_name' => $newLevel->level_name,
                'upgrade_reward' => $newLevel->upgrade_reward,
                'current_points' => $userPoints->current_points,
                'level_config' => $newLevel
            ];
        });

        // 在事务完成后发放升级奖励积分（避免循环依赖）
        if ($result['upgraded'] && $result['upgrade_reward'] > 0) {
            try {
                $upgradeRewardResult = $this->pointService->addPoints(
                    $userId,
                    'system_reward',
                    'system_reward',
                    $result['level_config']->id,
                    "升级到{$result['new_level_name']}奖励",
                    [
                        'level' => $result['new_level'],
                        'level_name' => $result['new_level_name'],
                        'points' => $result['upgrade_reward']
                    ]
                );
                
                // 更新最终积分数据
                $userPoints = UserPointsModel::where('user_id', $userId)->first();
                $result['current_points'] = $userPoints->current_points;
                $result['reward_result'] = $upgradeRewardResult;
            } catch (\Exception $e) {
                // 升级奖励发放失败，记录日志但不影响等级升级结果
                logger()->error('等级升级奖励发放失败', [
                    'user_id' => $userId,
                    'level' => $result['new_level'],
                    'reward_points' => $result['upgrade_reward'],
                    'error' => $e->getMessage()
                ]);
                $result['reward_result'] = [
                    'success' => false,
                    'message' => '升级奖励发放失败: ' . $e->getMessage()
                ];
            }
        }

        return $result;
    }

    /**
     * 根据积分计算等级
     */
    public function calculateLevelByPoints(int $points): ?LevelConfigModel
    {
        return LevelConfigModel::active()
            ->forPoints($points)
            ->orderBy('level', 'desc')
            ->first();
    }

    /**
     * 获取等级配置列表
     */
    public function getLevelConfigs(): array
    {
        return LevelConfigModel::active()
            ->byLevel()
            ->get()
            ->map(function ($level) {
                return [
                    'id' => $level->id,
                    'level' => $level->level,
                    'level_name' => $level->level_name,
                    'level_title' => $level->level_title,
                    'min_points' => $level->min_points,
                    'max_points' => $level->max_points,
                    'color' => $level->color,
                    'icon' => $level->icon,
                    'badge_image' => $level->badge_image,
                    'privileges' => $level->privileges,
                    'upgrade_reward' => $level->upgrade_reward,
                    'description' => $level->description,
                    'user_count' => $level->userPoints()->count()
                ];
            })
            ->toArray();
    }

    /**
     * 获取用户等级信息
     */
    public function getUserLevelInfo(int $userId): array
    {
        $userPoints = UserPointsModel::where('user_id', $userId)->first();
        if (!$userPoints) {
            return [
                'level' => 1,
                'level_name' => '青铜智者',
                'current_points' => 0,
                'level_progress' => 0,
                'next_level_points' => 100,
                'privileges' => []
            ];
        }

        $currentLevel = LevelConfigModel::where('level', $userPoints->level)->first();
        $nextLevel = LevelConfigModel::active()
            ->where('level', '>', $userPoints->level)
            ->orderBy('level')
            ->first();

        // 计算等级进度 此处以等级之间的间隔数量作为分母计算
        $levelProgress = 0;
        if ($currentLevel && $nextLevel) {
            $currentLevelMin = $currentLevel->min_points;
            $nextLevelMin = $nextLevel->min_points;
            $progressInLevel = $userPoints->current_points - $currentLevelMin;
            $levelRange = $nextLevelMin - $currentLevelMin;
            $levelProgress = $levelRange > 0 ? round(($progressInLevel / $levelRange) * 100, 2) : 100;
        }

        return [
            'level' => $userPoints->level,
            'level_name' => $userPoints->level_name,
            'level_title' => $currentLevel->level_title ?? '',
            'current_points' => $userPoints->current_points,
            'level_progress' => $levelProgress,
            'next_level_points' => $nextLevel->min_points ?? null,
            'next_level_name' => $nextLevel->level_name ?? '',
            'privileges' => $currentLevel->privileges ?? [],
            'color' => $currentLevel->color ?? '#CD7F32',
            'icon' => $currentLevel->icon ?? '',
            'badge_image' => $currentLevel->badge_image ?? '',
            'description' => $currentLevel->description ?? ''
        ];
    }

    /**
     * 获取等级分布统计
     */
    public function getLevelDistribution(): array
    {
        $levels = LevelConfigModel::active()->byLevel()->get();
        $userCounts = UserPointsModel::select('level', Db::raw('count(*) as count'))
            ->groupBy('level')
            ->pluck('count', 'level')
            ->toArray();

        return $levels->map(function ($level) use ($userCounts) {
            $userCount = $userCounts[$level->level] ?? 0;
            return [
                'level' => $level->level,
                'level_name' => $level->level_name,
                'color' => $level->color,
                'user_count' => $userCount,
                'percentage' => UserPointsModel::count() > 0 ? 
                    round(($userCount / UserPointsModel::count()) * 100, 2) : 0
            ];
        })->toArray();
    }

    /**
     * 检查用户是否有特定权限
     */
    public function userHasPrivilege(int $userId, string $privilege): bool
    {
        $userPoints = UserPointsModel::where('user_id', $userId)->first();
        if (!$userPoints) {
            return false;
        }

        $levelConfig = LevelConfigModel::where('level', $userPoints->level)->first();
        if (!$levelConfig || !$levelConfig->privileges) {
            return false;
        }

        return isset($levelConfig->privileges[$privilege]) && $levelConfig->privileges[$privilege];
    }

    /**
     * 获取升级历史
     */
    public function getUserUpgradeHistory(int $userId): array
    {
        return PointRecordModel::where('user_id', $userId)
            ->where('point_type', 'system_reward')
            // ->where('is_reversed', false)
            ->latest()
            ->get()
            ->map(function ($record) {
                return [
                    'upgrade_time' => $record->created_at->format('Y-m-d H:i:s'),
                    'description' => $record->description,
                    'reward_points' => $record->point_change,
                    'points_after_upgrade' => $record->current_points
                ];
            })
            ->toArray();
    }

    /**
     * 预览升级路径
     */
    public function getUpgradePath(int $userId, int $targetLevel = null): array
    {
        $userPoints = UserPointsModel::where('user_id', $userId)->first();
        if (!$userPoints) {
            return [];
        }

        $currentLevel = $userPoints->level;
        $query = LevelConfigModel::active()
            ->where('level', '>', $currentLevel)
            ->byLevel();

        if ($targetLevel) {
            $query->where('level', '<=', $targetLevel);
        }

        $levels = $query->get();

        return $levels->map(function ($level) use ($userPoints) {
            $pointsNeeded = max(0, $level->min_points - $userPoints->current_points);
            return [
                'level' => $level->level,
                'level_name' => $level->level_name,
                'min_points' => $level->min_points,
                'points_needed' => $pointsNeeded,
                'upgrade_reward' => $level->upgrade_reward,
                'description' => $level->description,
                'can_upgrade_now' => $pointsNeeded <= 0
            ];
        })->toArray();
    }

    /**
     * 创建或更新等级配置
     */
    public function saveOrUpdateLevelConfig(array $data): LevelConfigModel
    {
        $levelConfig = LevelConfigModel::where('level', $data['level'])->first();

        if ($levelConfig) {
            $levelConfig->update($data);
        } else {
            $levelConfig = LevelConfigModel::create($data);
        }

        return $levelConfig;
    }

    /**
     * 检查用户特权
     */
    public function checkUserPrivilege(int $userId, string $privilege): array
    {
        $levelInfo = $this->getUserLevelInfo($userId);
        $hasPrivilege = in_array($privilege, $levelInfo['privileges'] ?? []);
        
        return [
            'has_privilege' => $hasPrivilege,
            'privilege' => $privilege,
            'current_level' => $levelInfo['level'],
            'level_name' => $levelInfo['level_name']
        ];
    }

    /**
     * 获取可用特权列表
     */
    public function getAvailablePrivileges(): array
    {
        $privileges = $this->getPrivilegeLabels();
        $result = [];
        
        foreach ($privileges as $code => $name) {
            $result[] = [
                'code' => $code,
                'name' => $name,
                'description' => $this->getPrivilegeDescription($code),
                'required_level' => $this->getPrivilegeRequiredLevel($code)
            ];
        }
        
        return $result;
    }

    /**
     * 获取特权标签映射
     */
    private function getPrivilegeLabels(): array
    {
        return [
            'basic_post' => '基础发帖',
            'basic_comment' => '基础评论',
            'image_upload' => '图片上传',
            'topic_create' => '话题创建',
            'file_upload' => '文件上传',
            'essence_recommend' => '精华推荐',
            'moderator_rights' => '版主权限',
            'featured_author' => '特色作者',
            'all_permissions' => '所有权限',
            'exclusive_features' => '专属功能'
        ];
    }

    /**
     * 获取等级管理列表
     */
    public function getAdminLevelList(): array
    {
        return \App\Model\Points\LevelConfigModel::bySortOrder()
            ->get()
            ->map(function ($level) {
                return [
                    'id' => $level->id,
                    'level' => $level->level,
                    'level_name' => $level->level_name,
                    'level_title' => $level->level_title,
                    'min_points' => $level->min_points,
                    'max_points' => $level->max_points,
                    'color' => $level->color,
                    'icon' => $level->icon,
                    'badge_image' => $level->badge_image,
                    'privileges' => $level->privileges,
                    'upgrade_reward' => $level->upgrade_reward,
                    'description' => $level->description,
                    'is_active' => $level->is_active,
                    'sort_order' => $level->sort_order,
                    'user_count' => $level->userPoints()->count(),
                    'created_at' => $level->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $level->updated_at->format('Y-m-d H:i:s')
                ];
            })
            ->toArray();
    }

    /**
     * 创建等级配置
     */
    public function createLevel(array $params): array
    {
        $level = \App\Model\Points\LevelConfigModel::create($params);
        return $level->toArray();
    }

    /**
     * 更新等级配置
     */
    public function updateLevel(int $id, array $params): array
    {
        $level = \App\Model\Points\LevelConfigModel::find($id);
        if (!$level) {
            throw new AppException(StatusCode::ERR_SERVER, '等级配置不存在');
        }

        $level->update($params);
        return $level->toArray();
    }

    /**
     * 删除等级配置
     */
    public function deleteLevel(int $id): array
    {
        $level = \App\Model\Points\LevelConfigModel::find($id);
        if (!$level) {
            throw new AppException(StatusCode::ERR_SERVER, '等级配置不存在');
        }

        // 检查是否有用户在此等级
        $hasUsers = \App\Model\Points\UserPointsModel::where('level', $level->level)->exists();
        if ($hasUsers) {
            throw new AppException(StatusCode::ERR_SERVER, '有用户在此等级，无法删除');
        }

        $level->delete();
        return ['success' => true];
    }

    /**
     * 获取用户等级详情
     */
    public function getUserLevelDetail(int $userId): array
    {
        $levelInfo = $this->getUserLevelInfo($userId);
        $upgradeHistory = $this->getUserUpgradeHistory($userId);
        $upgradePath = $this->getUpgradePath($userId);

        return [
            'level_info' => $levelInfo,
            'upgrade_history' => $upgradeHistory,
            'upgrade_path' => $upgradePath
        ];
    }

    /**
     * 批量升级用户等级
     */
    public function batchUpgradeUsers(array $userIds = []): array
    {
        if (empty($userIds)) {
            // 如果没有指定用户ID，则检查所有用户
            $userIds = \App\Model\Points\UserPointsModel::pluck('user_id')->toArray();
        }

        $results = [];
        foreach ($userIds as $userId) {
            try {
                $result = $this->checkAndUpgradeLevel($userId);
                $results[] = [
                    'user_id' => $userId,
                    'success' => true,
                    'upgraded' => $result['upgraded'],
                    'new_level' => $result['new_level'] ?? null
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取等级统计信息
     */
    public function getLevelStatistics(): array
    {
        $totalUsers = \App\Model\Points\UserPointsModel::count();
        $levelDistribution = $this->getLevelDistribution();
        
        // 最近升级统计
        $recentUpgrades = \App\Model\Points\PointRecordModel::where('point_type', 'system_reward')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->selectRaw('count(*) as count, avg(point_change) as avg_reward')
            ->first();

        // 升级趋势
        $upgradeTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->toDateString();
            $count = \App\Model\Points\PointRecordModel::where('point_type', 'system_reward')
                ->whereDate('created_at', $date)
                ->count();
            
            $upgradeTrend[] = [
                'date' => $date,
                'count' => $count
            ];
        }

        return [
            'overview' => [
                'total_users' => $totalUsers,
                'recent_upgrades' => $recentUpgrades->count ?? 0,
                'avg_upgrade_reward' => $recentUpgrades->avg_reward ?? 0
            ],
            'level_distribution' => $levelDistribution,
            'upgrade_trend' => $upgradeTrend
        ];
    }

    /**
     * 获取特权描述
     */
    private function getPrivilegeDescription(string $privilege): string
    {
        $descriptions = [
            'advanced_search' => '可以使用高级搜索功能',
            'priority_support' => '享受优先客服支持',
            'exclusive_content' => '查看专属内容',
            'comment_priority' => '评论优先显示',
            'private_message' => '发送私信功能',
            'image_upload' => '上传图片权限',
            'topic_create' => '创建话题权限',
            'file_upload' => '上传文件权限',
            'essence_recommend' => '推荐精华内容',
            'moderator_rights' => '版主管理权限',
            'featured_author' => '特色作者标识',
            'all_permissions' => '拥有所有权限',
            'exclusive_features' => '专属功能访问'
        ];

        return $descriptions[$privilege] ?? '未知权限';
    }

    /**
     * 获取特权所需等级
     */
    private function getPrivilegeRequiredLevel(string $privilege): int
    {
        $requirements = [
            'advanced_search' => 2,
            'priority_support' => 2,
            'exclusive_content' => 3,
            'comment_priority' => 3,
            'private_message' => 2,
            'image_upload' => 2,
            'topic_create' => 3,
            'file_upload' => 3,
            'essence_recommend' => 4,
            'moderator_rights' => 4,
            'featured_author' => 4,
            'all_permissions' => 5,
            'exclusive_features' => 5
        ];

        return $requirements[$privilege] ?? 1;
    }
}