<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Redmine\ProductAttrKeyModel;
use App\Model\Redmine\ProductAttrValueModel;
use App\Model\Redmine\ProductPlatformModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductPlatformService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductPlatformModel
     */
    protected $model;
}