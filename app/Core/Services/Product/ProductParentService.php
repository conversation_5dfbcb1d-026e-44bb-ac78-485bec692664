<?php

/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use Hyperf\Di\Annotation\Inject;
use App\Model\Redmine\ProductParentModel;

class ProductParentService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductParentModel
     */
    protected $model;

    public function editProductParent($productId, $parentIds)
    {
        if(empty($productId)){
            return;
        }
        if(empty($parentIds)){
            $this->model->where('child_id', $productId)->delete();
            return;
        }
        //新增/删除父级
        !is_array($parentIds) && $parentIds = explode(',', $parentIds);
        $productParentIds = ProductParentModel::query()->where('child_id', $productId)->pluck('parent_id')->toArray();
        $addParentIds = array_diff($parentIds, $productParentIds);
        $deleteParentIds = array_diff($productParentIds, $parentIds);
        $this->model->where('child_id', $productId)->whereIn('parent_id', $deleteParentIds)->delete();
        //新增父级
        $createdBy = getRedmineUserId();
        $this->model->insert(array_map(function($parentId) use ($productId, $createdBy){
            return ['child_id' => $productId, 'parent_id' => $parentId, 'created_by' => $createdBy];
        }, $addParentIds ?? []));
    }

}
