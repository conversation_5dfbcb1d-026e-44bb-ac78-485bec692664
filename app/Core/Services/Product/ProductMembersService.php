<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Core\Services\UserService;
use App\Exception\AppException;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\ProductMemberModel;
use App\Model\Redmine\ProductModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductMembersService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductMemberModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        // list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        // $paginate = $query->orderBy($sort, $order)->with(['user'])->paginate($limit);
        // $paginate = $paginate ? $paginate->toArray() : [];
        // if (!empty($paginate['data'])) {
        //     $userIds = array_column($paginate['data'], 'user_id');
        //     $users = \App\Model\TchipBi\UserModel::query()->select(['user.*', 'user_third.third_user_id as third_user_id'])->join('user_third', 'user.id', '=', 'user_third.user_id')
        //         ->whereIn('user_third.third_user_id', $userIds)->where('platform', 'redmine')->where('user.status', 1)->get();
        //     $users = $users ? array_column($users->toArray(), null, 'third_user_id') : [];
        //     foreach ($paginate['data'] as &$datum) {
        //         if (!empty($users[$datum['user_id']])) {
        //             $datum['uid'] = $users[$datum['user_id']]['id'];
        //             $datum['name'] = $users[$datum['user_id']]['name'];
        //             $datum['biz_mail'] = $users[$datum['user_id']]['biz_mail'];
        //             $datum['thumb_avatar'] = $users[$datum['user_id']]['thumb_avatar'];
        //             $datum['department_text'] = $users[$datum['user_id']]['department_text'];
        //         } else {
        //             $datum['uid'] = 0;
        //             $datum['name'] = null;
        //             $datum['biz_mail'] = null;
        //             $datum['thumb_avatar'] = null;
        //             $datum['department_text'] = null;
        //         }
        //     }
        // }
        // return $paginate;


        if (!empty($filter['product_id']) && empty($filter['project_id'])) {
            $filter['project_id'] =    $filter['product_id'];
            unset($filter['product_id']);
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, make(MemberModel::class));
        $memberTable = 'members';
        $userTable = 'users';
        $paginate = $query->selectRaw("{$userTable}.*, CONCAT({$userTable}.lastname, {$userTable}.firstname) as name")
            ->join('users', "{$memberTable}.user_id", '=', "{$userTable}.id")
            ->orderBy($sort, $order)
            ->paginate($limit);

        $paginate = $paginate ? $paginate->toArray() : [];
        if (!empty($paginate['data'])) {
            $redmineUids = array_column($paginate['data'], 'id');
            $biUsers = make(UserService::class)->getUserListByThirdUserId($redmineUids, 'redmine');
            $biUsers = $biUsers ? array_column($biUsers, null, 'third_user_id') : [];
            foreach ($paginate['data'] as &$datum) {
                $datum['thumb_avatar'] = !empty($biUsers[$datum['id']]['thumb_avatar']) ? $biUsers[$datum['id']]['thumb_avatar'] : '';
                $datum['department_text'] = $biUsers[$datum['id']]['department_text'] ?? '';
            }
        }
        return $paginate;
        // return make(\App\Core\Services\Project\MemberService::class)->getList($filter, $op, $sort, $order, $limit);
    }


    public function doEdit(int $id, array $values)
    {
        $projectId = $values['project_id'] ?? ($values['product_id'] ?? null);
        if (empty($projectId)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        if ($id > 0) {
            return parent::doEdit($id, $values);
        } else {
            // if ($values['user_id'] && $values['product_id']) {
            //     $values['user_id'] = !is_array($values['user_id']) ? explode(',', $values['user_id']) : $values['user_id'];
            //     foreach ($values['user_id'] as $userId) {
            //         $where = [
            //             'product_id' => $values['product_id'],
            //             'user_id' => $userId,
            //         ];
            //         $this->model::query()->firstOrCreate($where);
            //     }
            // } else {
            //     throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
            // }
            $values['role_ids'] = $values['role_ids'] ?? [4];
            return make(\App\Core\Services\Project\MemberService::class)->doEditByProjectUser($projectId, $values);
        }
    }

    public function doEditProductWatchers($productId, $values)
    {
        if (!empty($values['user_id'])) {
            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();
            $userIds = !is_array($values['user_id']) ? explode(',', $values['user_id']) : $values['user_id'];
            foreach ($userIds as $userId) {
                $first = [
                    'product_id' => $productId,
                    'user_id'    => $userId,
                ];
                $result = $this->model::query()->firstOrCreate($first);
                if ($result === false) {
                    $db->rollBack();
                    throw new AppException(StatusCode::ERR_SERVER, __('exception.err_sqlerror'));
                }
            }
            $db->commit();
            return true;
        }
    }

    public function memberList($productId)
    {
        $result = UserModel::query()->selectRaw("users.id, CONCAT(users.lastname, users.firstname) as username, product_members.product_id, product_members.mail_notification, product_members.workwx_notification")
            ->join('product_members', 'users.id', '=', 'product_members.user_id')->where('product_id', $productId)->get();
        return $result;
    }

    public function addDefaultMember()
    {
        $products = ProductModel::query()->get()->toArray();
        $members = $this->productDefaultMember();
        foreach ($products as $product) {
            foreach ($members as $member) {
                if ($member > 0) {
                    $save = [
                        'product_id' => $product['id'],
                        'user_id' => $member,
                    ];
                    $this->model::firstOrCreate($save, $save);
                }
            }
        }
        return true;
    }

    /**
     * 产品默认添加的成员
     * @return array
     */
    public function productDefaultMember()
    {
        // 黄总、文晓东、邹学芳、廖华明、邓福盛、孙贵生、郑嘉文
        //$list = ['黄其勇', '文晓东', '邹学芳', '廖华明', '邓福盛', '孙贵生', '郑嘉文', '黄凤英'];
        $envList = env('DEFAULT_PRODUCT_USER_NAME');
        $list = $envList ? explode(', ', $envList) : ['黄其勇', '文晓东', '邹学芳', '廖华明', '邓福盛', '孙贵生', '郑嘉文', '黄凤英', '彭新杰', '高振兴', '陈桃', '陈倩仪', '李泳琳', '戴君豪（Linux）', '萧加结'];
        $members = make(\App\Core\Services\UserService::class)->userInfoByThirdByNames($list);
        return $members ? array_column($members, 'third_user_id') : [];
    }
}