<?php

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\Redmine\ProductChangeMailDetailModel;
use App\Model\Redmine\ProductChangeMailModel;
use App\Model\Redmine\ProductChangeRecordModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductChangeMailService extends BusinessService
{
    /**
     * @inject()
     * @var ProductChangeMailModel
     */
    protected $model;

    /**
     * 批量发送邮件
     * @param int $id 变更记录ID
     * @param array $values 发送参数
     * @return bool
     * @throws AppException
     */
    public function sendMail(array $values)
    {
        // 1. 参数验证
        $this->validateMailParams($values);
        $changeRecordId = $values['change_record_id'];
        // 2. 获取变更记录
        $change = ProductChangeRecordModel::query()
            ->with(['good'])
            ->find($changeRecordId);
        if (!$change) {
            throw new AppException(StatusCode::ERR_SERVER, '变更记录不存在');
        }

        // 3. 开始事务
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();

        try {
            //4.默认添加变更记录附件
            $values['attachments'] = $change->attachments?:[];
            // 4. 处理客户数据
            $clientIds = unique_filter_column($values['client_list'] ?? [], 'client_id');
            if (empty($clientIds)) {
                throw new AppException(StatusCode::ERR_SERVER, '客户数据无效');
            }

            // 5. 创建邮件记录
            $mail = $this->createMailRecord($changeRecordId, $values, $clientIds);

            // 6. 批量发送邮件
            $this->batchSendEmails($changeRecordId, $mail->id, $values, $values['client_list']);

            // 7. 提交事务
            $db->commit();

            // 8. 记录日志
            $this->logMailSend($changeRecordId, $mail->id, count($clientIds));
            //9.改变记录的状态
            if($change->status == 1){
                $change->status = 2;
                $change->save();
            }
            //9.查询发送失败的客户
            $failedClients = ProductChangeMailDetailModel::query()
                ->where('mail_id', $mail->id)
                ->where('send_status', 2)
                ->get();
            $failedClients = $failedClients ? $failedClients->toArray() : [];
            return $failedClients;
        } catch (Exception $e) {
            $db->rollBack();
            $this->logError($changeRecordId, $e->getMessage());
            throw new AppException(StatusCode::ERR_SERVER, '邮件发送失败：' . $e->getMessage());
        }
    }

    /**
     * 验证邮件参数
     * @param array $values
     * @throws AppException
     */
    private function validateMailParams(array $values): void
    {
        if (empty($values['client_list'])) {
            throw new AppException(StatusCode::ERR_SERVER, '请选择发送对象');
        }
        if (empty($values['mail_title'])) {
            throw new AppException(StatusCode::ERR_SERVER, '请输入邮件标题');
        }
        if (empty($values['mail_nick_name'])) {
            throw new AppException(StatusCode::ERR_SERVER, '请输入邮件昵称');
        }
        if (empty($values['mail_content'])) {
            throw new AppException(StatusCode::ERR_SERVER, '请输入邮件内容');
        }
        if (empty($values['change_record_id'])) {
            throw new AppException(StatusCode::ERR_SERVER, '变更记录ID不能为空');
        }
    }

    /**
     * 创建邮件记录
     * @param int $changeRecordId
     * @param array $values
     * @param array $clientIds
     * @return ProductChangeMailModel|\Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model
     */
    private function createMailRecord(int $changeRecordId, array $values, array $clientIds)
    {
        return $this->model::query()->create([
            'change_record_id' => $changeRecordId,
            'mail_title' => $values['mail_title'],
            'mail_nick_name' => $values['mail_nick_name'],
            'mail_content' => $values['mail_content'],
            'mail_content_html' => $values['mail_content_html'],
            'client_ids' => $clientIds,
            'created_by' => getRedmineUserId(),
        ]);
    }

    /**
     * 批量发送邮件
     * @param int $changeRecordId
     * @param int $mailId
     * @param array $values
     * @param array $clients
     */
    private function batchSendEmails(int $changeRecordId, int $mailId, array $values, array $clients): void
    {
        $batchSize = 10; // 每批发送数量
        $chunks = array_chunk($clients, $batchSize);

        foreach ($chunks as $chunk) {
            $this->processEmailChunk($changeRecordId, $mailId, $values, $chunk);
        }
    }

    /**
     * 处理一批邮件发送
     * @param int $changeRecordId
     * @param int $mailId
     * @param array $values
     * @param array $clients
     */
    private function processEmailChunk(int $changeRecordId, int $mailId, array $values, array $clients): void
    {
        //防止开发环境发送到客户邮箱
        if(env('APP_ENV') == 'dev'){
            $mailAddress = auth()->user()->id == 257 ? auth()->user()->email : '';
        }else{
            $mailAddress = '';
        }
        foreach ($clients as $client) {
            try {
                $result = sendEmail(
                    $values['mail_title'],
                    $values['mail_content_html'],
                    $mailAddress?:$client['email'],
                    $values['mail_nick_name'],
                    true
                );

                $this->createMailDetail(
                    $changeRecordId,
                    $mailId,
                    $client,
                    $result
                );
            } catch (Exception $e) {
                $this->createMailDetail(
                    $changeRecordId,
                    $mailId,
                    $client,
                    false,
                    $e->getMessage()
                );
            }
        }
    }

    /**
     * 创建邮件详情记录
     * @param int $changeRecordId
     * @param int $mailId
     * @param array $client
     * @param bool $result
     * @param string|null $errorMessage
     */
    private function createMailDetail(
        int $changeRecordId,
        int $mailId,
        array $client,
        bool $result,
        ?string $errorMessage = null
    ): void {
        ProductChangeMailDetailModel::query()->create([
            'change_record_id' => $changeRecordId,
            'mail_id' => $mailId,
            'client_id' => $client['client_id'],
            'client_company' => $client['client_company'],
            'client_name' => $client['client_name'],
            'client_contact' => $client['client_contact'],
            'client_email' => $client['email'],
            'send_status' => $result ?  1: 2,
            'send_result' => $errorMessage ?? ($result ? '发送成功' : '发送失败'),
            'created_by' => getRedmineUserId(),
        ]);
    }

    /**
     * 记录邮件发送日志
     * @param int $changeRecordId
     * @param int $mailId
     * @param int $totalCount
     */
    private function logMailSend(int $changeRecordId, int $mailId, int $totalCount): void
    {
        // 这里可以添加日志记录逻辑
        Log::get('system', 'system')->info('邮件发送完成', [
            'change_record_id' => $changeRecordId,
            'mail_id' => $mailId,
            'total_count' => $totalCount,
            'created_by' => getRedmineUserId(),
        ]);
    }

    /**
     * 记录错误日志
     * @param int $changeRecordId
     * @param string $errorMessage
     */
    private function logError(int $changeRecordId, string $errorMessage): void
    {
        Log::get('system', 'system')->info('邮件发送失败', [
            'change_record_id' => $changeRecordId,
            'error_message' => $errorMessage,
            'created_by' => getRedmineUserId(),
        ]);
    }

    /**
     * 获取邮件发送历史
     */
    public function getMailHistory(array $filter = [], array $op = [], string $sort = 'id', string $order = 'desc')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        //用户
        $query = $query->with([
            'creator',
            'detail'
        ]);
        $data = $query->orderBy($sort, $order)->get();
        $data = $data ? $data->toArray() : [];
        foreach ($data as &$item) {
            // 提取所有 client_name 并拼接
            $item['client_name'] = array_column($item['detail'] ?? [], 'client_name');
            $item['client_names'] = implode('、', $item['client_name']);

            // 提取所有 company_client_name 并拼接
            $companyClient = array_map(function ($detail) {
                if (!empty($detail['client_company'])) {
                    return $detail['client_company'] . '/' . (!empty($detail['client_name']) ? $detail['client_name'] : $detail['client_contact']);
                } else {
                    return (!empty($detail['client_name']) ? $detail['client_name'] : '') . '/' . $detail['client_contact'];
                }
            }, $item['detail'] ?: []);

            $item['company_client_names'] = implode('、', array_filter($companyClient));
        }
        //按日期分组
        $result = [];
        foreach ($data as $val) {
            $day = substr($val['created_at'], 0, 10);
            $val['created_time_text'] = TimeUtils::formatDate($val['created_at'], 'H:i');
            $result[$day][] = $val;
        }
        return $result;
    }
}