<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Redmine\ProductMilestoneModel;
use App\Model\Redmine\ProjectModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductMilestoneService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductMilestoneModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        /* @var ProductMilestoneModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $paginate = $query->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        // $projectModel = make(ProjectModel::class);
        // foreach ($paginate['data'] as &$page) {
        //     $page['projects'] = $projectModel::query()->select(['projects.id', 'projects.name'])->join('projects_ext', 'projects.id', '=', 'projects_ext.project_id')
        //         ->where('projects_ext.product_id', $page['id'])->get();
        // }
        return $paginate;
    }
}