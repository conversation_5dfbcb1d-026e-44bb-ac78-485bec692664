<?php

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\ProductCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\TchipSale\LinkageService;
use App\Core\Services\TchipSale\SaleService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\FilesDocProductVersionModel;
use App\Model\Redmine\ProductChangeMailDetailModel;
use App\Model\Redmine\ProductChangeMailModel;
use App\Model\Redmine\ProductChangeRecordModel;
use App\Model\Redmine\ProductChangeRecordGoodModel;
use App\Model\Redmine\ProductChangeRecordProjectModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\ProjectsProgressModel;
use App\Model\Redmine\UserModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use League\HTMLToMarkdown\HtmlConverter;

class ProductChangeRecordService extends BusinessService
{
    /**
     * @Inject()
     * @var ProductChangeRecordModel
     */
    protected $model;


    public function getOverView($id)
    {
        $overView = $this->model::query()->with([
            'good',
            'project',
            'creator'
        ])->find($id);

        $overView = $overView ? $overView->toArray() : [];

        if ($overView) {
            //获取用户
            $users = UserModel::query()->whereIn('id', $overView['notice_members']?:[0])->get()->keyBy('id')->toArray();
            $overView['notice_members_name'] = implode(',', $users?array_column($users,'name'):[]);
            $overView['created_by_name'] = $overView['creator']['name'] ?? '';
            //获取项目
            $projectIds = array_column($overView['project'] ?: [], 'project_id');
            $projectData = ProjectModel::query()->whereIn('id', $projectIds?:[0])->pluck('name', 'id')->toArray();
            $overView['project_name_text'] = implode(',', $projectData);
            $overView['project_version'] = $overView['project'] ? array_map(function($project) use ($projectData) {
                $version_text = '';
                if(!empty($project['version_ids'])){
                    // 确保version_ids是数组
                    $versionIds = is_string($project['version_ids']) ? json_decode($project['version_ids'], true) : $project['version_ids'];
                    if(is_array($versionIds) && !empty($versionIds)){
                        $version = FilesDocProductVersionModel::query()->whereIn('id', $versionIds)->pluck('version_name','id')->toArray();
                        $version_text = implode(',', $version);
                    }
                }
                return $projectData[$project['project_id']] . ($version_text ? '(' . $version_text . ')' : '');
            }, $overView['project']) : [];
            $overView['project_version_text'] = implode(',', array_filter($overView['project_version']));
            $overView['project_ids'] = $projectIds?:[];
            //获取产品
            $overView['product_codes'] = array_column($overView['good'] ?: [], 'product_code');
            $overView['product_code_text'] = implode(',', $overView['product_codes']);
            // 获取分类
            $category = CategoryModel::query()
                ->where('type', ProductCode::PRODUCT_CHANGE_CATEGORY)
                ->pluck('name', 'id')
                ->toArray();
            // 通知类型
            $overView['notice_type_text'] = array_match_option($overView['notice_type'], $category);
            // 变更原因
            $overView['change_reason_text'] = implode(',', $overView['change_reason']?:[]);
            // 变更影响
            $overView['change_impact_text'] = array_match_option($overView['change_impact'], $category);
            // 保密等级
            $overView['privacy_level_text'] = ProductCode::PRIVACY_LEVEL_ARR[$overView['privacy_level']] ?? '';
            // 重要级别
            $overView['importance_level_text'] = ProductCode::IMPORTANCE_LEVEL_ARR[$overView['importance_level']] ?? '';
            // 创建日期
            $overView['created_at_text'] = TimeUtils::formatDate($overView['created_at'], 'Y-m-d H:i');
            //变更内容文本
            $overView['notice_content_text'] = strip_tags($overView['notice_content_html']?:'');
            //获取最新一次邮件发送内容
            $lastMail = ProductChangeMailModel::query()->where('change_record_id', $id)->orderBy('id', 'desc')->first();
            $overView['last_mail_content'] = $lastMail ? ($lastMail->mail_content_html ?: null) : null;

            //处理附件的url
            $attachments = $overView['attachments'] ? array_map(function($attachment) {
                $attachment['url'] = buildOnlineFileUrl($attachment['url']);
                return $attachment;
            }, $overView['attachments']) : [];
            $overView['attachments'] = $attachments;
            //整理product_groups
            $projectGroups = [];
            if (!empty($overView['project'])) {
                $projectIds = array_column($overView['project'], 'project_id');
                // 按group_no分组
                $groupedProjects = [];
                foreach ($overView['project'] as &$project) {
                    $groupNo = $project['group_no'] ?? 1;
                    if (!isset($groupedProjects[$groupNo])) {
                        $groupedProjects[$groupNo] = [];
                    }
                    $groupedProjects[$groupNo][] = $project;
                    $project['name'] = $projectData[$project['project_id']] ?? '';
                    if(!empty($project['version_ids'])){
                        $version_name = FilesDocProductVersionModel::query()->whereIn('id', $project['version_ids'])->pluck('version_name','id')->toArray();
                        $project['version_name'] = implode(' 、 ', array_filter($version_name))?:'全部';
                    }else{
                        $project['version_name'] = '全部';
                    }
                }
                
                // 整理每个分组的数据
                foreach ($groupedProjects as $groupNo => $projects) {
                    $groupData = [];
                    foreach ($projects as $item) {
                        //版本名称

                        // 获取该项目的产品数据
                        $productCodes = array_filter($overView['good'] ?? [], function($good) use ($item) {
                            return $good['project_id'] == $item['project_id'];
                        });
                        
                        $groupData[] = [
                            'id' => $item['project_id'],
                            'name' => $projectData[$item['project_id']] ?? '',
                            'version_ids' => $item['version_ids'] ?? [],
                            'selected_product_code' => array_values(array_map(function($code) {
                                return [
                                    'code' => $code['product_code'],
                                    'name' => $code['product_name'],
                                    'spec' => $code['product_spec']
                                ];
                            }, $productCodes))
                        ];
                    }
                    $projectGroups[] = $groupData;
                }
            }else{
                $overView['project'] = [];
            }
            $overView['product_groups'] = array_values($projectGroups);
        }

        return $overView;
    }


    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @param string $groupMode 分组模式：'record'记录分组，'project'项目分组
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, string $groupMode = 'record')
    {
        //关键字查询
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('title', 'like', '%' . $keywords . '%')
                    ->orWhere('projects.name', 'like', '%' . $keywords . '%');
            });
        }

        if ($groupMode === 'record') {
            // 记录分组模式：每个修改记录一条数据
            return $this->getRecordGroupedList($query, $sort, $order, $limit);
        } else {
            // 项目分组模式：每个项目一条数据
            return $this->getProjectGroupedList($query, $sort, $order, $limit);
        }
    }

    /**
     * 记录分组模式
     */
    private function getRecordGroupedList($query, $sort, $order, $limit)
    {
        //获取创建人
        $query = $query->with(['creator','good']);
        //关联查询
        $query = $query->leftJoin('product_change_record_project as record_project', function ($join) {
            $join->on('product_change_record.id', '=', 'record_project.change_record_id');
            $join->whereNull('record_project.deleted_at');
        })->leftJoin('projects', 'projects.id', '=', 'record_project.project_id');
        $query = $query->select([
            'product_change_record.*',
            Db::raw('GROUP_CONCAT(DISTINCT projects.name) as project_name_text'),
        ])->groupBy('product_change_record.id');

        $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();
        
        return $this->formatListData($result);
    }

    /**
     * 项目分组模式
     */
    private function getProjectGroupedList($query, $sort, $order, $limit)
    {
        //获取创建人
        $query = $query->with(['creator','good']);
        //关联查询 - 项目分组模式下不进行GROUP BY，让每个项目单独显示
        $query = $query->leftJoin('product_change_record_project as record_project', function ($join) {
            $join->on('product_change_record.id', '=', 'record_project.change_record_id');
            $join->whereNull('record_project.deleted_at');
        })->leftJoin('projects', 'projects.id', '=', 'record_project.project_id');
        
        $query = $query->select([
            'product_change_record.*',
            'record_project.project_id',
            'record_project.group_no',
            'record_project.version_ids',
            'projects.name as project_name',
        ]);

        $result = $query->orderBy($sort, $order)->paginate($limit)->toArray();
        
        return $this->formatProjectListData($result);
    }

    /**
     * 格式化记录分组数据
     */
    private function formatListData($result)
    {
        //获取分类
        $category = CategoryModel::query()->where('type', ProductCode::PRODUCT_CHANGE_CATEGORY)->pluck('name', 'id')->toArray();
        //获取通知成员
        $members = array_column($result['data'] ?? [], 'notice_members');
        //平铺
        $members = array_merge(...$members);
        $members = unique_filter($members)?:[0];
        //获取用户
        $memberUsers = UserModel::query()->whereIn('id', $members)->get()->keyBy('id')->toArray();
        
        foreach ($result['data'] as &$item) {
            //通知类型
            $item['notice_type_text'] = array_match_option($item['notice_type'], $category);
            //变更原因
            $item['change_reason_text'] = implode(',', $item['change_reason']?:[]);
            //变更影响
            $item['change_impact_text'] = array_match_option($item['change_impact'], $category);
            //保密等级
            $item['privacy_level_text'] = ProductCode::PRIVACY_LEVEL_ARR[$item['privacy_level']] ?? '';
            //重要级别
            $item['importance_level_text'] = ProductCode::IMPORTANCE_LEVEL_ARR[$item['importance_level']] ?? '';
            //创建日期
            $item['created_at_text'] = TimeUtils::formatDate($item['created_at']);
            //通知成员
            $item['notice_members_arr'] = array_match_option($item['notice_members'], $memberUsers, true);
            //产品料号
            $item['product_codes'] = array_column($item['good'] ?? [], 'product_code');
            $item['product_code_text'] = implode(',', $item['product_codes']);
            //变更内容文本
            $item['notice_content_text'] = strip_tags($item['notice_content_html'] ?: '');
        }
        
        return $result;
    }

    /**
     * 格式化项目分组数据
     */
    private function formatProjectListData($result)
    {
        //获取分类
        $category = CategoryModel::query()->where('type', ProductCode::PRODUCT_CHANGE_CATEGORY)->pluck('name', 'id')->toArray();
        //获取通知成员
        $members = array_column($result['data'] ?? [], 'notice_members');
        //平铺
        $members = array_merge(...$members);
        $members = unique_filter($members)?:[0];
        //获取用户
        $memberUsers = UserModel::query()->whereIn('id', $members)->get()->keyBy('id')->toArray();
        
        foreach ($result['data'] as &$item) {
            //通知类型
            $item['notice_type_text'] = array_match_option($item['notice_type'], $category);
            //变更原因
            $item['change_reason_text'] = implode(',', $item['change_reason']?:[]);
            //变更影响
            $item['change_impact_text'] = array_match_option($item['change_impact'], $category);
            //保密等级
            $item['privacy_level_text'] = ProductCode::PRIVACY_LEVEL_ARR[$item['privacy_level']] ?? '';
            //重要级别
            $item['importance_level_text'] = ProductCode::IMPORTANCE_LEVEL_ARR[$item['importance_level']] ?? '';
            //创建日期
            $item['created_at_text'] = TimeUtils::formatDate($item['created_at']);
            //通知成员
            $item['notice_members_arr'] = array_match_option($item['notice_members'], $memberUsers, true);
            
            // 项目分组模式下的特殊处理
            $item['project_name_text'] = $item['project_name'] ?? '';
            
            // 获取该项目的产品数据
            if (!empty($item['good'])) {
                $projectProducts = array_filter($item['good'], function($good) use ($item) {
                    return $good['project_id'] == $item['project_id'];
                });
                $item['product_codes'] = array_column($projectProducts, 'product_code');
                $item['product_code_text'] = implode(',', $item['product_codes']);
            } else {
                $item['product_codes'] = [];
                $item['product_code_text'] = '';
            }
            
            //变更内容文本
            $item['notice_content_text'] = strip_tags($item['notice_content_html'] ?: '');
        }
        
        return $result;
    }

    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        $db->beginTransaction();
        try {

            if ($id > 0) {
                $changeId = $id;
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                if (isset($values['product_groups'])) {
                    $this->editProjectAndGood($id, $values['product_groups']);
                }
                $result = $row->update($values);

            } else {
                empty($values['created_by']) && $values['created_by'] = getRedmineUserId();
                $result = $this->model::query()->create($values);
                if (isset($values['product_groups'])) {
                    $this->editProjectAndGood($result->id, $values['product_groups']);
                }
                $changeId = $result->id;
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

        if(!empty($changeId)){
            if(!empty($values['notice_content'])){
                $this->noticeMember($changeId);
            }
        }


        return $result;
    }

    public function noticeMember($id){
        $record = $this->getOverView($id);
        if(!empty($record['notice_members'])){
            $userService = make(\App\Core\Services\UserService::class);
            $userIds = $userService->biUserIdByThirdUserId($record['notice_members']);
            $userIds = unique_filter($userIds);
            if(!empty($userIds)){
                $params = [
                    'content' => $this->buildWxNoticeContent($record),
                    'content_html' => $this->buildMailNoticeContent($record)
                ];
                make(NoticeService::class)->bindUserSend($userIds, $params, 'productChangeNotice');
            }
        }
    }

    public function buildMailNoticeContent($record)
    {
        $attachmentsLink = '';
        $updateMessage = '';

        // 处理附件
        if (!empty($record['attachments'])) {
            $attachmentsLink .= "<br> <a>附件:</a> <br>";
            foreach ($record['attachments'] as $attachment) {
                $attachmentsLink .= "<a href=\"{$attachment['url']}\">{$attachment['filename']}</a> <br>";
            }
        }

        // 处理变更原因
        $changeReasonText = '';
        if (!empty($record['change_reason_text'])) {
            $changeReasonText = "<div>变更原因: {$record['change_reason_text']}</div>";
        }

        // 处理变更影响
        $changeImpactText = '';
        if (!empty($record['change_impact_text'])) {
            $changeImpactText = "<div>变更影响: {$record['change_impact_text']}</div>";
        }

        // 处理通知内容
        $description = !empty($record['notice_content_html']) ? 
            $record['notice_content_html'] : (!empty($record['notice_content']) ? 
                $record['notice_content'] : '');

        // 处理markdown换行符
        if (is_string($description) && !hasHtmlTags($description)) {
            $descriptionArr = explode("\n", $description);
            $description = '';
            foreach ($descriptionArr as $ar) {
                $description .= $ar . '<br/>';
            }
            $description = !empty($description) ? "<div>{$description}</div>" : '';
        }

        // 处理更新信息
        $followUpAuthor = isset($record['creator']) ? ($record['creator']['name'] ?? '') : '';
        $followUpdateTime = date('Y-m-d H:i', strtotime($record['updated_at'] ?? date('Y-m-d H:i:s')));
        $updateMessage .= "更新时间：{$followUpdateTime}<br>更新人员：{$followUpAuthor}";

        // 获取项目名称
        $projectName = $record['project_name_text'] ?? '';

        // 构建URL
        $url = biFrontendHost() . '/project/productChangeDetail?change_record_id=' . $record['id'];

        $html = <<<HTML
        <p>{$projectName} 有新的产品变更通知:<br/>
        {$updateMessage}
        </p>
        {$changeReasonText}
        {$changeImpactText}
        {$description}
        {$attachmentsLink}
        <br/>
        <p><a href="{$url}" target="_blank">点击查看详情</a></p>
        <p>以上信息由系统发出，如有疑问请联系管理员。</p>
HTML;

        return $html;
    }

    public function buildWxNoticeContent($record)
    {
        // 处理通知内容
        $description = $record['notice_content'] ?? '';
        $htmlToMarkDown = new HtmlConverter();
        $isImg = false;
        
        if (is_string($description) && strpos($description, '<img') !== false) {
            $isImg = true;
        }
        
        // 正则除去图片
        if (is_string($description)) {
            $description = pregRemoveContent($description, '/<img.*?(?:>|\/>)/');
            
            if (hasHtmlTags($description)) {
                $description = $htmlToMarkDown->convert((string)$description);
                $descriptionArr = explode(PHP_EOL, $description);
                $description = '';
                foreach ($descriptionArr as $ar) {
                    $stripAr = strip_tags($ar);
                    $description .= "{$stripAr} \n\n";
                }
            }
            
            if ($isImg) {
                $description .= "\n <font color=\"warning\">内容含有图片请登陆系统查看。</font>\n";
            }
        } else {
            $description = '';
        }

        // 处理更新信息
        $updateMessage = '';
        $followUpAuthor = isset($record['creator']) ? ($record['creator']['name'] ?? '') : '';
        $followUpdateTime = date('Y-m-d H:i', strtotime($record['updated_at'] ?? date('Y-m-d H:i:s')));
        $changeReasonText = $record['change_reason_text'] ? : '无';
        $changeImpactText = $record['change_impact_text'] ? : '无';
        $updateMessage .= "变更产品：{$record['project_name_text']} \n变更原因: {$changeReasonText} \n变更影响: {$changeImpactText} \n更新时间：{$followUpdateTime} \n 更新人员：{$followUpAuthor} \n";


        // 处理附件
        $attachments_link = '';
        if (!empty($record['attachments'])) {
            $attachments_link .= "\n 附件: \n";
            foreach ($record['attachments'] as $attachment) {
                $attachments_link .= "[{$attachment['filename']}]({$attachment['url']}) \n";
            }
        }

        // 构建URL
        $url = biFrontendHost() . '/project/productChangeDetail?change_record_id=' . $record['id'];
        
        // 构建标题
        $title = $record['title'] ?? '产品变更通知';
        // 构建消息内容
        $content = <<<EOT
`产品变更通知：`
<font color="warning">{$title}</font>
{$updateMessage}
{$description}
{$attachments_link}
[点击查看详情]({$url})
EOT;

        return $content;
    }

    public function editProjectAndGood($id,$productGroup)
    {
        //先删除旧的关联
        ProductChangeRecordProjectModel::query()->where('change_record_id', $id)->delete();
        ProductChangeRecordGoodModel::query()->where('change_record_id', $id)->delete();
        //新增新的关联
        foreach($productGroup as $groupIndex => $group){
            foreach($group as $product){
                // 处理项目关联
                $projectData = [
                    'change_record_id' => $id,
                    'group_no' => $groupIndex + 1,
                    'project_id' => $product['id'],
                    'version_ids' => $product['version_ids'] ?? null,
                    'created_by' => getRedmineUserId()
                ];
                ProductChangeRecordProjectModel::query()->create($projectData);
                
                // 处理产品关联
                foreach($product['selected_product_code'] as $productCode){
                    $goodData = [
                        'change_record_id' => $id,
                        'project_id' => $product['id'],
                        'product_name' => $productCode['name'],
                        'product_code' => $productCode['code'],
                        'product_spec' => $productCode['spec'],
                        'created_by' => getRedmineUserId()
                    ];
                    ProductChangeRecordGoodModel::query()->create($goodData);
                }
            }
        }
    }


    /**
     * 删除
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {
        //添加事务
        $db = DB::connection('tchip_redmine');
        $db->beginTransaction();
        try {
            $ids = !is_array($ids)?explode(',', $ids):$ids;
            //查出数据，监测是否可以删除
            $data = $this->model::query()->whereIn('id', $ids)->get();
            if ($data->isEmpty()) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
        }
        //监测是否可以删除
        foreach ($data as $item) {
            if ($item->status == ProductCode::SEND_STATUS_SENDED) {
                throw new AppException(StatusCode::ERR_SERVER, '已发送的记录不能删除');
            }
            //删除相关表的数据
            ProductChangeRecordProjectModel::query()->where('change_record_id', $item->id)->delete();
            ProductChangeRecordGoodModel::query()->where('change_record_id', $item->id)->delete();
            ProductChangeMailDetailModel::query()->where('change_record_id', $item->id)->delete();
            ProductChangeMailModel::query()->where('change_record_id', $item->id)->delete();
        }
        $this->model::destroy($ids);
        $db->commit();
        return true;
        } catch (Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }


    /**
     * erpapi里的查询，都会有NAME的查询逻辑，所以额外添加严格按条件查询的方法
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array|mixed
     * result： {
     * "CODE": "PAS905Y401043210",
     * "NAME": "AML-S905Y4-V1.0(4G32G)(PCB日期20231128)",
     * "SPEC": "配置：4G/32G；版本：V1.0；内存：LPDDR4；",
     * "ROW_NUMBER": "1"
     * },
     */
    public function getErpGoods($filter, $op, $sort, $order, $limit = 999)
    {
        $json = [
            'filter' => $filter,
            'op' => $op,
            'sort' => $sort,
            'order' => $order,
            'limit' => $limit
        ];
        $erpData = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/stock/goods/search', ['json' => $json], 'POST');
        $data = $erpData['data']??[];
        $result = [];
        foreach ($data as $item){
            $result[] = [
                'code' => $item['CODE'],
                'name' => $item['NAME'],
                'spec' => $item['SPEC'],
            ];
        }
        return $result;
    }

    /**
     * 获取Sale客户列表
     */
    public function getSaleClient(array $filter = [], array $op = [], string $sort = 'id', string $order = 'desc', int $limit = 10)
    {
        $changeId = $filter['change_record_id'] ?? 0;
        unset($filter['change_record_id']);
        $clientData = make(SaleService::class)->getClientByProductCode($filter, $op, $sort, $order, $limit);
        if(!empty($clientData['data'])){
            //获取该记录下每个客户的已发送次数
            $clientSendCount = ProductChangeMailDetailModel::query()
                ->where('change_record_id', $changeId)
                ->select('client_id', Db::raw('COUNT(*) as send_count'))
                ->where('send_status', 1)
                ->groupBy('client_id')
                ->pluck('send_count', 'client_id')
                ->toArray();

            foreach ($clientData['data'] as &$item) {
                $item['send_count'] = $clientSendCount[$item['client_id']] ?? 0;
            }
        }

        return $clientData;
    }

    public function conf()
    {
        $categoryService = make(CategoryService::class);
        return [
            'privacy_level'    => associativelyIndex(ProductCode::PRIVACY_LEVEL_ARR),
            'importance_level' => associativelyIndex(ProductCode::IMPORTANCE_LEVEL_ARR),
            'status'      => associativelyIndex(ProductCode::SEND_STATUS_ARR),
            'notice_type'      => $categoryService->getOptionByPKey(ProductCode::PRODUCT_CHANGE_CATEGORY,ProductCode::NOTICE_TYPE_CATEGORY),
            'change_impact'    => $categoryService->getOptionByPKey(ProductCode::PRODUCT_CHANGE_CATEGORY,ProductCode::CHANGE_IMPACT_CATEGORY),
            'client_type' => make(LinkageService::class)->getOptions(['_parentId'=>1]),
            'client_level' => make(LinkageService::class)->getOptions(['_parentId'=>2]),
        ];
    }

    public function dealHistoryProductChangeNotice()
    {
        $db = DB::connection('tchip_redmine');
        $db->beginTransaction();
        try {
            $exist = ProductChangeRecordModel::query()->where('project_progress_id', '<>', 0)->pluck('project_progress_id')->toArray();
            //查询产品原有的PCN记录
            $history = ProjectsProgressModel::query()->with(['project'])->where('type', 10)->get();
            $history = $history ? $history->toArray() : [];
            
            //同步到新表
            foreach ($history as $item) {
                if (in_array($item['id'], $exist)) continue;
                $attachments = $item['attachment_reversion'] ?: [];
                $changeRecord = [
                    'title'               => ($item['project']['name'] ?? '') . ' PCN记录',
                    'notice_type'         => [],
                    'change_reason'       => [],
                    'change_impact'       => [],
                    'privacy_level'       => 3,
                    'importance_level'    => 2,
                    'notice_members'      => array_values(unique_filter(array_merge($item['mail_user']?:[], $item['workwx_user']?:[], $item['notice_user']?:[]))),
                    'notice_content'      => $item['description'],
                    'notice_content_html' => $item['description_html'],
                    'attachments'      => $attachments,
                    'project_ids'         => [$item['project']['id'] ?? 0],
                    'status'              => 1,
                    'project_progress_id' => $item['id'],
                    'created_by'          => $item['create_user_id']
                ];
                $recordResult = ProductChangeRecordModel::query()->create($changeRecord);
                if (!empty($item['project']['id'])) {
                    ProductChangeRecordProjectModel::query()->create([
                        'change_record_id' => $recordResult->id,
                        'group_no'         => 1,
                        'project_id'       => $item['project']['id'],
                        'created_by'       => $item['create_user_id'],
                    ]);
                }
            }
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}
