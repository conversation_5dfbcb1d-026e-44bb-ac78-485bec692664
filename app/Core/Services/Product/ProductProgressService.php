<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\ProductCode;
use App\Constants\StatusCode;
use App\Core\Services\Project\ProgressService;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\Setting\SettingsService;
use App\Exception\AppException;
use App\Model\Model;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProductPlatformModel;
use App\Model\Redmine\ProductProgressDetailModel;
use App\Model\Redmine\ProductProgressModel;
use App\Model\Redmine\ProductWatcherModel;
use App\Model\Redmine\ProductMemberModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\UserModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Queue\Redmine\ProductProgressWxQueue;
use App\Core\Services\AuthService;
use App\Core\Services\Redmine\AccountService;

/**
 * 产品跟进信息
 */
class ProductProgressService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductProgressModel
     */
    protected $model;

    /**
     * @Inject()
     * @var ProductProgressWxQueue
     */
    protected $productProgressWxQueue;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    /**
     * @Inject()
     * @var AccountService
     */
    protected $accountService;

    public function doEdit(int $id, array $values)
    {
        if (!empty($values['product_id']) && (empty($values['project_id']) || $values['project_id'] === 0 || $values['project_id'] === '0')) {
            $values['project_id'] = $values['product_id'];
            unset($values['product_id']);
        }
        return make(\App\Core\Services\Project\ProjectsProgressService::class)->doEdit($id, $values);
//         $exist_notification_type = true;
//         // if (isset($values['type']) && $values['type'] == 2) {
//         // if (!empty($values['project_id']) && $values['project_id'] > 0) {
//         //     return make(ProgressService::class)->doEdit($id, $values);
//         // } else if(!empty($values['product_id']) && $values['product_id'] > 0) {
//         $result = Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($id, $values, &$exist_notification_type) {
//
//             // 处理通知人员
//             if (!empty($values['notification_member']) && !empty($values['notification_type'])) {
//                 foreach ($values['notification_type'] as $ntype) {
//                     if ($ntype == 'mail') {
//                         $values['mail_user'] = array_values($values['notification_member']);
//                     } else if ($ntype == 'workwx') {
//                         $values['workwx_user'] = array_values($values['notification_member']);
//                     }
//                 }
//                 unset($values['notification_member']);
//                 unset($values['notification_type']);
//             }
//
//              else if (!empty($values['workwx_user'])) {
//                  $values['workwx_user'] = array_values($values['workwx_user']);
//                  if (empty($values['mail_user']) && (!empty($values['notification_type']) && in_array('mail', $values['notification_type']))) {
//                      $values['mail_user'] = $values['workwx_user'];
//                  }
//                  if (empty($values['notification_member']) && empty($values['mail_user']) && empty($values['workwx_user'])) {
//                      $values['mail_user'] = [];
//                      $values['workwx_user'] = [];
//                      unset($values['notification_member']);
//                      unset($values['notification_type']);
//                  }
//                  else if ( empty($values['notification_type'])) {
//                      $exist_notification_type = false;
//                      unset($values['notification_member']);
//                      unset($values['notification_type']);
//                  }
//              } else if (!empty($values['mail_user'])) {
//                  $values['mail_user'] = array_values($values['mail_user']);
//                  if (empty($values['workwx_user']) && (!empty($values['notification_type']) && in_array('workwx', $values['notification_type']))) {
//                      $values['workwx_user'] = $values['mail_user'];
//                  }
//                  if (empty($values['notification_member']) && empty($values['mail_user']) && empty($values['workwx_user'])) {
//                      $values['mail_user'] = [];
//                      $values['workwx_user'] = [];
//                      unset($values['notification_member']);
//                      unset($values['notification_type']);
//                  }
//                  else if ( empty($values['notification_type'])) {
//                      $exist_notification_type = false;
//                      unset($values['notification_member']);
//                      unset($values['notification_type']);
//                  }
//              }
//
//             // 修改属性
//             $details = [];
//             $productProgressDetailsModel = make(ProductProgressDetailModel::class);
//             if (!empty($values['details'])) {
//                 $details = $values['details'];
//                 unset($values['details']);
//             }
//             if ($id > 0) {
//                 $row = $this->model::query()->find($id);
//                 if (!$row) {
//                     throw new AppException(StatusCode::ERR_SERVER, __('exception.err_sqlerror'));
//                 }
//                 $result = $row->update($values);
//                 // 大于5才获取实例，否则提交附件一次就通知一次，需要等到按通知按钮才通知
//                 if ($result && count($values) > 5) {
//                     $result = $this->model::query()->find($id);
//                 }
//             } else {
//                 $values['create_user_id'] = $values['create_user_id'] ?? getRedmineUserId();
//                 $result = $this->model::query()->create($values);
//             }
//
//             foreach ($details as &$detail) {
//                 $detail['progress_id'] = $result->id;
//                 $detail['old_value'] = $detail['old_value'] ?? '';
//                 // Db::connection(DataBaseCode::TCHIP_REDMINE)->insert("insert into product_progress_details (`property`, `prop_key`, `old_value`, `value`, `progress_id`, `content`, `updated_at`, `created_at`) values ('{$detail['property']}', '{$detail['prop_key']}', '{$detail['old_value']}', '{$detail['value']}', '{$detail['progress_id']}', CURRENT_TIMESTAMP(5), CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())");
//                 $productProgressDetailsModel::query()->create($detail);
//             }
//             return $result;
//         });
//         // 推送通知事件
//         if (!empty($result->id)) {
//             $isPush = true;
//             if (!empty($values['details'])) {
//                 $propKey = array_column($values['details'], 'prop_key');
// //                if ($values['type'] == ProductCode::PROGRESS_TYPE['property']['value'] && !array_intersect(ProductCode::DESC_FIELD, $propKey)) {
// //                    $isPush = false;
// //                }
//             }
//             if ($isPush && $exist_notification_type) $this->productProgressWxQueue->push(['product_progress_id' => $result->id, 'product_id' => $values['product_id']]);
//         }
//         return $result;
    }

    public function getOverView($id)
    {
        $row = $this->model::query()->with(['details'])->find($id);
        $row = $row ? $row->toArray() : [];
        if (!empty($row['details'])) {
            foreach ($row['details'] as &$detail) {
                $detail['diff_value_text'] = $this->getDetailsAttr($detail['property'], $detail['prop_key'], $detail['old_value'], $detail['value']);
            }
        }
        return $row;
    }

    /**
     * 删除操作
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {
        $result = Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($ids) {
            $ids = explode(',', $ids);
            foreach ($ids as $id) {
                $result = $this->model::destroy($id);
                if ($result) {
                    ProductProgressDetailModel::query()->where('progress_id', $id)->delete();
                }
            }
        });
        return 1;
    }

    public function productFollowList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $page = 1)
    {
        if (!empty($filter['product_id']) && (empty($filter['project_id']) || $filter['project_id'] === 0 || $filter['project_id'] === '0')) {
            $filter['project_id'] = $filter['product_id'];
            unset($filter['product_id']);
        }
        return make(\App\Core\Services\Project\ProjectsProgressService::class)->productFollowList($filter, $op, $sort, $order, $limit, $page);
    }

    public function productFollowListv1(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $page = 1)
    {
        // 未指定产品ID，查询该用户下所有产品
        $ruid = getRedmineUserId();
        if (empty($filter['product_id'])) {
            if (!$this->authService->isSuper() && !$this->accountService->isAdmin()) {
                $memberProducts = ProductMemberModel::query()->where('user_id', $ruid)->pluck('product_id');
                $watcherProducts = ProductWatcherModel::query()->where('user_id', $ruid)->pluck('product_id');
                $filter['product_id'] = array_merge(($memberProducts ? $memberProducts->toArray() : [] ), ($watcherProducts ? $watcherProducts->toArray() : []));
                if ($filter['product_id']) {
                    $filter['product_id'] = implode(',', $filter['product_id']);
                    $op['product_id'] = 'IN';
                } else {
                    $filter['product_id'] = 0;
                }
            }
        } else {
            $product = ProductModel::query()->find($filter['product_id']);
            $product = $product ? $product->toArray() : [];
            if (!empty($product['relation_product_id']) && empty($filter['version']) && empty($filter['version_pre'])) {
                $filter['product_id'] = implode(',', array_merge((!is_array($filter['product_id']) ? explode(',', $filter['product_id']) : $filter['product_id']), $product['relation_product_id']));
                $op['product_id'] = 'IN';
            }
        }

        // else {
        //     $productIds = is_array($filter['product_id']) ? $filter['product_id'] : explode(',', $filter['product_id']);
        //     if (!$this->authService->isSuper() && !$this->accountService->isAdmin()) {
        //         $memberProducts = ProductMemberModel::query()->where('user_id', $ruid)->whereIn('product_id', $productIds)->pluck('product_id');
        //         $filter['product_id'] = $memberProducts ? implode(',', $memberProducts->toArray()) : null;
        //         if( $filter['product_id']) {
        //             $op['product_id'] = 'IN';
        //         }
        //     }
        // }

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $paginate = $query->with('details', 'users')->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];

        if (!empty($paginate['data'])) {

            // 获取上级回复数据
            $pids = array_filter(array_column($paginate['data'], 'pid'));
            $usernameSql = "id, pid, create_user_id, type, description, created_at, IF(username != '', username, (SELECT CONCAT(lastname, firstname) as username from users where id = product_progress.create_user_id)) as username";
            $parents = $this->model::query()->selectRaw($usernameSql)->whereIn('id', $pids)->get();
            $parents = $parents ? array_column($parents->toArray(), null, 'id') : [];

            $productIds = array_values(array_unique(array_column($paginate['data'], 'product_id')));
            $productsInfo = ProductModel::query()->whereIn('id', $productIds)->get();
            $productsInfo = $productsInfo ? array_column($productsInfo->toArray(), null, 'id') : [];
            foreach ($paginate['data'] as &$datum) {
                $datum['product'] = $productsInfo[$datum['product_id']] ?? [];
                if (!empty($datum['pid']) && !empty($parents[$datum['pid']])) {
                    $datum['parent'] = $parents[$datum['pid']];
                } else {
                    $datum['parent'] = null;
                }
            }

            // 获取被通知人的头像信息
            foreach ($paginate['data'] as &$item) {
//                $item['notice_user_message'] = [];
                if ($item['workwx_user']) {
                    foreach ($item['workwx_user'] as $user) {
                        $message = \App\Model\Redmine\UserModel::query()->where('id', $user)->first()->toArray();
                        $item['notice_user_message'][$user] = $message;
                    }
                }
                if ($item['mail_user']) {
                    foreach ($item['mail_user'] as $user) {
                        $message = \App\Model\Redmine\UserModel::query()->where('id', $user)->first()->toArray();
                        $item['notice_user_message'][$user] = $message;
                    }
                }
            }

            //获取改版记录附件信息
            // foreach ($paginate['data'] as &$item) {
            //     if ($item['type'] == 2) {
            //         if ($item['attachment_reversion']) {
            //             foreach ($item['attachment_reversion'] as $attachment) {
            //                 if ($attachment) {
            //                     $attachmentMessage = \App\Model\Redmine\AttachmentModel::query()->where('id', $attachment)->first()->toArray();
            //                     $item['attachment'][] = $attachmentMessage;
            //                 }
            //             }
            //         }
            //     }
            // }
        }
        return $paginate;

        // else {
        // $where = "product_id = {$filter['product_id']}";
        // $whereProject = "projects_ext.product_id = {$filter['product_id']}";
        // }

        // 需要存在条件才可以查询数据
        // if (!empty($where) && !empty($whereProject)) {
        //     $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
        //     $page = $page <= 0 ? 1 : $page;
        //     $offset = (($page - 1) * $limit);
        //     // $result = $db->select("select t1.*, IF(t1.username = '', CONCAT(users.lastname, users.firstname), t1.username) as username, projects.name as project_name, mail_user, workwx_user from
        //     //                        (select product_progress.id, product_progress.product_id, product.name as product_name, 0 as project_id, product_progress.create_user_id, product_progress.username, product_progress.description, product_progress.created_at, 1 as type, product_progress.mail_user, product_progress.workwx_user from product_progress left join product on product_progress.product_id = product.id where {$where} and product_progress.deleted_at is null
        //     //                        union select projects_progress.id, 0 as product_id, '' as product_name, projects_progress.project_id, create_user_id, '' as username, description, created_at, 2 as type, mail_user, workwx_user from projects_progress left join projects_ext on projects_progress.project_id = projects_ext.project_id where {$whereProject} and projects_progress.deleted_at is null) t1 left join users on t1.create_user_id = users.id left join projects on t1.project_id = projects.id order by created_at desc limit {$offset}, {$limit}");
        //     $result = $db->select("select t1.*, IF(t1.username = '', CONCAT(users.lastname, users.firstname), t1.username) as username, projects.name as project_name, mail_user, workwx_user from
        //                            (select product_progress.id, product_progress.product_id, product.name as product_name, 0 as project_id, product_progress.create_user_id, product_progress.username, product_progress.description, product_progress.created_at, 1 as type, product_progress.mail_user, product_progress.workwx_user from product_progress left join product on product_progress.product_id = product.id where {$where} and product_progress.deleted_at is null) t1 left join users on t1.create_user_id = users.id left join projects on t1.project_id = projects.id order by created_at desc limit {$offset}, {$limit}");
        // }

        // if ($result) {
        //     $userService = make(UserService::class);
        //     $projectProgressIds = collect($result)->where('type', 1)->pluck('id')->toArray();
        //     $result = json_decode(json_encode($result), true);
        //     if ($projectProgressIds) {
        //         $details = ProductProgressDetailModel::query()->whereIn('progress_id', $projectProgressIds)->get();
        //         $details = $details ? $details->toArray() : [];
        //         $this->getDetailsAttr($details);
        //
        //         foreach ($result as &$res) {
        //             $res['thumb_avatar'] = $userService->userInfoByThirdUserId($res['create_user_id'], 'redmine', 'thumb_avatar');
        //             if (is_object($res)) {
        //                 $res = object_array($res);
        //             }
        //             foreach ($details as $detail) {
        //                 if ($res['type'] == 1 && $detail['progress_id'] == $res['id']) {
        //                     $res['details'][] = $detail;
        //                 }
        //             }
        //
        //         }
        //     }
        // }
        // }

        // $notificationUser = [];
        // foreach ($result as &$res) {
        //     $res['mail_user'] = $res['mail_user'] ?: [];
        //     $res['mail_user'] = !is_array($res['mail_user']) ? json_decode($res['mail_user'], true) : $res['mail_user'];
        //     $res['workwx_user'] = $res['workwx_user'] ?: [];
        //     $res['workwx_user'] = !is_array($res['workwx_user']) ? json_decode($res['workwx_user'], true) : $res['workwx_user'];
        //     $notificationUser = array_merge($notificationUser, $res['mail_user']);
        //     $notificationUser = array_merge($notificationUser, $res['workwx_user']);
        // }
        // $notificationUser = array_unique($notificationUser);
        //
        // $notificationUserList = make(UserService::class)->getUserListByThirdUserId($notificationUser, 'redmine');
        // $notificationUserList = array_column($notificationUserList, null, 'third_user_id');
        // foreach ($result as &$res) {
        //     if (!empty($res['mail_user'])) {
        //         $res['mail_user_list'] = [];
        //         foreach ($res['mail_user'] as $mailUser) {
        //             if (!empty($notificationUserList[$mailUser])) {
        //                 $res['mail_user_list'][] = $notificationUserList[$mailUser];
        //             }
        //         }
        //     }
        //     if (!empty($res['workwx_user'])) {
        //         $res['workwx_user_list'] = [];
        //         foreach ($res['workwx_user'] as $workwxUser) {
        //             if (!empty($notificationUserList[$workwxUser])) {
        //                 $res['workwx_user_list'][] = $notificationUserList[$workwxUser];
        //             }
        //         }
        //     }
        // }
        // return $result;
    }


    public function getDetailsAttr($property, $propKey, $oldValue, $value)
    {
        // $fieldList = $this->getAttrNameList();
        // // foreach ($details as &$detail) {
        // $detail['prop_name'] = $fieldList[$detail['prop_key']] ?? $detail['prop_key'];
        if ($property == 'attr' || $property == 'ext_attr') {
            switch ($propKey) {
                case 'client_id':
                    $oldValueText = $oldValue;
                    $valueText = $value;
                    break;
                case 'charge_id':
                case 'hard_handler_uid':
                case 'soft_handler_uid':
                    $oldUser = UserModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldUser ? ($oldUser->lastname . $oldUser->firstname) : '-';
                    $user = UserModel::query()->where('id', $value)->first();
                    $valueText = $user ? ($user->lastname . $user->firstname) : '-';
                    break;
                case 'hard_handler_id':
                case 'soft_handler_id':
                    $oldValue = !is_array($oldValue) ? ($oldValue ? json_decode($oldValue, true) : [] ): $oldValue;
                    $value = !is_array($value) ? ($value ? json_decode($value, true) : []) : $value;
                    $ids = array_merge($oldValue, $value);
                $oldValueText = '';
                $valueText = '';
                    if ($ids) {
                        $users = UserModel::query()->whereIn('id', $ids)->get();
                        foreach ($users as $user) {
                            if (in_array($user->id, $oldValue)) {
                                $oldValueText = $oldValueText ? $oldValueText . ', ' . $user->name : $user->name;
                            }
                            if (in_array($user->id, $value)) {
                                $valueText = $valueText ? $valueText . ', ' . $user->name : $user->name;
                            }
                        }
                    }
                    break;
                case 'project_id':
                    $oldValue = $oldValue ? (!is_array($oldValue) ? json_decode($oldValue, true) : $oldValue) : [];
                    $value = $value ? ( !is_array($value) ? json_decode($value, true) : $value) : [];
                    $ids = array_merge($oldValue ?? [], $value ?? []);
                    if ($ids) {
                        $projects = ProjectModel::query()->whereIn('id', $ids)->get();
                        $oldValueText = '';
                        $valueText = '';
                        foreach ($projects as $project) {
                            if (in_array($project->id, $oldValue)) {
                                $oldValueText = $oldValueText ? $oldValueText . ', ' . $project->name : $project->name;
                            }
                            if (in_array($project->id, $value)) {
                                $valueText = $valueText ? $valueText . ', ' . $project->name : $project->name;
                            }
                        }
                    }
                    break;
                case 'platform_id':
                    $oldPlatform = ProductPlatformModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldPlatform ? $oldPlatform->name : '-';
                    $platform = ProductPlatformModel::query()->where('id', $value)->first();
                    $valueText = $platform ? $platform->name : '-';
                    break;
                case 'status':
                    $statuList = make(CategoryService::class)->typeChildrenList('product_status');
                    $statuList = $statuList ? array_column($statuList, null, 'keywords') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'product_type':
                    $oldType = CategoryModel::query()->where('id', $oldValue)->first();
                    $oldValueText = $oldType ? $oldType->name : '-';
                    $type = CategoryModel::query()->where('id', $value)->first();
                    $valueText = $type ? $type->name : '-';
                    break;
                case 'website_cn':
                case 'website_en':
                case 'taobao':
                case 'demo':
                case 'sale':
                case 'online_info':
                case 'img_material':
                case 'wiki':
                case 'rom':
                case 'specification_cn':
                case 'specification_en':
                case 'amazon':
                case 'tmall':
                case 'shop_en':
                    $settingService = make(SettingsService::class);
                    $statuList = $settingService->statusList('product_attr_status');
                    $statuList = $statuList ? array_column($statuList, null, 'value') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'online_status':
                    $categoryService = make(CategoryService::class);
                    $statuList = $categoryService->lists('product_online_status');
                    $statuList = $statuList ? array_column($statuList, null, 'id') : [];
                    $oldValueText = $statuList[$oldValue]['name'] ?? '-';
                    $valueText = $statuList[$value]['name'] ?? '-';
                    break;
                case 'progress':
                    $oldValueText = $oldValue ? $oldValue . '%' : '0%';
                    $valueText = $value ? $value . '%' : '0%';
                    break;
                default:
                    $oldValueText = $oldValue ?? '-';
                    $valueText = $value;
            }
        } else if ($property == 'desc_attr') {
            if (strpos($propKey, '_link') !== false) {
                $oldValueText = $oldValue ?? '-';
                $valueText = $value;
            } else {
                $oldValueText = CategoryModel::query(true)->where('keywords', $oldValue)->where('type', 'product_desc_status')->value('name');
                $valueText = CategoryModel::query(true)->where('keywords', $value)->where('type', 'product_desc_status')->value('name');
            }
        }else if ($property == 'json') {
            $oldValueText = json_decode($oldValue, true);
            $valueText = json_decode($value, true);
            foreach ($oldValueText as $tkey => $tvalue) {
                if ($tvalue == $valueText[$tkey]) {
                    unset($oldValueText[$tkey]);
                    unset($valueText[$tkey]);
                }
            }
            $oldValueText = array_values($oldValueText);
            $valueText = array_values($valueText);
        } else {
            $oldValueText = $oldValue ?? '-';
            $valueText = $value;
        }
        // }
        return [$oldValueText, $valueText];
    }

    /**
     * 获取修改的类型名称
     * @param $propKey
     * @return string
     */
    public function getAttrNameList($propKey): string
    {
        $fieldList = [
            'name'                  => '产品名称',
            'client_id'             => '客户',
            'charge_id'             => '负责人',
            'platform_id'           => '产品平台',
            'status'                => '状态',
            'itemtype'              => '项目类型',
            'plantype'              => '方案类别',
            'view_state'            => '查看权限',
            'relationpro'           => '所属产品',
            'customer'              => '顾客名称',
            'product_type'          => '产品类型',
            'yewu_handler'          => '业务员',
            'hard_handler_uid'      => '硬件负责人',
            'soft_handler_uid'      => '软件负责人',
            'hard_handler_id'       => '硬件负责人',
            'soft_handler_id'       => '软件负责人',
            'creator'               => '创建人',
            'created_date'          => '立项日期',
            'closed_date'           => '归档日期',
            'progress'              => '进度',
            'progress_status'       => '进度状态',
            'description'           => '描述',
            'website_cn'            => '中文官网',
            'website_en'            => '英文官网',
            'taobao'                => '淘宝',
            'demo'                  => '样机',
            'sale'                  => '销售',
            'online_info'           => '上线信息状态',
            'img_material'          => '图片素材状态',
            'wiki'                  => '维基',
            'rom'                   => '固件',
            'specification_cn'      => '中文规格书状态',
            'specification_en'      => '英文规格书状态',
            'specification'         => '规格书',
            'amazon'                => '亚马逊状态',
            'tmall'                 => '天猫状态',
            'shop_en'               => '英文商城',
            'website_cn_link'       => '中文官网地址',
            'website_en_link'       => '英文官网地址',
            'taobao_link'           => '淘宝地址',
            'demo_link'             => '样机地址',
            'sale_link'             => '销售地址',
            'online_info_link'      => '上线信息地址',
            'img_material_link'     => '图片素材地址',
            'wiki_link'             => '维基地址',
            'rom_link'              => '固件地址',
            'specification_cn_link' => '中文规格书地址',
            'specification_en_link' => '英文规格书地址',
            'amazon_link'           => '亚马逊地址',
            'tmall_link'            => '天猫地址',
            'shop_en_link'          => '英文商城地址',
            'online_status'         => '上线状态',
            'online_note'           => '上线注备',
            'official_desc'         => '官网',
            'sale_desc'             => '销售平台',
            'launch_desc'           => '上线准备',
            'project_id'            => '关联项目',
            'copywriting'           => '文案',
            'website_img'           => '网页图',
            'shelves_img'           => '上架图',
            'package'               => '包装',
            'wiki_cn'               => '维基中',
            'wiki_en'               => '维基英',
            'wiki_cn_link'          => '维基中地址',
            'wiki_en_link'          => '维基中地址',
            'outside_datum'         => '外发资料',



        ];


        $extList = array_column(ProductCode::EXT_FIELD, 'text', 'value');
        $fieldList = array_merge($fieldList, $extList);
        return $fieldList[$propKey] ?? '';
    }

    protected function getAttrDescFieldName($field)
    {
        $arr = [
            'color' => '颜色',
            'url'   => '地址',
            'text'  => '描述'
        ];
        return $arr[$field] ?? $field;
    }

    /**
     * 检测修改的数据是否需要推送事件
     * @param $productId
     * @param $productProgressId
     * @param array $attrs
     * @return void
     */
    protected function productEditEvent($productId, $productProgressId, array $attrs)
    {
        $onlineAttr = ['website_cn', 'website_en', 'taobao', 'demo', 'sale', 'online_info', 'img_material', 'wiki', 'rom', 'specification_cn', 'specification_en', 'amazon', 'tmall', 'shop_en', 'online_status', 'online_note'];
        $isEvent = false;
        foreach ($attrs as $attr) {
            if (!empty($attr['prop_key']) && in_array($attr['prop_key'], $onlineAttr)) {
                $isEvent = true;
                break;
            }
        }
        if ($isEvent) {
            // 这里 dispatch(object $event) 会逐个运行监听该事件的监听器
            $this->productEditQueue->push([
                'product_id' => $productId,
            ]);
        }
    }

}