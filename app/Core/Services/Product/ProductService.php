<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\ProductCode;
use App\Constants\ProjectCode;
use App\Constants\StatusCode;
use App\Core\Services\AuthService;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\Project\IssueService;
use App\Core\Services\Project\MemberService;
use App\Core\Services\Project\ProjectService;
use App\Core\Services\Queue\Redmine\ProductEditQueue;
use App\Core\Services\Redmine\AccountService;
use App\Core\Services\UserService;
use App\Core\Utils\FlowUtils;
use App\Core\Utils\Pinyin;
use App\Core\Utils\Tree;
use App\Exception\AppException;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\EnabledModuleModel;
use App\Model\Redmine\FilesDocProductModel;
use App\Model\Redmine\FlowModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\IssueRelationModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\ProductAttrModel;
use App\Model\Redmine\ProductDescValueModel;
use App\Model\Redmine\ProductExtModel;
use App\Model\Redmine\ProductMember;
use App\Model\Redmine\ProductMemberModel;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProductProgressDetailModel;
use App\Model\Redmine\ProductProgressModel;
use App\Model\Redmine\ProductWatcherModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\ProjectsAttrModel;
use App\Model\Redmine\ProjectsCustomizedModel;
use App\Model\Redmine\ProjectsDescModel;
use App\Model\Redmine\ProjectsExtModel;
use App\Model\Redmine\ProjectsInfoModel;
use App\Model\Redmine\ProjectsProgressDetailsModel;
use App\Model\Redmine\ProjectsProgressModel;
use App\Model\Redmine\ProjectsTrackerModel;
use App\Model\Redmine\ProjectsWatcherModel;
use App\Model\Redmine\TrackersModel;
use App\Model\Redmine\UserModel;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use App\Model\TchipSale\FollowDescModelTable;
use App\Model\TchipSale\FollowTableModel;
use App\Model\TchipSale\LinkageModel;
use App\Model\TchipSale\UserTableModel;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Throwable;

class ProductService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductModel
     */
    protected $model;

    /**
     * @Inject()
     * @var \App\Core\Services\UserService
     */
    protected $userService;

    /**
     * @Inject()
     * @var AccountService
     */
    protected $accountService;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    /**
     * @Inject()
     * @var ProductEditQueue
     */
    protected $productEditQueue;

    /**
     * @Inject()
     * @var ProjectService
     */
    protected $projectService;

//     public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
//     {
//         $redmineUserid = getRedmineUserId();
//         /* @var ProductModel $query */
//         // unset($filter['productWatcher']);
//
//         list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
//         $paginate = $query
//             ->with(['charge', 'member', 'productDesc'])
//             ->withCount(['productWatcher as is_watch' => function ($query) use ($redmineUserid) {
//             $query->where('user_id', $redmineUserid);
//         }, 'member as member_count']);
//
//
//
//         // 加入权限查看判断
//         // isAdmin-redmine_admin,isSuper-bi_admin
//         // if (!$this->accountService->isAdmin($redmineUserid) && !$this->authService->isSuper()) {
//         //     $paginate = $paginate->whereHas('member', function ($query) use($redmineUserid) {
//         //         $query->where('user_id', $redmineUserid);
//         //         return $query;
//         //     });
//         // }
//
//         $paginate = $paginate->orderBy($sort, $order)->paginate($limit);
//         $paginate = $paginate ? $paginate->toArray() : [];
//
//         // 产品扩展
//         $productIds = array_column($paginate['data'], 'id');
//         $extList = ProductExtModel::query()->whereIn('product_id', $productIds)->get();
//         $extList = $extList ? array_column($extList->toArray(), null, 'product_id') : [];
//
//         // 产品型类
//         $productTypeIds = array_column($paginate['data'], 'product_type');
//         $productTypes = CategoryModel::query()->whereIn('id', $productTypeIds)->get();
//         $productTypes = $productTypes ? array_column($productTypes->toArray(), null, 'id') : [];
//
//         // 产品状态
//         $productStatus = make(CategoryService::class)->typeChildrenList('product_status');
//         $productStatus = array_column($productStatus, null, 'keywords');
//
//         // 软、硬件负责人
//         $userService = make(\App\Core\Services\UserService::class);
//         $softHandleArr = array_column($paginate['data'], 'soft_handler_id');
//         $hardHandleArr = array_column($paginate['data'], 'hard_handler_id');
//         $handleIds = [];
//         foreach ($softHandleArr as $sIds) {
//             if ($sIds) {
//                 $handleIds = array_merge($handleIds, $sIds);
//             }
//         }
//         foreach ($hardHandleArr as $hIds) {
//             if ($hIds) {
//                 $handleIds = array_merge($handleIds, $hIds);
//             }
//         }
//         $handleUsers = $userService->getUserListByThirdUserId($handleIds, 'redmine');
//         $handleUsers = $handleUsers ? array_column($handleUsers, null, 'third_user_id') : [];
//
//         $initProductDesc = $this->initProductDesc(0);
//
//         $productProgress = ProductProgressModel::query()
//             ->whereIn('product_id', $productIds)
//             ->whereIn('type', [6, 7, 5])
//             ->orderBy('created_at', 'desc')
//             ->get();
//         $productProgressMap = [];
//         foreach ($productProgress as $progress) {
//             $productProgressMap[$progress->product_id][$progress->type] = $progress->toArray();
//         }
//
//         $productProjects = ProjectModel::query()->select(['projects.id', 'projects.name'])->join('projects_ext', 'projects.id', '=', 'projects_ext.project_id')
//             ->whereIn('projects_ext.product_id',$productIds)->get();
//         $productProjectsMap = [];
//         foreach ($productProjects as $project) {
//             $productProjectsMap[$project->id] = $project->toArray();
//         }
//
//         // $productAttr = $this->productAttrList();
//         foreach ($paginate['data'] as &$page) {
// //            $page['projects'] = ProjectModel::query()->select(['projects.id', 'projects.name'])->join('projects_ext', 'projects.id', '=', 'projects_ext.project_id')
// //                ->where('projects_ext.product_id', $page['id'])->get();
//
//             $page['projects'] = $productProjectsMap[$page['id']] ?? [];
//
//             $page['soft_progress_detail'] = $productProgressMap[$page['id']][6] ?? null;
//             $page['hard_progress_detail'] = $productProgressMap[$page['id']][7] ?? null;
//             $page['pack_progress_detail'] = $productProgressMap[$page['id']][5] ?? null;
//
// //            $page['soft_progress_detail'] = ProductProgressModel::query()->where('product_id', $page['id'])
// //                ->where('type', 6)->orderBy('created_at', 'desc')->first();
// //            $page['soft_progress_detail'] = $page['soft_progress_detail'] ? $page['soft_progress_detail']->toArray() : null;
// //            $page['hard_progress_detail'] = ProductProgressModel::query()->where('product_id', $page['id'])
// //                ->where('type', 7)->orderBy('created_at', 'desc')->first();
// //            $page['hard_progress_detail'] = $page['hard_progress_detail'] ? $page['hard_progress_detail']->toArray() : null;
// //            $page['pack_progress_detail'] = ProductProgressModel::query()->where('product_id', $page['id'])
// //                ->where('type', 5)->orderBy('created_at', 'desc')->first();
// //            $page['pack_progress_detail'] = $page['pack_progress_detail'] ? $page['pack_progress_detail']->toArray() : null;
//
//             // 产品描述(上线)属性列表
//             // $page['product_attr'] = $productAttr;
//
//             if (!empty($extList[$page['id']])) {
//                 $page['ext'] = $extList[$page['id']];
//             } else {
//                 $page['ext'] = null;
//             }
//
//             if (empty($page['product_desc'])) {
//                 $page['product_desc'] = $initProductDesc;
//                 foreach ($page['product_desc'] as &$po) {
//                     $po['product_id'] = $page['id'];
//                 }
//             } else {
//                 $page['product_desc'] = array_column($page['product_desc'], null, 'desc_id');
//             }
//             $page['product_type_text'] = !empty($productTypes[$page['product_type']]['name']) ? $productTypes[$page['product_type']]['name'] : '';
//             // 加入状态
//             if (isset($productStatus[$page['status']])) {
//                 $page['status_text'] = $productStatus[$page['status']]['name'];
//             } else {
//                 $page['status_text'] = '未定状态';
//             }
//
//             // 软件负责人.硬件负责人名称注入
//             $this->handleProductHandlerUser($page, $handleUsers);
//         }
//         return $paginate;
//     }

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $redmineUserid = getRedmineUserId();
        // 是否只查询关注与成员项目
        $isMyself = false;
        if (isset($filter['is_myself'])) {
            $isMyself = $filter['is_myself'] == 1 ? true : false;
            unset($filter['is_myself']);
        }

        // 成员项目,提取到外面判断哪个产品是有成员权限的
        $mProjectIds = MemberModel::query()->where('user_id', $redmineUserid)
            ->groupBy('project_id')->pluck('project_id')->toArray();
        if (!isset($filter['id']) && !isset($filter['projects.id']) && !$this->accountService->isAdmin($redmineUserid) && $isMyself) {

            // 关注项目
            $wProjectIds = ProjectsWatcherModel::query()->where('user_id', $redmineUserid)
                ->groupBy('project_id')->pluck('project_id')->toArray();
            $projectIds = array_unique(array_merge($mProjectIds, $wProjectIds));
            if (count($projectIds) > 0) {
                $filter['projects.id'] = implode(',', $projectIds);
                $op['projects.id'] = 'IN';
            }
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, make(ProjectModel::class));


        // 是否关注SQL
        $watchSql = "select id from projects_watchers where projects.id = project_id and user_id = {$redmineUserid}";

        $projectTable = 'projects';
        $extTable = 'projects_ext';
        $infoTable = 'projects_info';
        $categoryTable = 'category';
        $descTable = 'projects_desc';

        $paginate = $query
            ->selectRaw("projects.*, IF(($watchSql) is null, 0, 1) as is_watch, {$extTable}.project_type, {$extTable}.icon, {$extTable}.project_type, {$extTable}.module, {$extTable}.relation_project_id,{$extTable}.relation_product_id, {$extTable}.batch_begin_time,  {$infoTable}.category, {$infoTable}.platform, {$infoTable}.product_status, {$infoTable}.product_type, {$infoTable}.hard_handler_uid, {$infoTable}.soft_handler_uid, {$infoTable}.creator, {$infoTable}.img, {$infoTable}.created_date, {$infoTable}.closed_date, {$infoTable}.product_progress, {$infoTable}.accessories_category, {$infoTable}.size_book, {$infoTable}.material, {$infoTable}.online_status")
            ->leftJoin($extTable, "{$projectTable}.id", '=', "{$extTable}.project_id")
            ->leftJoin($infoTable, "{$projectTable}.id", '=', "{$infoTable}.project_id")
            ->with(['projectsAttr', 'flow' => function ($query) {
                $query->where('flow_type', 'project');
            }])
            ->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        if (!empty($paginate['data'])) {
            $projectIds = array_column($paginate['data'], 'id');

            // 最后跟进 2024.5.13不需要显示最后跟进 By Jason
            // $accountService = make(\App\Core\Services\Redmine\AccountService::class);
            // // 跟进权限查看
            // $visibleSql = null;
            // if (!$this->authService->isSuper() && !$accountService->isAdmin()) {
            //     $visibleNotice = ProjectCode::PROGREES_VISIBLE_NOTICE;
            //     $visibleSql = "((visible = {$visibleNotice} and (JSON_CONTAINS(mail_user->'$[*]', '{$redmineUserid}') or JSON_CONTAINS(workwx_user->'$[*]', '{$redmineUserid}'))) or visible = 1)";
            // }
            // $progress = ProjectsProgressModel::query()
            //     ->selectRaw("project_id, description")
            //     ->whereIn('project_id', $projectIds)
            //     ->whereIn('type', [1, 6, 7, 5])
            //     ->whereRaw("(project_id, created_at) IN (SELECT project_id, MAX(created_at) FROM projects_progress where description <> '' and description is not  null ". ($visibleSql ? ' and ' . $visibleSql : '')." GROUP BY project_id)")
            //     ->get();
            // $progress = $progress ? array_column($progress->toArray(), null, 'project_id') : [];
            // 最后跟进 2024.5.13 结束

            // 如果是定制产品需要统计出货数量
            $customizeds = [];
            $relation_sale_product_ids = [];
            if ((!empty($filter['category']) && $filter['category'] == 'customized') || !empty($filter['projects_info.category']) && $filter['projects_info.category'] == 'customized') {
                $customizeds    = ProjectsCustomizedModel::query()->whereIn('project_id', $projectIds)->get()->toArray();
                // $saleProductIds = array_column($customizeds, 'relation_sale_product_id');
                // $saleProductIds = array_unique(array_filter($saleProductIds));
                // $sales = ClientSaleKyptTableModel::query()
                //     ->selectRaw("prod_id, sum(num) as count")
                //     ->whereIn('prod_id', $saleProductIds)
                //     ->where('delete_time', 0)
                //     ->groupBy('prod_id')
                //     ->get()->toArray();
                // $sales = array_column($sales, null, 'prod_id');
                // foreach ($customizeds as &$customized) {
                //     if ($customized['relation_sale_product_id'] > 0 && !empty($sales[$customized['relation_sale_product_id']])) {
                //         $customized['product_sale_count'] = $sales[$customized['relation_sale_product_id']]['count'];
                //     } else {
                //         $customized['product_sale_count'] = 0;
                //     }
                // }
                $relation_sale_product_ids = array_column($customizeds, 'relation_sale_product_id');
                $sale_product_name =  LinkageModel::query()->select('id', 'text')->whereIn('id',$relation_sale_product_ids)->get()->toArray() ?? [];
                $sale_product_name = array_column($sale_product_name, 'text', 'id');

                foreach ($customizeds as &$customized) {
                    $relation_sale_product_id = $customized['relation_sale_product_id'];
                    $customized['relation_sale_product_name'] = $sale_product_name[$relation_sale_product_id] ?? '';
                }

                $customizeds = array_column($customizeds, null, 'project_id');
            }

            // 获取配件分类
            $accs = [];
            if (!empty($filter['category']) && $filter['category'] == 'accessories') {
                $accIds = array_filter(array_column($paginate['data'], 'accessories_category'));
                $accs = CategoryModel::query()->whereIn('id', $accIds)->get();
                $accs = $accs ? array_column($accs->toArray(), null, 'id') : [];
            }

            // 产品发布属性 必要的上线信息与必要的文档资料 2024.11.13
            $productPub = [];

            // 产品状态
            $productStatus = make(CategoryService::class)->typeChildrenList('product_status');
            $productStatus = array_column($productStatus, null, 'keywords');
            // 产品上线状态
            $productOnlineStatus = make(CategoryService::class)->typeChildrenList('product_online_status');
            $productOnlineStatus = array_column($productOnlineStatus, null, 'keywords');

            // 默认的产品发布数据列表
            $DefproductPub = ['num' => 0, 'content' => []];

            // 未完成的描述状态数量
            $prodDesc = ProjectsDescModel::query()
                ->leftJoin($categoryTable, "{$descTable}.desc_id", '=', "{$categoryTable}.id")
                ->whereIn('project_id', $projectIds)->where("{$categoryTable}.status", 1)->get();
            // 726=维基中, 841=淘宝上线图, 842=商城上线图, 949=SDK
            foreach ($prodDesc as $descItem) {
                if (!isset($toDoDescCount[$descItem->project_id])) {
                    $toDoDescCount[$descItem->project_id] = 0;
                }
                if (in_array($descItem->value, ProductCode::PRODUCT_DESC_TODO)) {
                    $toDoDescCount[$descItem->project_id] ++;
                    if (in_array($descItem->desc_id, [726, 841, 842, 949])) {
                        if (!isset($productPub[$descItem->project_id])) {
                            $productPub[$descItem->project_id] = $DefproductPub;
                        }
                        $productPub[$descItem->project_id]['num'] ++;
                        $productPub[$descItem->project_id]['content'][] =  (make(CategoryService::class)->getOverView($descItem['desc_id']))['name'] ?? '';
                    }
                }
            }

            $defaultToDoDescCount = make(CategoryService::class)->categoryCount('product_desc_attr', 1);

            // 获取流程节点事项
            $flows = array_filter(array_column($paginate['data'], 'flow'));
            $nodes = [];
            foreach ($flows as $flow) {
                if (!empty($flow['nodes'])) {
                    $nodes = array_merge($nodes, $flow['nodes']);
                }
            }
            $properties = array_column($nodes, 'properties');
            $issueIds = [];
            foreach ($properties as $property) {
                if (!empty($property['issue_id'])) {
                    $issueIds[] = $property['issue_id'];
                }
            }
            $issuesModel = make(IssueModel::class);
            $issues = $issuesModel::query()->whereIn('id', $issueIds)->with('assignedText')->get();
            $issues = $issues ? $issues->toArray() : [];
            foreach ($issues as &$issue) {
                // $nowYear = date('Y');
                // $startDate = $nowYear == date('Y', strtotime($issue['start_date'])) ? date('m/d', strtotime($issue['start_date'])) : date('Y/m/d', strtotime($issue['start_date']));
                $startDate = date('m/d', strtotime($issue['start_date']));
                if (!empty($issue['due_date'])) {
                    // $dueDate = $nowYear == date('Y', strtotime($issue['due_date'])) ? date('m/d', strtotime($issue['due_date'])) : date('Y/m/d', strtotime($issue['start_date']))$issue['due_date'];
                    $dueDate = date('m/d', strtotime($issue['due_date']));
                } else {
                    $dueDate = '-';
                }
                $issue['sched_date'] =  "{$startDate}~{$dueDate}";
            }
            $issues = $issues ? array_column($issues, null, 'id') : [];

            $ids = array_column($paginate['data'], 'id');

            // 获取所有项目上线信息集合
            $allDesc = ProjectsDescModel::query()
                    ->with(['descInfo'])
                ->whereIn('project_id', $ids)->get()->toArray();
            $groupedDesc = array_reduce($allDesc, function($carry, $item) {
                $projectId = $item['project_id'];
                if (!isset($carry[$projectId])) {
                    $carry[$projectId] = [];
                }
                $carry[$projectId][] = $item;
                return $carry;
            }, []);


            // 获取所有项目文件当前版本，
            $allVers = make(\App\Model\Redmine\FilesDocProductVersionModel::class)::query()
                ->whereIn('product_id', $ids)
                ->orderBy('product_id', 'desc')
                ->orderBy('sort', 'desc')
                ->get();
            $allVers = $allVers ? $allVers->toArray() : [];
            // 开始手工过滤最后一个版本
            $allFileVersion = [];
            foreach ($allVers as $ver) {
                if (empty($allFileVersion[$ver['product_id']])) {
                    $allFileVersion[$ver['product_id']] = $ver;
                }
            }
            $versionIds = array_column($allFileVersion, 'id');

            // 获取默认产品资料文件设置
            // 获取顶层节点的 ID
            $topLevelIds = make(\App\Model\Redmine\CategoryModel::class)::query()
                ->select('id')
                ->where('status', 1)
                ->where('type', 'product_doc_type')
                ->where('pid', 0);

            // 获取次层节点的 ID
            $secondLevelIds = make(\App\Model\Redmine\CategoryModel::class)::query()
                ->select('id')
                ->where('status', 1)
                ->where('type', 'product_doc_type')
                ->whereIn('pid', $topLevelIds);

            // 获取底层节点的数量
            $defaultFileCount = make(\App\Model\Redmine\CategoryModel::class)::query()
                ->where('type', 'product_doc_type')
                ->where('status', 1)
                ->whereNotIn('id', $topLevelIds)
                ->whereNotIn('id', $secondLevelIds)
                ->count();

            // 获取所有项目产品资料文件上传信息集合
            $allFileDesc = ProjectModel::query()->select('id', 'name')->with(['productFileDesc' => function ($query) {
                return $query->where('file_status', 1);
            }])
                ->whereIn('id', $ids)
                ->get();

            $needDocFileIds = [934, 919, 931];
            foreach ($allFileDesc as $prodItem) {
                if (!empty($prodItem->productFileDesc)) {
                    // 1. 当前本版
                    // 2. 是否需要上传 open_status = 1
                    // 3. 未上传的 attachment_id = !attachment_id
                    // 4. 指定文件 [底板参考设计id=934, 产品规格书（中文） = 919, 产品规格书（英文） = 931]
                    foreach ($prodItem->productFileDesc as $descItem) {
                        if (!empty($allFileVersion[$prodItem->id]['id']) &&
                            $descItem->file_version == $allFileVersion[$prodItem->id]['id'] &&
                            $descItem->open_status == 1 &&
                            (empty($descItem->attachment_id) || $descItem->attachment_id === -1) &&
                            in_array($descItem->category_id, $needDocFileIds)
                        ) {
                            if (empty($productPub[$prodItem->id])) {
                                $productPub[$prodItem->id] = $DefproductPub;
                            }
                            $productPub[$prodItem->id]['num'] ++;
                            $productPub[$prodItem->id]['content'][] = (make(CategoryService::class)->getOverView($descItem->category_id))['name'] ?? '';
                        }
                    }
                } else {
                    if (empty($productPub[$prodItem->id])) {
                        $productPub[$prodItem->id] = $DefproductPub;
                    }
                    $productPub[$prodItem->id]['num'] = $productPub[$prodItem->id]['num'] + 3;
                    $productPub[$prodItem->id]['content'] = array_merge($productPub[$prodItem->id]['content'], CategoryModel::query()->whereIn('id', $needDocFileIds)->pluck('name')->toArray());
                }
            }

            // $allFileDesc = $allFileDesc->map(function ($item) use ($allFileVersion, $defaultFileCount) {
            //         // 找到所有关联中最小的 category_pid
            //         $minCategoryId = $item->productFileDesc->min('category_pid');
            //
            //         // 过滤掉 category_pid 等于最小 category_pid 的元素 & 过滤得到当前使用的版本
            //         $filteredDesc = $item->productFileDesc->filter(function ($desc) use ($minCategoryId, $allFileVersion) {
            //             return $desc->category_pid !== $minCategoryId && $desc->file_version === $allFileVersion[$desc->product_id]['id'];
            //         });
            //
            //
            //         // 统计需要上传的但是无attachment的个数
            //         $nullAttachmentCount = $filteredDesc->filter(function ($desc) {
            //             return (is_null($desc->attachment_id) || $desc->attachment_id === -1) && $desc->open_status == 1; // 0 状态为不上传 && 是否启用
            //         })->count();
            //         // 添加统计字段到结果中
            //         $item->null_attachment_count = empty($item->productFileDesc->toArray()) ? $defaultFileCount : $nullAttachmentCount;
            //
            //         return $item;
            //     })
            //     ->toArray();

            $topCategoryId = CategoryModel::query()->where('type', 'product_doc_type')->where('pid', 0)->value('id');
            // 利用查询条件统计未上传的文件资料，存在数据并为0时代表已完成上传，大于0时代表待上传数量，没有数据代表都没有上传
            $allFileDescCount = FilesDocProductModel::query()->selectRaw('product_id, sum(IF((attachment_id is null or attachment_id <= 0) and file_status = 1, 1, 0)) as count')
                // ->where(function ($query) {
                //     $query->whereNull('file_status')->orWhere('file_status', 1);
                // })
                ->whereIn('product_id', $ids)
                ->whereIn('file_version', $versionIds)
                ->where('category_pid', '!=', $topCategoryId)
                ->where('open_status', 1)
                //     ->where(function ($query) {
                //     $query->whereNull('attachment_id')->orWhere('attachment_id', '<=', 0);
                // })
                ->groupBy('product_id')->get();

            $allFileDescCount = $allFileDescCount ? $allFileDescCount->toArray() : [];


            $allFileDescCount = array_column($allFileDescCount, null, 'product_id');

            // 获取进行中的节点信息用以前端列表展示
            $progressNodes = make(IssueModel::class)::query()->select('id', 'subject', 'project_id', 'status_id', 'due_date', 'parent_id')
                ->whereIn('project_id', $projectIds)
                //->where('status_id', '=', 12)
                ->get()
                ->groupBy('project_id')
                //->map(function ($items) {
                //    return $items->map(function ($item) {
                //        return $item->only(['id', 'subject', 'project_id', 'status_id', 'due_date', 'parent_id']);
                //    });
                //})
                ->map(function ($items) {
                    // 将集合转换为数组
                    $nodes = $items->toArray();

                    // 建立 id 与节点的映射，方便后续查找父节点
                    $nodeById = [];
                    foreach ($nodes as $node) {
                        $nodeById[$node['id']] = $node;
                    }

                    // 用于缓存计算好的深度，避免重复计算
                    $depthMemo = [];

                    // 定义一个递归闭包，用于计算每个节点的深度
                    $getDepth = function ($node) use (&$getDepth, $nodeById, &$depthMemo) {
                        if (isset($depthMemo[$node['id']])) {
                            return $depthMemo[$node['id']];
                        }
                        // 如果 parent_id 为 null，则认为是根节点，深度为 1
                        if (is_null($node['parent_id'])) {
                            $depthMemo[$node['id']] = 1;
                            return 1;
                        }
                        // 如果存在父节点，则递归计算父节点的深度，再加 1
                        if (isset($nodeById[$node['parent_id']])) {
                            $depth = $getDepth($nodeById[$node['parent_id']]) + 1;
                            $depthMemo[$node['id']] = $depth;
                            return $depth;
                        }
                        // 如果找不到父节点，也默认设置为根节点
                        $depthMemo[$node['id']] = 1;
                        return 1;
                    };

                    // 遍历每个节点，添加 depth 字段
                    foreach ($nodes as &$node) {
                        $node['depth'] = $getDepth($node);
                    }

                    // 过滤仅保留层级 <= 1 且 status_id 为 12 的节点
                    $filtered = array_filter($nodes, function ($node) {
                        return $node['depth'] <= 1 && $node['status_id'] == 12;
                    });

                    // 返回过滤后的数组（重新索引）
                    return array_values($filtered);
                });


            //return $progressNodes;

            foreach ($paginate['data'] as &$datum) {

                $datum['progress_nodes'] = $progressNodes->get($datum['id'], []);

                //是否是项目成员
                $datum['is_project_member'] = in_array($datum['id'], $mProjectIds) ? 1 : 0;
                // $datum['product_progress'] = $this->getProductProgress($datum['product_status']);
                if (isset($productStatus[$datum['product_status']])) {
                    $datum['product_status_text'] = $productStatus[$datum['product_status']]['name'];
                } else {
                    $datum['product_status_text'] = '未定状态';
                }

                if (isset($productOnlineStatus[$datum['online_status']])) {
                    $datum['online_status_text'] = $productOnlineStatus[$datum['online_status']]['name'];
                } else {
                    $datum['online_status_text'] = '未定状态';
                }

                // customizeds
                if (!empty($customizeds[$datum['id']])) {
                    $datum['project_customized'] = $customizeds[$datum['id']];
                    // 统计量产数据
                    $datum['project_customized']['product_sale_count'] =
                        $this->getCustomizedSaleCount(
                            !empty($datum['project_customized']['relation_sale_product_id']) ? (int) $datum['project_customized']['relation_sale_product_id'] : 0,
                            !empty($datum['project_customized']['sale_client_id']) ? (int) $datum['project_customized']['sale_client_id'] : 0,
                        );
                } else {
                    $datum['project_customized'] = [
                        'product_sale_count' => 0
                    ];
                }

                // 处理流程部分
                if (!empty($datum['flow']['nodes'])) {
                    $itemNodes = array_column($datum['flow']['nodes'], null, 'id');
                    foreach ($datum['flow']['nodes'] as $nkey => &$node) {
                        if (!empty($node['properties']['issue_id'])) {
                            // 节点加入事项信息
                            $node['properties']['issue'] = !empty($issues[$node['properties']['issue_id']]) ? $issues[$node['properties']['issue_id']] : null;
                            $itemNodes[$node['id']]['properties']['issue'] = $node['properties']['issue'];
                            if (!empty($node['properties']['issue'])) {
                                if (!in_array($node['properties']['issue']['subject'], FlowModel::$topNodesName)) {
                                    unset($datum['flow']['nodes'][$nkey]);
                                }
                            }
                            // 计算流程子节点数量
                            $node['properties']['children_count'] = 0;
                            $node['properties']['children_done']  = 0;
                            FlowUtils::getNodesChildCount($node['properties']['children_count'], $node['properties']['children_done'], $node, $itemNodes, $datum['flow']['edges']);
                            // if (!empty($datum['flow']['edges'])) {
                            //     foreach ($datum['flow']['edges'] as $edge) {
                            //         if ($edge['sourceNodeId'] == $node['id'] && !in_array($itemNodes[$edge['targetNodeId']]['text']['value'], FlowModel::$topNodesName)) {
                            //             if ($itemNodes[$edge['targetNodeId']]['properties']['status'] == 1) {
                            //                 $node['properties']['children_done'] ++;
                            //             }
                            //             $node['properties']['children_count'] ++;
                            //         }
                            //     }
                            // }
                        }
                    }
                    $flowService = make(\App\Core\Services\Project\FlowService::class);
                    $flowService->handleNodesSort($datum['flow']['nodes'], $datum['flow']['edges']);
                }



                $datum['product_pub'] = isset($productPub[$datum['id']]) ? $productPub[$datum['id']] : null;
                $datum['todo_desc'] = isset($toDoDescCount[$datum['id']]) ? $toDoDescCount[$datum['id']] : $defaultToDoDescCount;
                $datum['hard_handler_uid'] = !empty($datum['hard_handler_uid']) ? json_decode($datum['hard_handler_uid'], true) : null;
                $datum['soft_handler_uid'] = !empty($datum['soft_handler_uid']) ? json_decode($datum['soft_handler_uid'], true) : null;
                if (!empty($progress[$datum['id']])) {
                    $datum['last_progress_detail'] = [
                        'description' => $progress[$datum['id']]['description'],
                        'description_text' => removeMarkdownSyntax($progress[$datum['id']]['description'])
                    ];
                } else {
                    $datum['last_progress_detail'] = null;
                }

                $datum['accessories_category_name'] = !empty($accs[$datum['accessories_category']]['name']) ? $accs[$datum['accessories_category']]['name'] : '';
                // $datum['last_progress_detail'] = !empty($progress[$datum['id']]) ? $progress[$datum['id']]['description'] : null;
                // $datum['soft_progress_detail'] = $productProgressMap[$datum['id']][ProductCode::PROGRESS_TYPE['soft_progress']['value']] ?? null;
                // $datum['hard_progress_detail'] = $productProgressMap[$datum['id']][ProductCode::PROGRESS_TYPE['hard_progress']['value']] ?? null;
                // $datum['pack_progress_detail'] = $productProgressMap[$datum['id']][ProductCode::PROGRESS_TYPE['package']['value']] ?? null;

//                if (!empty($filter['category']) && $filter['category'] == 'accessories') {
                    $datum['desc'] = array_column($groupedDesc[$datum['id']] ?? [], null, 'desc_id') ?? [];
//                }
                $datum['null_attachment_count'] = isset($allFileDescCount[$datum['id']]['count']) ? $allFileDescCount[$datum['id']]['count'] : $defaultFileCount;
            }
        }
        return $paginate;
    }

    /**
     * 产品概述
     * @param $id
     * @return array|Builder|Builder[]|\Hyperf\Database\Model\Collection|Model|mixed[]|null
     */
    public function getOverViewv1($id)
    {
        $overView = $this->model::query()->with(['charge', 'hardHandler', 'softHandlerLinux', 'softHandlerAndroid', 'member', 'productDesc', 'ext'])->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if (!empty($overView['member'])) {
            $overView['member_mail']   = collect($overView['member'])->where('mail_notification', 1)->toArray();
            $overView['member_mail']   = array_column($overView['member_mail'], 'user_id');
            $overView['member_workwx'] = collect($overView['member'])->where('workwx_notification', 1)->toArray();
            $overView['member_workwx'] = array_column($overView['member_workwx'], 'user_id');
            $overView['member']        = array_column($overView['member'], 'user_id');

            $overView['member_info']        = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username, lastname, firstname')->whereIn('id', $overView['member'])->get();
            $overView['member_mail_info']   = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username')->whereIn('id', $overView['member_mail'])->get();
            $overView['member_workwx_info'] = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username')->whereIn('id', $overView['member_workwx'])->get();
        }

        // 产品描述(上线)属性列表
        $overView['product_attr'] = $this->productAttrList($id);

        if (!empty($overView['attachment'])) {
            $overView['attachment'] = is_array($overView['attachment']) ? $overView['attachment'] : json_decode($overView['attachment'], true);
            foreach ($overView['attachment'] as &$att) {
                if(!is_array($att)) {
                    $name = explode('/', $att);
                    $att = [
                        'url' => $att,
                        'filename' => $name[count($name) - 1],
                    ];
                }
            }
        }

        // 产品状态
        $productStatus = make(CategoryService::class)->typeChildrenList('product_status');
        $productStatus = array_column($productStatus, null, 'keywords');
        if (isset($productStatus[$overView['status']])) {
            $overView['status_text'] = $productStatus[$overView['status']]['name'];
        } else {
            $overView['status_text'] = '未定状态';
        }

        // 软、硬件负责人
        $this->handleProductHandlerUser($overView);

        // 线上属性
        $overView['product_desc'] = $this->initProductDesc($overView['id'], $overView['product_desc'] ?? []);

        $uid = getRedmineUserId();
        if ($uid) {
            $overView['is_watcher'] = ProductWatcherModel::query()->where('product_id', $overView['id'])->where('user_id', $uid)->first() ? 1 : 0;
        } else {
            $overView['is_watcher'] = 0;
        }
        $overView['product_type_text'] = CategoryModel::query()->where('id', $overView['product_type'])->value('name');
        return $overView;
    }

    /**
     * 获取项目数据版
     * @param $id
     * @return array|Builder|Builder[]|\Hyperf\Database\Model\Collection|Model|mixed[]|null
     */
    public function getOverView($id)
    {
        $model =  make(ProjectModel::class);
        $overView = $model::query()->with([
            'projectsExt',
            'projectsInfo',
            'projectsCustomized',
            'members',
            'projectsDesc',
            'productParent',
            'attachment' => function ($query) {
                $query->where('container_type', 'Project');
                return $query;
            },
            'flow' => function ($query){
                $query->where('flow_type', 'project');
            },
        ])->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if ($overView) {
            if (!empty($overView['members'])) {
                $overView['members']            = array_column($overView['members'], 'user_id');
                $overView['member_info']        = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username, lastname, firstname')->whereIn('id', $overView['members'])->get();
                $overView['member_mail_info']   = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username')->whereIn('id', $overView['members'])->get();

                $overView['member_workwx_info'] = UserModel::query()->selectRaw('id,CONCAT(lastname, firstname) as username')->whereIn('id', $overView['members'])->get();
            } else {
                $overView['member_info']        = [];
                $overView['member_mail_info']   = [];
                $overView['member_workwx_info'] = [];
            }

            if (!empty($overView['projects_customized'])) {
                $overView['projects_customized']['prod_name'] = '';
                if (isset($overView['projects_customized']['relation_sale_product_id']) && $overView['projects_customized']['relation_sale_product_id'] > 0) {
                    $overView['projects_customized']['prod_name'] = LinkageModel::query()->where('id', $overView['projects_customized']['relation_sale_product_id'])->value('text');
                    // 量产数量（已销售数量）

                    $overView['projects_customized']['amount'] =
                        $this->getCustomizedSaleCount(
                            (int) $overView['projects_customized']['relation_sale_product_id'],
                        !empty($overView['projects_customized']['sale_client_id']) ? (int) $overView['projects_customized']['sale_client_id'] : 0);
                }
                $overView['projects_customized']['amount'] = $overView['projects_customized']['amount'] ?? 0;
            }

            // 定制产品业务跟进员
            if (!empty($overView['projects_customized']['yewu_handler_id'])) {
                $overView['projects_customized']['yewu_handler'] = UserModel::query()->select(['id', 'lastname', 'firstname'])->where('id', $overView['projects_customized']['yewu_handler_id'])->first();
            } else {
                $overView['projects_customized']['yewu_handler'] = null;
            }

            // 产品描述(上线)属性列表
            $overView['projects_attr'] = $this->productAttrList($id);

            // 软、硬件负责人
            $this->handleProductHandlerUser($overView['projects_info']);

            // 处理附件
            if (!empty($overView['attachment'])) {
                $overView['attachment'] = is_array($overView['attachment']) ? $overView['attachment'] : json_decode($overView['attachment'], true);
                foreach ($overView['attachment'] as &$att) {
                    if(!is_array($att)) {
                        $name = explode('/', $att);
                        $att = [
                            'url' => $att,
                            'filename' => $name[count($name) - 1],
                        ];
                    }
                }
            }

            // 产品状态
            $productStatus = make(CategoryService::class)->typeChildrenList('product_status');
            $productStatus = array_column($productStatus, null, 'keywords');
            if (isset($productStatus[$overView['projects_info']['product_status']])) {
                $overView['projects_info']['status_text'] = $productStatus[$overView['projects_info']['product_status']]['name'];
            } else {
                $overView['projects_info']['status_text'] = '未定状态';
            }

            // 上线状态
            $productOnlineStatus = make(CategoryService::class)->typeChildrenList('product_online_status');
            $productOnlineStatus = array_column($productOnlineStatus, null, 'keywords');
            if (isset($productOnlineStatus[$overView['projects_info']['online_status']])) {
                $overView['projects_info']['online_status_text'] = $productOnlineStatus[$overView['projects_info']['online_status']]['name'];
            } else {
                $overView['projects_info']['online_status_text'] = '未定状态';
            }

            // 配件分类
            if (!empty($overView['projects_info']['accessories_category'])) {
                $overView['projects_info']['accessories_category_text'] = CategoryModel::query()->where('id', $overView['projects_info']['accessories_category'])->value('name') ? : '';
            }

            // // 软、硬件负责人
            // $this->handleProductHandlerUser($overView);
            //
            // 线上属性
            $overView['projects_desc'] = $this->initProductDesc($overView['id'], $overView['projects_desc'] ?? []);
            //
            $uid = getRedmineUserId();
            if ($uid) {
                $overView['is_watcher'] = ProjectsWatcherModel::query()->where('project_id', $overView['id'])->where('user_id', $uid)->first() ? 1 : 0;
            } else {
                $overView['is_watcher'] = 0;
            }
            $overView['projects_info']['product_type_text'] = CategoryModel::query()->where('id', $overView['projects_info']['product_type'])->value('name');

            // 产品系列
            $overView['projects_info']['series'] = [];
            if (!empty($overView['projects_info']['series_id'])) {
                $overView['projects_info']['series'] = CategoryModel::query()->select(['id', 'name'])->whereIn('id', $overView['projects_info']['series_id'])->get();
            }

            // 是否默认创建产品流程
            // if (empty($overView['flow']) && env('REDMINE_AUTO_CREATE_PROJECT_FLOW', true)) {
            //     try {
            //         make(\App\Core\Services\Project\FlowService::class)->createDefaultFlow($overView['id']);
            //         $overView['flow'] = FlowModel::query()->where('container_id', $overView['id'])->where('flow_type', 'project')->first();
            //     } catch (AppException $e) {
            //
            //     }
            // }

            if (!empty($overView['flow']['nodes']) && is_array($overView['flow']['nodes']) && count($overView['flow']['nodes']) > 0) {
                $overView['flow']['nodes'] = $this->getNodesIssues($overView['flow']['nodes']);
                    // $flowIssueIds = array_column(array_column($overView['flow']['nodes'], 'properties'), 'issue_id');
                    // $issues = IssueModel::query()->with('issueStatus')->whereIn('id', $flowIssueIds)->get()->toArray();
                    // $issues = array_column($issues, null, 'id');
                    // foreach ($overView['flow']['nodes'] as &$node) {
                    //     if (!empty($issues[$node['properties']['issue_id']])) {
                    //         $node['properties']['issue'] = $issues[$node['properties']['issue_id']];
                    //         $node['properties']['issue']['name'] = $node['properties']['issue']['subject'];
                    //     }
                    // }
            }

            // 20250319 流程节点标签多选
            if (!empty($overView['flow']['nodes']) && is_array($overView['flow']['nodes']) && count($overView['flow']['nodes']) > 0) {
                foreach ($overView['flow']['nodes'] as &$node) {
                    if (!empty($node['properties']['label']['customLabels'])) {
                        $node['properties']['label']['customLabelIds'] = array_column(
                            array_filter($node['properties']['label']['customLabels'], function ($label) {
                                return isset($label['id']) && !is_null($label['id']);
                            }),
                            'id'
                        );
                    } else {
                        $node['properties']['label']['customLabelIds'] = [];
                    }

                }
            }
            // 父级产品ID
            $overView['parent_product_ids'] = array_column($overView['product_parent'] ?? [], 'parent_id');
        }

        return $overView;
    }

    /**
     * 编辑产品资料
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     * @throws Throwable
     */
    public function doEditv1(int $id, array $values)
    {
        // throw new AppException(StatusCode::ERR_SERVER, __('No_results_were_found'));
        $this->formatValues($values);
        $values['platform'] = empty($values['platform']) ? '' : $values['platform'];
        $result = Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($id, $values) {
            if (isset($values['closed_date']) && $values['closed_date'] === '') {
                unset($values['closed_date']);
            }
            $saveWatcher    = []; // 关注人表
            $saveMember     = []; // 产品成员
            $saveAttr       = [];
            $isDefaultMember= $values['is_default_member'] ?? 1;
            // 通知成员，跟据notification_type判断需要通知的方式
            $notificationMember   = [];
            if (!empty($values['watcher'])) {
                $saveWatcher = is_array($values['watcher']) ? $values['watcher'] : explode(',', $values['watcher']);
                unset($values['watcher']);
            }
            if (!empty($values['member'])) {
                if (is_array($values['member'])) {
                    // 成员数据结构只取user_id
                    if (!empty($values['member'][0]['user_id'])) {
                        $saveMember = array_column($values['member'], 'user_id');
                    } else {
                        $saveMember = $values['member'];
                    }
                } else {
                    $saveMember =  explode(',', $values['member']);
                }
                unset($values['member']);
            }
            if (!empty($values['product_attr'])) {
                $saveAttr = $values['product_attr'];
                unset($values['product_attr']);
            }
            if (!empty($values['notification_member'])) {
                $notificationMember = $values['notification_member'];
                unset($values['notification_member']);
            }

            // 获取
            $descAttr = !empty($values['product_desc']) ? $values['product_desc'] : [];

            if ($id > 0) {
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('No_results_were_found'));
                }
                // 获取修改的值
                $saveProgress = [
                    'create_user_id'      => getRedmineUserId(),
                    'product_id'          => $row->id,
                    'status'              => 1,
                    'details'             => [],
                    'notification_type'   => $values['notification_type'] ?? [],
                ];

                if (in_array('mail', $saveProgress['notification_type']) && $notificationMember) {
                    $saveProgress['mail_user'] = $notificationMember;
                }
                if (in_array('workwx', $saveProgress['notification_type']) && $notificationMember) {
                    $saveProgress['workwx_user'] = $notificationMember;
                }

                $rowArray = $row->toArray();

                // 获取键名，因为创建数据时有些字段默认为null,isset函数返回false值，导致无法捕捉数据变化
                $rowKeys = array_keys($rowArray);
                foreach ($values as $key => $value) {
                    // 处理 official_desc, sale_desc, launch_desc
                    if (in_array($key, $rowKeys) && !in_array($key, ['charge_uid', 'created_at', 'updated_at', 'deleted_at']) && $value != $rowArray[$key]) {
                        $property = $this->getPropertyType($key);
                        $saveProgress['details'][] = [
                            'property'  => $property,
                            'prop_key'  => $key,
                            'old_value' => is_array($rowArray[$key]) ? json_encode($rowArray[$key]) : $rowArray[$key],
                            'value'     => is_array($value) ? json_encode($value) : $value,
                        ];
                    }
                }
                $result = $row->update($values);
                if (!empty($values['ext'])) {

                    if (!empty($values['ext']['id'])) {
                        $oldExt = ProductExtModel::query()->find($values['ext']['id']);
                        foreach ($values['ext'] as $extKey => $ext) {
                            if ($ext != $oldExt->$extKey) {
                                $saveProgress['details'][] = [
                                    'property'  => 'ext_attr',
                                    'prop_key'  => $extKey,
                                    'prop_name' => ProductCode::EXT_FIELD[$extKey]['text']  ?? $extKey,
                                    'old_value' => $oldExt->$extKey,
                                    'value'     => $ext,
                                ];
                            }
                        }
                    }

                    $values['ext']['product_id'] = $id;
                    ProductExtModel::updateOrCreate(['product_id' => $id], $values['ext']);
                    // $row->ext()->update($values['ext']);
                }
                foreach ($descAttr as $desc) {
                    $nowDesc = ProductDescValueModel::query()->where('product_id', $id)->where('desc_id', $desc['desc_id'])->first();
                    if ($nowDesc) {
                        $nowDesc = $nowDesc->toArray();
                        // 描述状态
                        if ($nowDesc['value'] != $desc['value'] || $nowDesc['url'] != $desc['url']) {
                            $propKey = CategoryModel::query()->find($desc['desc_id']);
                            $saveProgress['details'][] = [
                                'property'  => 'desc_attr',
                                'prop_key'  => $propKey->keywords ?? $desc['desc_id'],
                                'prop_name' => $propKey->name  ?? $desc['desc_id'],
                                'old_value' => json_encode(['value' => $nowDesc['value'], 'url' => $nowDesc['url']]),
                                'value'     => json_encode(['value' => $desc['value'], 'url' => $desc['url'] ?? '']),
                            ];
                        }
                        // 描述地址
                        // if ($nowDesc['url'] != $desc['url']) {
                        //     $propKey = CategoryModel::query()->find($desc['desc_id']);
                        //     $saveProgress['details'][] = [
                        //         'property'  => 'desc_attr',
                        //         'prop_key'  => $propKey->keywords ? $propKey->keywords . '_link' : $desc['desc_id'],
                        //         'prop_name' => $propKey->name  ?? $desc['desc_id'],
                        //         'old_value' => $nowDesc['url'],
                        //         'value'     => $desc['url'],
                        //     ];
                        // }
                    }
                    ProductDescValueModel::updateOrCreate([
                        'product_id' => $desc['product_id'],
                        'desc_id' => $desc['desc_id'],
                    ], $desc);
                }

                if (!$result) {
                    throw new AppException(StatusCode::ERR_SERVER, __('exception.err_sqlerror'));
                }

                // 判断是否更新硬件负责人
                $havingMembers[] = $values['charge_uid'] ?? $rowArray['charge_uid'];
                if (!empty($values['hard_handler_id'])) {
                    $havingMembers = array_merge($havingMembers, $values['hard_handler_id']);
                }
                // 判断是否更新软件负责人
                if (!empty($values['soft_handler_ud'])) {
                    $havingMembers = array_merge($havingMembers, $values['soft_handler_ud']);
                }
                // 创建或者更新成员
                foreach ($havingMembers as $memberItem) {
                    ProductMemberModel::firstOrCreate(
                        ['user_id' => $memberItem, 'product_id' => $id],
                        ['user_id' => $memberItem, 'product_id' => $id, 'mail_notification' => 0, 'workwx_notification' => 0]
                    );
                }
                // 删除已经不在列表的成员
                ProductMemberModel::query()->whereNotIn('user_id', $havingMembers)->where('product_id', $id)->delete();


                $result = $row;
            } else {
                if (!isset($values['charge_uid'])) {
                    $values['charge_uid'] = getRedmineUserId();
                }
                $result = $this->model::query()->create($values);
                $values['ext']['product_id'] = $result->id;
                $result->ext()->create($values['ext']);
                // 添加默认负责人关注人
                if (!empty($values['charge_uid'])) {
                    $saveMember[] = $values['charge_uid'];
                }
                // 添加硬件负责人
                if (!empty($values['hard_handler_id'])) {
                    $saveMember = array_merge($saveMember, $values['hard_handler_id']);
                    // $hardIds = !is_array($values['hard_handler_id']) ? explode(',', $values['hard_handler_id']) : $values['hard_handler_id'];
                    // foreach ($hardIds as $hard) {
                    //     $saveMember[] = $hard;
                    // }

                }
                // 添加软件负责人
                if (!empty($values['soft_handler_id'])) {
                    $saveMember = array_merge($saveMember, $values['hard_handler_id']);
                    // $saveMember[] = $values['soft_handler_id'];
                }

                // 是否添加默认成员
                if ($isDefaultMember) {
                    $saveMember = array_merge($saveMember, make(ProductMembersService::class)->productDefaultMember);
                }

                // 保存描述信息
                $descAttr = $this->initProductDesc($result->id, $descAttr);
                foreach ($descAttr as $desc) {
                    ProductDescValueModel::create($desc);
                }
            }

            if (!empty($result->id)) {
                $id = $result->id;
                // 保存关注人
                $saveWatcher = array_unique($saveWatcher);
                if ($saveWatcher) {
                    foreach ($saveWatcher as $watcher) {
                        ProductWatcherModel::updateOrCreate(
                            ['product_id' => $result->id, 'user_id' => $watcher],
                            ['user_id' => $watcher]
                        );
                    }
                }
                // 保存成员
                $saveMember = array_unique($saveMember);
                if ($saveMember) {
                    foreach ($saveMember as $member) {
                        ProductMemberModel::updateOrCreate(
                            ['product_id' => $result->id, 'user_id' => $member],
                            ['user_id' => $member, 'mail_notification' => 0, 'workwx_notification' => 0]
                        );
                    }
                }

                // 保存推送邮件成员
                // if ($saveMemberMail) {
                //     $mailNotification = (!empty($values['notification_type']) && in_array('mail', $values['notification_type'])) ? 1 : 0;
                //     $workwxNotification = (!empty($values['notification_type']) && in_array('workwx', $values['notification_type'])) ? 1 : 0;
                //     // if (!isset($values['notification_type'])) {
                //     //     $mailNotification = 1;
                //     // } else if (!empty($values['notification_type']) && is_array($values['notification_type'])) {
                //     //     foreach ($values['notification_type'] as $ntype) {
                //     //         if ($ntype === 'mail') {
                //     //             $mailNotification = 1;
                //     //         } else if ($ntype === 'workwx') {
                //     //             $workwxNotification = 1;
                //     //         }
                //     //     }
                //     // }
                //
                //     // if ($mailNotification || $workwxNotification) {
                //     //     $updateSave = [];
                //     //     if ($mailNotification) {
                //     //         $updateSave['mail_notification'] = 0;
                //     //     }
                //     //     if ($workwxNotification) {
                //     //         $updateSave['workwx_notification'] = 0;
                //     //     }
                //     //     if ($updateSave) {
                //     //         ProductMemberModel::query()->where('product_id', $result->id)->whereNotIn('user_id', $saveMemberMail)->update($updateSave);
                //     //     }
                //         foreach ($saveMemberMail as $member) {
                //             ProductMemberModel::updateOrCreate(
                //                 ['product_id' => $result->id, 'user_id' => $member],
                //                 ['user_id' => $member, 'mail_notification' => $mailNotification, 'workwx_notification' => $workwxNotification]
                //             );
                //         }
                //     // }
                // }

                // 是否推送消息通知事件异步队列
                if ($id && count($saveProgress['details']) > 0) {
                    $saveProgress['type'] = ProductCode::PROGRESS_TYPE['property']['value'];
                    $resultProgress = make(ProductProgressService::class)->doEdit(0, $saveProgress);
                    if (!$resultProgress) {
                        throw new AppException(StatusCode::ERR_SERVER, __('exception.err_sqlerror'));
                    }
                    // 推送异步队列事件
                    // $this->productEditEvent($id, $resultProgress->id, $saveProgress['details']);
                }

                // 保存机型属性
                foreach ($saveAttr as $attrItem) {
                    if (!empty($attrItem['id'])) {
                        ProductAttrModel::query()->updateOrCreate(
                            ['product_id'=>$id,'attr_id'=>$attrItem['id']],
                            ['attr_value_id'=>$attrItem['attr_value_id']]);
                    }
                }

                // $productAttrModel = make(ProductAttrModel::class);
                // $attrIds = array_column($saveAttr, 'id');

                // 删除不存在的属性
                // if ($attrIds) {
                //     $productAttrModel::query()->where('product_id', $result->id)->whereNotIn('id', $attrIds)->delete();
                // }
                // foreach ($saveAttr as &$attr) {
                //     if ($attr) {
                //         // 数据类型转换
                //         if (!empty($attr['attr_value_id'])) {
                //             foreach ($attr['attr_value_id'] as &$valId) {
                //                 $valId = (int) $valId;
                //             }
                //         }
                //         // 没有选择属性ID跳过
                //         if (empty($attr['attr_id'])) continue;
                //         if (!empty($attr['id'])) {
                //             $upRow = $productAttrModel::query()->find($attr['id']);
                //             foreach ($attr as $key => &$a) {
                //                 if ($key == 'id') continue;
                //                 if (isset($upRow->$key)) {
                //                     $upRow->$key = $a;
                //                 }
                //             }
                //             $upRow->save();
                //         } else {
                //             $attr['product_id'] = $result->id;
                //             $productAttrModel::query()->create($attr);
                //         }
                //     }
                // }

            }

            $result = $result ? $result->toArray() : [];
            // $result['baseinfo'] = !empty($baseinfo) && is_object($baseinfo) ? $baseinfo->toArray() : [];
            $result['watcher'] = $saveWatcher;
            return $result;
        });
        return $result;
    }

    public function doEdit($id, $values)
    {
        $service = make(\App\Core\Services\Project\ProjectService::class);
        return $service->doEdit($id, $values);
    }

    /**
     * 专用方法项目添加所属产品
     * @param $productIdqq
     * @param array $projectIds
     * @return void
     */
    public function addProject($productId, array $projectIds)
    {
        $projectExtModel = make(ProjectsExtModel::class);
        foreach ($projectIds as $projectId) {
            $projectExtModel::query()->where('project_id', $projectId)->update(['product_id' => $productId]);
        }
        return true;
    }

    public function doDelete($ids): int
    {
        return $this->projectService->doDelete($ids);
        // Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($ids) {
        //     $ids = is_array($ids) ? $ids : explode(',', $ids);
        //     $baseinfoModel = make(ProductBaseinfoModel::class);
        //     foreach ($ids as $id) {
        //         $result = $this->model::destroy($id);
        //         if (!$result) {
        //             throw new AppException(StatusCode::ERR_SERVER, __('common.Delete_fail'));
        //         }
        //     }
        // });
    }

    /**
     * 产品的项目列表
     * @return void
     */
    public function productIssueJournals($productId, array $filter = [], array $op = [], $sort = 'id', $order = 'DESC', $limit = 10)
    {
        $issuesIds = ProjectsExtModel::query()->leftJoin('issues', 'projects_ext.project_id', '=', 'issues.project_id')
            ->where('projects_ext.product_id', $productId)->pluck('issues.id');

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, JournalsModel::query());
        $paginate = $query->selectRaw('journals.*, issues.subject, IFNULL(journal_ext.parent_id, 0) as parent_id, journal_ext.quick_code')
            ->with(['details', 'userInfo'])
            ->leftJoin('issues', 'journals.journalized_id', '=', 'issues.id')
            ->leftJoin('journal_ext', 'journals.id', '=', 'journal_ext.journal_id')
            ->where('journals.journalized_type', 'Issue')->whereIn('journals.journalized_id', $issuesIds)
            ->orderBy('created_on', 'ASC')->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        foreach ($paginate['data'] as $pkey => &$pval) {
            $pval['notes_height'] = '';
            if (!empty($pval['notes'])) {
                $height = formatRedmineHeighthl($pval['notes'], $pval['journalized_id'], $pval['journalized_type']);
                $pval['notes_height'] = $height != $pval['notes'] ? $height : '';
            }

            if ($pval['parent_id'] == 0) {
                $where = "%{$pval['id']}%";
            } else {
                $where = "%|{$pval['id']}%";
            }
            $children = JournalsModel::query()->selectRaw('journals.*, IFNULL(journal_ext.parent_id, 0) as parent_id, journal_ext.quick_code')
                ->with(['userInfo'])
                ->join('journal_ext', 'journals.id', '=', 'journal_ext.journal_id')->where('journal_ext.quick_code', 'LIKE', $where)
                ->orderBy('journals.created_on', 'ASC')
                ->get();
            if ($children) {
                $children = $children->toArray();
                foreach ($children as &$child) {
                    $child['notes_height'] = '';
                    if (!empty($child['notes'])) {
                        $height = formatRedmineHeighthl($child['notes'], $child['journalized_id'], $child['journalized_type']);
                        $child['notes_height'] = $height != $child['notes'] ? $height : '';
                    }
                }
                $pval['children'] = make(Tree::class)->getTreeList($children, $pval['id'], 'parent_id', 'created_on', 'ASC');
            } else {
                $pval['children'] = [];
            }
        }
        return $paginate;
    }

    /**
     * 产品下所有项目的所有成员列表
     * @return void
     */
    public function memberList(int $productId)
    {
        $projectModel = make(ProjectsExtModel::class);
        $userIds = $projectModel::query()->join('members', 'projects_ext.project_id', '=', 'members.project_id')
            ->where('projects_ext.product_id', $productId)->groupBy('members.user_id')->pluck('members.user_id')->toArray();
        $userList = UserModel::query()->whereIn('id', $userIds)->where('status', 1)->get();
        return $userList;
    }

    /**
     * 产品集关注成员列表
     * @param int $productId
     * @return Builder[]|\Hyperf\Database\Model\Collection
     */
    public function watchersBiList(int $productId)
    {
        $productWatchersModel = make(ProductWatcherModel::class);
        $userModel = make(\App\Model\TchipBi\UserModel::class);
        $userIds = $productWatchersModel::query()->where('product_id', $productId)->groupBy('user_id')->pluck('user_id')->toArray();
        return $userModel::query()->with(['third'])->where('status', 1)->whereHas('third', function ($query) use ($userIds) {
            $query->whereIn('third_user_id', $userIds)->where('platform', 'redmine');
        })->get();

        // $userIds  = $projectModel::query()->join('members', 'projects_ext.project_id', '=', 'members.project_id')
        //     ->where('projects_ext.product_id', $productId)->groupBy('members.user_id')->pluck('members.user_id')->toArray();
        // $userList = UserModel::query()->whereIn('id', $userIds)->where('status', 1)->get();
        // return $userList;
    }

    public function formatValues(&$values)
    {
        $intFields = ['website_cn', 'website_en', 'taobao', 'foreign_shop', 'demo', 'sale', 'online_info', 'img_material', 'wiki', 'rom', 'specification_cn', 'specification_en', 'amazon', 'tmall', 'shop_en', 'online_status'];
        foreach ($intFields as $field) {
            if (isset($values[$field]) && !$values[$field]) {
                $values[$field] = (int)$values[$field];
            }
        }
    }

    /**
     * 获取待上线产品的数量
     * @return int
     */
    public function awaitOnlineCount()
    {
        $result =  $this->model::query()->where('online_status', ProductCode::AWAIT_ONLINE)->count();
        return $result;
    }

    /**
     * 获取用户关注的产品数量
     * @param $redmineUserid
     * @return int
     */
    public function watcherCount($redmineUserid = null)
    {
        $redmineUserid = $redmineUserid ?? getRedmineUserId();

        $count = 0;
        if ($redmineUserid) {
            $count = ProductWatcherModel::query()->selectRaw("DISTINCT count(product_id) as count")->where('user_id', $redmineUserid)->first();
            $count = $count->count ?? 0;
        }
        return $count;
    }

    /**
     * 检测修改的数据是否需要推送事件
     * @param $productId
     * @param $productProgressId
     * @param array $attrs
     * @return void
     */
    protected function productEditEvent($productId, $productProgressId, array $attrs)
    {
        // $onlineAttr = [
        //     'website_cn', 'website_cn_link', 'website_en', 'website_en_link', 'taobao', 'taobao_link', 'demo', 'demo_link', 'sale', 'sale_link',
        //     'online_info', 'online_info_link', 'img_material', 'img_material_link', 'wiki', 'wiki_link', 'rom', 'rom_link',
        //     'specification_cn', 'specification_cn_link', 'specification_en', 'specification_en_link', 'amazon', 'amazon_link', 'tmall', 'tmall_link',
        //     'shop_en', 'shop_en_link', 'online_status', 'online_note'
        // ];
        $onlineAttr = [
            'official_desc', 'sale_desc', 'launch_desc'
        ];
        $isEvent = false;
        foreach ($attrs as $attr) {
            if (!empty($attr['prop_key']) && in_array($attr['prop_key'], $onlineAttr)) {
                $isEvent = true;
                break;
            }
        }
        if ($isEvent) {
            $this->productEditQueue->push([
                'product_id' => $productId,
                'product_progress_id' => $productProgressId
            ]);
        }
    }

    /**
     * 软硬件负责人数据迁移到新字段
     * @return void
     */
    public function migrateHandleId()
    {
        $rows = $this->model->get();
        foreach ($rows as $row) {
            $hard = null;
            $soft = null;
            $save = [];
            // 处理硬件负责人数据
            if (!empty($row->hard_handler_uid) && $row->hard_handler_uid > 0) {
                $hard = [$row->hard_handler_uid];
            }
            if (!empty($row->soft_handler_linux_uid) && $row->soft_handler_linux_uid > 0) {
                $soft = [$row->soft_handler_linux_uid];
            }
            if (!empty($row->soft_handler_android_uid) && $row->soft_handler_android_uid > 0) {
                if ($soft) {
                    $soft[] = $row->soft_handler_android_uid;
                } else {
                    $soft = [$row->soft_handler_android_uid];
                }
            }
            if ($hard && empty($row->hard_handler_id)) {
                $save['hard_handler_id'] = $hard;
            }
            if ($soft && empty($row->soft_handler_id)) {
                $save['soft_handler_id'] = $soft;
            }

            if (count($save) > 0) {
                $row->update($save);
            }

            // 开始迁移描述数据，主要是迁移已填写的网址
            $officialDesc = !empty($row->official_desc) ? (is_array($row->official_desc) ? $row->official_desc : json_decode($row->official_desc, true)) : [];
            $saleDesc = !empty($row->sale_desc) ? (is_array($row->sale_desc) ? $row->sale_desc : json_decode($row->sale_desc, true)) : [];
            $lauchDesc = !empty($row->launch_desc) ? (is_array($row->launch_desc) ? $row->launch_desc : json_decode($row->launch_desc, true)) : [];
            $categoryCacheArr = [];
            $descArr = array_merge($officialDesc, $saleDesc, $lauchDesc);
            foreach ($descArr as $desc) {
                if (!empty($desc['name'])) {
                    if (empty($categoryCacheArr[$desc['name']])) {
                        $cate = CategoryModel::query()->where('name', $desc['name'])->where('type', 'product_desc_attr')->first();
                        if ($cate) {
                            $categoryCacheArr[$desc['name']] = $cate->toArray();
                            // text
                            $descSave = ['product_id' => $row->id, 'desc_id' => $categoryCacheArr[$desc['name']]['id'], 'url' => $desc['url'] ?? null];
                            if (!empty($desc['url'])) {
                                // 如果已经有地址默认为已上线
                                $descSave['value'] = 5;
                            } else {
                                // 没有地址默认为待处理
                                $descSave['value'] = 2;
                            }
                            ProductDescValueModel::firstOrCreate(['product_id' => $row->id, 'desc_id' => $categoryCacheArr[$desc['name']]['id']], $descSave);
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 同步销售系统中定制项目到redmine产品中
     * @return void
     * @throws Throwable
     */
    public function saleFollowMigrateProduct()
    {
        $follows = FollowTableModel::query()->get();
        $userService = make(UserService::class);

        $followIds = $follows->pluck('id')->toArray();
        $products = ProductModel::query()
            ->whereIn('origin_product_id', $followIds)
            ->where('category', '=', 'customized')
            ->get();
        $productsMap = $products->keyBy('origin_product_id');

        foreach ($follows as $follow) {
            $prod = $productsMap[$follow->id] ?? null;
//            $prod = ProductModel::query()->where('origin_product_id', $follow->id)->where('category', 'customized')->first();
            if (!$prod){
                // userid(创建人) business_follow_uid(跟进业务人)
                $saleUser = $saleBusiness = null;
                if (!empty($follow->userid)) {
                    $saleUser = UserTableModel::query()->find($follow->userid);
                    if ($saleUser) {
                        $saleUser = $userService->userInfoByThirdByName($saleUser->username, 'redmine', 'third_user_id');
                    }
                }

                if (!empty($follow->business_follow_uid)) {
                    $saleBusiness = UserTableModel::query()->find($follow->business_follow_uid);
                    if ($saleBusiness) {
                        $saleBusiness = $userService->userInfoByThirdByName($saleBusiness->username, 'redmine', 'third_user_id');
                    }
                }

                Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($follow, $saleUser, $saleBusiness) {
                    $productPlatform = LinkageModel::query()->where('id', $follow->chip_platform)->first();

                    $save = [
                        'origin_product_id' => $follow->id,
                        'name'              => $follow->projectname,
                        'category'          => 'customized', // 定制分类
                        'client_id'         => $follow->clientid,
                        'charge_uid'        => $saleBusiness ?? ($saleUser ?? 0), // 负责人ID
                        // 'platform_id' => $follow->
                        'platform'          => $productPlatform->text ?? '',
                        // 'status' =>
                        // 'itemtype' => // 项目类型
                        // 'plantype' => // 方案类别
                        // 'view_state' => // 查看权限
                        'relationpro'       => $follow->relationpro, // 所属产品ID
                        // 'customer' => '' // 顾客名称
                        // 'product_type' =>
                        'yewu_handler'      => $saleBusiness ?? ($saleUser ?? 0),
                        'creator'           => $saleUser ?? 0, // 创建人
                        // 'created_date'      => $follow->time,
                        // 'created_at'        => $follow->time,
                        // 'updated_at'        => $follow->uptime,
                        'progress'          => $follow->progress,
                        // 'progress_status'  =>
                        'description'       => $follow->note,
                        'status'            => $follow->type ?? 0,
                        'attachment'        => !is_array($follow->attachment) ? json_decode($follow->attachment, true) : $follow->attachment,
                        'ext'               => [
                            'urgency'            => $follow->urgency,
                            'shipment_predicted' => $follow->shipment_predicted ?? 0,
                            'curr'               => $follow->curr ?? '',
                            'project_industry'   => $follow->project_industry ?? '',
                            // 'customized_status'  => $follow->customized_status ?? 0,
                            'cost'               => $follow->cost ?? 0,
                            'company_website'    => $follow->company_website ?? '',
                            'company_background' => $follow->company_background ?? '',
                            'sale_client_id'          => $follow->clientid ?? 0,
                        ]
                    ];
                    if (!empty($follow->uptime)) {
                        $save['updated_at'] = date('Y-m-d H:i:s', $follow->uptime);
                    }
                    if (!empty($follow->time)) {
                        $save['created_date'] = date('Y-m-d 00:00:00', strtotime($follow->time));
                        $save['created_at'] = date('Y-m-d H:i:s', strtotime($follow->time));
                    }
                    $this->doEdit(0, $save);
                });
            }
        }
    }

    /**
     * 获取产品上线跟进表配置字段
     * @param $code
     * @return array[]
     */
    public function getFollowCode($code): array
    {
        switch ($code){
            case 'sale_desc':
                return ProductCode::SALE_DESC;
            case 'launch_desc':
                return ProductCode::LAUNCH_DESC;
            case 'official_desc':
            default:
                return ProductCode::OFFICIAL_DESC;
        }
    }

    /**
     *
     * @param $prop
     * @return void
     */
    public function getPropertyType($prop)
    {
        if (in_array($prop, ProductCode::DESC_FIELD)) {
            $attr = 'json';
        } else {
            $attr = 'attr';
        }
        return $attr;
    }

    public function getProgressTypeList($in = [], $notin = [])
    {
        $list = ProductCode::PROGRESS_TYPE;
        if ($in) {
            foreach ($list as $k => $i) {
                if (in_array($k, $in)) {
                    unset($list[$i]);
                }
            }
        } else if ($notin) {
            foreach ($notin as $i) {
                if (isset($list[$i])) {
                    unset($list[$i]);
                }
            }
        }
        return array_values($list);
    }

    public function initProductDesc($productId, $productDesc = null)
    {
        // 键值对转换
        if (!empty($productDesc)) {
            $productDesc = array_column(array_values($productDesc), null, 'desc_id');
        }
        $treeRows = make(\App\Core\Services\Project\CategoryService::class)->getTree(['type'=> 'product_desc_attr', 'pid' => 0, 'status' => 1], ['pid' => '>']);
        $rows = [];
        $defaultValue = 0;
        $defaultUrl = '';
        if ($treeRows) {
            foreach ($treeRows as $tr) {
                if (!empty($tr['children'])) {
                    foreach ($tr['children'] as $ct) {
                        $rows[$ct['id']] = [
                            'project_id' => $productId,
                            'desc_id' =>  $ct['id'],
                            'value' => $defaultValue,
                            'url' => $defaultUrl,
                        ];
                    }
                }
            }
        }
        if (!empty($productDesc)) {
            foreach ($rows as $key => $row) {
                if (empty($productDesc[$key])) {
                    $productDesc[$key] = [
                        'project_id' => $productId,
                        'desc_id' => $key,
                        'value' => $defaultValue,
                        'url' => $defaultUrl,
                    ];
                }
            }
            $rows = $productDesc;
        }
        return $rows;
    }

    /**
     * 处理overView['projects_info']里的所有负责人数据
     */
    public function handleProductHandlerUser(&$overView, array $handleUsers = [])
    {
        if (empty($handleUsers)) {
            $userService = make(\App\Core\Services\UserService::class);
            $handleArr = !empty($overView['soft_handler_uid']) ? $overView['soft_handler_uid'] : [];
            $handleArr = array_merge($handleArr, (!empty($overView['hard_handler_uid']) ? $overView['hard_handler_uid'] : []));
            $handleArr = array_merge($handleArr, (!empty($overView['product_manager_uid']) ? [$overView['product_manager_uid']] : []));
            $handleArr = array_merge($handleArr, (!empty($overView['purchaser_id']) ? $overView['purchaser_id'] : []));
            $handleArr = array_merge($handleArr, (!empty($overView['architect_id']) ? $overView['architect_id'] : []));
            $handleUsers = $handleArr ? $userService->getUserListByThirdUserId($handleArr, 'redmine') : [];
            $handleUsers = $handleUsers ? array_column($handleUsers, null, 'third_user_id') : [];
        }
        // 软件负责人名称注入
        $overView['soft_handler'] = [];
        if (!empty($overView['soft_handler_uid'])) {
            foreach ($overView['soft_handler_uid'] as $suid) {
                if (!empty($handleUsers[$suid])) {
                    $overView['soft_handler'][] = [
                        'id' => $handleUsers[$suid]['third_user_id'],
                        'name' => $handleUsers[$suid]['name'],
                        'thumb_avatar' => $handleUsers[$suid]['thumb_avatar'],
                    ];
                }
            }
        }

        // 硬件负责人名称注入
        $overView['hard_handler'] = [];
        if (!empty($overView['hard_handler_uid'])) {
            foreach ($overView['hard_handler_uid'] as $suid) {
                if (!empty($handleUsers[$suid])) {
                    $overView['hard_handler'][] = [
                        'id' => $handleUsers[$suid]['third_user_id'],
                        'name' => $handleUsers[$suid]['name'],
                        'thumb_avatar' => $handleUsers[$suid]['thumb_avatar'],
                    ];
                }
            }
        }

        // 采购负责人
        $overView['purchaser'] = [];
        if (!empty($overView['purchaser_id'])) {
            foreach ($overView['purchaser_id'] as $suid) {
                if (!empty($handleUsers[$suid])) {
                    $overView['purchaser'][] = [
                        'id' => $handleUsers[$suid]['third_user_id'],
                        'name' => $handleUsers[$suid]['name'],
                        'thumb_avatar' => $handleUsers[$suid]['thumb_avatar'],
                    ];
                }
            }
        }

        // 结构负责人
        $overView['architect'] = [];
        if (!empty($overView['architect_id'])) {
            foreach ($overView['architect_id'] as $suid) {
                if (!empty($handleUsers[$suid])) {
                    $overView['architect'][] = [
                        'id' => $handleUsers[$suid]['third_user_id'],
                        'name' => $handleUsers[$suid]['name'],
                        'thumb_avatar' => $handleUsers[$suid]['thumb_avatar'],
                    ];
                }
            }
        }

        // 产品负责人
        $overView['product_manager'] = [];
        if (!empty($overView['product_manager_uid']) && !empty($handleUsers[$overView['product_manager_uid']])) {
            $overView['product_manager'][] = [
                'id' => $handleUsers[$overView['product_manager_uid']]['third_user_id'],
                'name' => $handleUsers[$overView['product_manager_uid']]['name'],
                'thumb_avatar' => $handleUsers[$overView['product_manager_uid']]['thumb_avatar'],
            ];
        }
    }

    /**
     * 获取产品属性列表
     * @param $productId
     * @return array|Builder[]|\Hyperf\Database\Model\Collection|mixed[]
     */
    public function productAttrList($productId)
    {
        $productAttrItems = CategoryModel::query()->where('pid', 0)
            ->where('type', 'product_attr')->where('status', 1)->orderBy('sort', 'desc')->get();
        if ($productAttrItems) {
            if (count($productAttrItems) == 1) {
                $productAttrItems = CategoryModel::query()->where('pid', $productAttrItems[0]->id)
                    ->where('type', 'product_attr')->where('status', 1)->get();
                $productAttrItems = $productAttrItems ? $productAttrItems->toArray() : [];
            }
            foreach ($productAttrItems as &$productAttrItem) {
                $productAttrItem['value'] = CategoryModel::query()->where('pid', $productAttrItem['id'])
                    ->where('status', 1)
                    ->orderByDesc('name')->orderBy('sort', 'desc')->get();
                $productAttrItem['attr_value_id'] = \App\Model\Redmine\ProjectsAttrModel::query()->where('project_id', $productId)->where('attr_id', $productAttrItem['id'])
                        ->value('attr_value_id') ?? [];
            }
        }
        return $productAttrItems;
    }


    /**
     * 删除多个descvalue
     * @return void
     */
    public function uploadProductDescValue()
    {
        $products = $this->model::withTrashed()->get();
        $descs = CategoryModel::query()->where('pid', '>', 0)
            ->where('type', 'product_desc_attr')->get();
        foreach ($products as $product) {
            if ($product->deleted_at !== null) {
                ProductDescValueModel::where('product_id', $product->id)->delete();
                continue;
            }
            foreach ($descs as $desc) {
                $descValue = ProductDescValueModel::query()->where('product_id', $product->id)->where('desc_id', $desc->id)
                    ->orderBy('id')->get();
                // 更新默认为0
                $descValue = $descValue ? $descValue->toArray() : [];
                if (count($descValue) > 0) {
                    if ($descValue[0]['value'] === null) {
                        ProductDescValueModel::query()->where('id', $descValue[0]['id'])->update(['value' => 0]);
                    }
                    // 删除多条数据
                    if (count($descValue) > 1) {
                        foreach ($descValue as $key => $value) {
                            if ($key > 0) {
                                ProductDescValueModel::destroy($value['id']);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取产品进度
     * @param $status
     * @return int
     */
    public function getProductProgress($status)
    {
        $value = 0;
        switch ($status) {
            case ProductCode::PRODUCT_STATUS_ASSESS:
                $value = 20;
                break;
            case ProductCode::PRODUCT_STATUS_DEV:
                $value = 40;
                break;
            case ProductCode::PRODUCT_STATUS_TP:
                $value = 60;
                break;
            case ProductCode::PRODUCT_STATUS_MP:
                $value = 80;
                break;
            case ProductCode::PRODUCT_STATUS_PAUSE:
            case ProductCode::PRODUCT_STATUS_DONE:
                $value = 100;
                break;
        }
        return $value;
    }

    public function productMigrateProject()
    {
        $products = $this->model::query()->orderBy('id', 'asc')->get()->toArray();
        $taskTracker = TrackersModel::query()->where('name', '重要事项')->value('id');
        foreach ($products as $product) {
            Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($product, $taskTracker) {
                $project = ProjectModel::query()->where('name', $product['name'])->first();
                if (empty($project->id)) {
                    $productExt = ProductExtModel::query()->where('product_id', $product['id'])->first();
                    // projects
                    $description = $product['description'] ? (strlen($product['description']) > 65534 ? '' : $product['description']) : '';
                    $saveProject = [
                        'name'                => $product['name'],
                        'description'         => $description,
                        'identifier'          => strtolower(Pinyin::get($product['name'])),
                        'status'              => 1,
                        'lft'                 => 0,
                        'rgt'                 => 0,
                        'created_on'          => date('Y-m-d H:i:s'),
                        'is_public'           => 0,
                        'projects_ext'        => [
                            'project_type'        => ProjectCode::PROJECT_TYPE_PROD,
                            'module'              => ["projects_info", "projects_attr", "projects_desc", "projects_customized"],
                            'relation_project_id' => $product['relation_project_id'] ?? null,
                            'relation_product_id' => $product['relation_product_id'] ?? null,
                        ],
                        'projects_info'       => [
                            'category'         => !empty($product['category']) ? $product['category'] : 'opensource',
                            'platform'         => $product['platform'],
                            'product_status'   => $product['status'],
                            'product_type'     => $product['product_type'],
                            'product_progress' => $product['progress'],
                            'creator'          => $product['creator'],
                            'img'              => $product['img'],
                            'created_date'     => (empty($product['created_date']) || $product['created_date'] == '0000-00-00' || $product['created_date'] == '0000-00-00 00:00:00') ? date('Y-m-d') : date('Y-m-d', strtotime($product['created_date'])),
                        ],
                        'projects_attr'       => [],
                        'projects_desc'       => [],
                        'projects_customized' => [
                            'urgency'            => $productExt->urgency ?? 0,
                            'yewu_handler_id'    => $product['charge_uid'] ?? 0,
                            'shipment_predicted' => $productExt->shipment_predicted ?? 0,
                            'amount'             => $productExt->amount ?? 0,
                            'curr'               => $productExt->curr ?? '人民币',
                            'cost'               => $productExt->cost ?? 0,
                            'project_industry'   => $productExt->project_industry ?? '',
                            'company_website'    => $productExt->company_website ?? '',
                            'company_background' => $productExt->company_background ?? '',
                            'sale_client_id'     => $productExt->sale_client_id ?? 0,
                        ],
                    ];
                    // 负责人，json字段处理有数据才需要加入
                    if (!empty($product['hard_handler_id'])) {
                        $saveProject['projects_info']['hard_handler_uid'] = $product['hard_handler_id'];
                    }
                    if (!empty($product['soft_handler_id'])) {
                        $saveProject['projects_info']['soft_handler_uid'] = $product['soft_handler_id'];
                    }
                    if (!empty($product['closed_date'])) {
                        $saveProject['projects_info']['closed_date'] = $product['closed_date'];
                    }

                    $productAttr = ProductAttrModel::query()->where('product_id', $product['id'])->get();
                    foreach ($productAttr as $attr) {
                        $saveProject['projects_attr'][] = [
                            'attr_id'       => $attr->attr_id,
                            'attr_value_id' => $attr->attr_value_id,
                        ];
                    }
                    $productDesc = ProductDescValueModel::query()->where('product_id', $product['id'])->get();
                    foreach ($productDesc as $desc) {
                        if (!isset($saveProject['projects_desc'][$desc->desc_id])) {
                            $saveProject['projects_desc'][$desc->desc_id] = [
                                'desc_id' => $desc->desc_id,
                                'value'   => $desc->value,
                                'url'     => $desc->url,
                            ];
                        }
                    }
                    $saveProject['projects_desc'] = array_values($saveProject['projects_desc']);
                    $saveProject['members']       = ProductMemberModel::query()->where('product_id', $product['id'])->pluck('user_id');
                    $saveProject['members']       = $saveProject['members'] ? $saveProject['members']->toArray() : null;
                    $saveProject['watchers']      = ProductWatcherModel::query()->where('product_id', $product['id'])->pluck('user_id');
                    $saveProject['watchers']      = $saveProject['watchers'] ? $saveProject['watchers']->toArray() : null;


                    // 创建主项目
                    $result = ProjectModel::create($saveProject);

                    if ($result) {

                        // 添加事项模块
                        EnabledModuleModel::create(['project_id' => $result->id, 'name' => 'issue_tracking']);
                        $saveProject['projects_ext']['project_id']        = $result->id;
                        $saveProject['projects_info']['project_id']       = $result->id;
                        $saveProject['projects_customized']['project_id'] = $result->id;
                        ProjectsExtModel::create($saveProject['projects_ext']);
                        ProjectsInfoModel::create($saveProject['projects_info']);
                        ProjectsCustomizedModel::create($saveProject['projects_customized']);
                        foreach ($saveProject['projects_attr'] as $saveAttr) {
                            ProjectsAttrModel::firstOrCreate(['project_id' => $result->id, 'attr_id' => $saveAttr['attr_id']], $saveAttr);
                        }
                        foreach ($saveProject['projects_desc'] as $saveDesc) {
                            ProjectsDescModel::firstOrCreate(['project_id' => $result->id, 'desc_id' => $saveDesc['desc_id']], $saveDesc);
                        }

                        if ($saveProject['members']) {
                            make(MemberService::class)->doEditByProjectUser($result->id, ['role_ids' => 4, 'user_id' => $saveProject['members'], 'inherited_from' => 0]);
                        }

                        if ($saveProject['watchers']) {
                            foreach ($saveProject['watchers'] as $watcher) {
                                ProjectsWatcherModel::firstOrCreate(['project_id' => $result->id, 'user_id' => $watcher], ['project_id' => $result->id, 'user_id' => $watcher]);
                            }
                        }
                        ProjectsTrackerModel::query()->create(['project_id' => (int)$result->id, 'tracker_id' => (int)$taskTracker]);

                        $progress = ProductProgressModel::query()->where('product_id', $product['id'])->get()->toArray();
                        foreach ($progress as $pro) {
                            $saveProgress   = [
                                'project_id'           => $result['id'],
                                'pid'                  => $pro['pid'],
                                'description'          => $pro['description'],
                                'create_user_id'       => $pro['create_user_id'],
                                'mail_user'            => $pro['mail_user'],
                                'workwx_user'          => $pro['workwx_user'],
                                'created_at'           => $pro['created_at'],
                                'type'                 => $pro['type'],
                                'username'             => $pro['username'],
                                'version_pre'          => $pro['version_pre'],
                                'version'              => $pro['version'],
                                'describe'             => $pro['describe'],
                                'attachment_reversion' => $pro['attachment_reversion'],
                                'created_at'           => $pro['created_at']
                            ];
                            $resultProgress = ProjectsProgressModel::create($saveProgress);
                            ProjectsProgressModel::query()->where('id', $resultProgress->id)->update(['created_at' => $pro['created_at']]);
                            $progressDetails = ProductProgressDetailModel::query()->where('progress_id', $pro['id'])->get()->toArray();
                            if ($resultProgress && $progressDetails) {
                                foreach ($progressDetails as $detail) {
                                    $saveDetail = [
                                        'progress_id' => $resultProgress->id,
                                        'property'    => $detail['property'],
                                        'prop_key'    => $detail['prop_key'],
                                        'old_value'   => $detail['old_value'],
                                        'value'       => $detail['value'],
                                        'created_at'  => $detail['created_at'],
                                    ];
                                    $proDetail  = ProjectsProgressDetailsModel::create($saveDetail);
                                    ProjectsProgressDetailsModel::query()->where('id', $proDetail->id)->update(['created_at' => $detail['created_at']]);
                                }
                            }
                        }
                    }
                } else {
                    // 原来已经存在该项目时处理
                    $projectId  = $project->id;
                    $projectExt = ProjectsExtModel::query()->where('project_id', $projectId)->first();
                    // 原来已经是产品类型的跳过操作
                    if ($projectExt && $projectExt->project_type == ProjectCode::PROJECT_TYPE_PROD) {
                        //软硬件负责人错误修复处理
                        $existInfo = ProjectsInfoModel::query()->where('project_id', $projectId)->first();
                        if ($existInfo) {
                            if (is_numeric($existInfo->hard_handler_uid) && !empty($product['hard_handler_id']) && is_array($product['hard_handler_id'])) {
                                foreach ($product['hard_handler_id'] as &$hard) {
                                    $hard = (int) $hard;
                                }
                                $existInfo->hard_handler_uid = $product['hard_handler_id'];

                            }
                            if (is_numeric($existInfo->soft_handler_uid) && !empty($product['soft_handler_id']) && is_array($product['soft_handler_id'])) {
                                foreach ($product['soft_handler_id'] as &$hard) {
                                    $hard = (int) $hard;
                                }
                                $existInfo->soft_handler_uid = $product['soft_handler_id'];
                            }
                            $existInfo->update();
                        }
                        return;
                    };

                    $productExt = ProductExtModel::query()->where('product_id', $product['id'])->first();

                    // 合并description
                    $description = $product['description'] ? (strlen($product['description']) > 65534 ? '' : $product['description']) : '';
                    $description = !empty($project['description']) ? $project['description'] . PHP_EOL . $description : $description;


                    $saveProject = [
                        'description' => $description,
                    ];

                    $saveProjectExt = [
                        'project_id'   => $projectId,
                        'project_type' => ProjectCode::PROJECT_TYPE_PROD,
                    ];

                    $saveMembers  = ProductMemberModel::query()->where('product_id', $product['id'])->pluck('user_id');
                    $saveMembers  = $saveMembers ? $saveMembers->toArray() : null;
                    $saveWatchers = ProductWatcherModel::query()->where('product_id', $product['id'])->pluck('user_id');
                    $saveWatchers = $saveWatchers ? $saveWatchers->toArray() : null;

                    $saveProjectInfo = [
                        'project_id'       => $projectId,
                        'category'         => !empty($product['category']) ? $product['category'] : 'opensource',
                        'platform'         => $product['platform'],
                        'product_status'   => $product['status'],
                        'product_type'     => $product['product_type'],
                        'product_progress' => $product['progress'],
                        'creator'          => $product['creator'],
                        'img'              => $product['img'],
                        'created_date'     => (empty($product['created_date']) || $product['created_date'] == '0000-00-00' || $product['created_date'] == '0000-00-00 00:00:00') ? date('Y-m-d') : date('Y-m-d', strtotime($product['created_date'])),
                    ];
                    // 负责人，json字段处理有数据才需要加入
                    if (!empty($product['hard_handler_id'])) {
                        $saveProjectInfo['hard_handler_uid'] = $product['hard_handler_id'];
                        foreach ($saveProjectInfo['hard_handler_uid'] as &$value) {
                            $value = (int) $value;
                        }
                        $saveMembers = array_merge($saveMembers, $product['hard_handler_id']);

                    }
                    if (!empty($product['soft_handler_id'])) {
                        $saveProjectInfo['soft_handler_uid'] = $product['soft_handler_id'];
                        foreach ($saveProjectInfo['soft_handler_uid'] as &$value) {
                            $value = (int) $value;
                        }
                        $saveMembers = array_merge($saveMembers, $product['soft_handler_id']);
                    }
                    if (!empty($product['closed_date'])) {
                        $saveProjectInfo['closed_date'] = $product['closed_date'];
                    }

                    $projectsCustomized = [
                        'project_id'         => $projectId,
                        'urgency'            => $productExt->urgency ?? 0,
                        'yewu_handler_id'    => $product['charge_uid'] ?? 0,
                        'shipment_predicted' => $productExt->shipment_predicted ?? 0,
                        'amount'             => $productExt->amount ?? 0,
                        'curr'               => $productExt->curr ?? '人民币',
                        'cost'               => $productExt->cost ?? 0,
                        'project_industry'   => $productExt->project_industry ?? '',
                        'company_website'    => $productExt->company_website ?? '',
                        'company_background' => $productExt->company_background ?? '',
                        'sale_client_id'     => $productExt->sale_client_id ?? 0,
                    ];

                    $saveProjectsAttr = [];
                    $saveProjectsDesc = [];
                    $productAttr      = ProductAttrModel::query()->where('product_id', $product['id'])->get();
                    foreach ($productAttr as $attr) {
                        $saveProjectsAttr[] = [
                            'attr_id'       => $attr->attr_id,
                            'attr_value_id' => $attr->attr_value_id,
                        ];
                    }

                    $productDesc = ProductDescValueModel::query()->where('product_id', $product['id'])->get();
                    foreach ($productDesc as $desc) {
                        if (!isset($saveProjectsDesc[$desc->desc_id])) {
                            $saveProjectsDesc[$desc->desc_id] = [
                                'desc_id' => $desc->desc_id,
                                'value'   => $desc->value,
                                'url'     => $desc->url,
                            ];
                        }
                    }
                    $saveProjectsDesc = array_values($saveProjectsDesc);


                    // 更新主项目主要是说明合并
                    ProjectModel::query()->where('id', $projectId)->update($saveProject);

                    // 创建或者更新ext数据
                    ProjectsExtModel::updateOrCreate(['project_id' => $projectId], $saveProjectExt);

                    ProjectsInfoModel::create($saveProjectInfo);
                    ProjectsCustomizedModel::create($projectsCustomized);
                    foreach ($saveProjectsAttr as $saveAttr) {
                        ProjectsAttrModel::firstOrCreate(['project_id' => $projectId, 'attr_id' => $saveAttr['attr_id']], $saveAttr);
                    }
                    foreach ($saveProjectsDesc as $saveDesc) {
                        ProjectsDescModel::firstOrCreate(['project_id' => $projectId, 'desc_id' => $saveDesc['desc_id']], $saveDesc);
                    }

                    if ($saveMembers) {
                        $saveMembers = array_unique($saveMembers);
                        make(MemberService::class)->doEditByProjectUser($projectId, ['role_ids' => 4, 'user_id' => $saveMembers, 'inherited_from' => 0]);
                    }

                    if ($saveWatchers) {
                        foreach ($saveWatchers as $watcher) {
                            ProjectsWatcherModel::firstOrCreate(['project_id' => $projectId, 'user_id' => $watcher], ['project_id' => $projectId, 'user_id' => $watcher]);
                        }
                    }

                    // 加入可建重要事项
                    ProjectsTrackerModel::query()->create(['project_id' => (int)$projectId, 'tracker_id' => (int)$taskTracker]);

                    $progress = ProductProgressModel::query()->where('product_id', $product['id'])->get()->toArray();
                    foreach ($progress as $pro) {
                        $saveProgress   = [
                            'project_id'           => $projectId,
                            'pid'                  => $pro['pid'],
                            'description'          => $pro['description'],
                            'create_user_id'       => $pro['create_user_id'],
                            'mail_user'            => $pro['mail_user'],
                            'workwx_user'          => $pro['workwx_user'],
                            'created_at'           => $pro['created_at'],
                            'type'                 => $pro['type'],
                            'username'             => $pro['username'],
                            'version_pre'          => $pro['version_pre'],
                            'version'              => $pro['version'],
                            'describe'             => $pro['describe'],
                            'attachment_reversion' => $pro['attachment_reversion'],
                            'created_at'           => $pro['created_at']
                        ];
                        $resultProgress = ProjectsProgressModel::create($saveProgress);
                        ProjectsProgressModel::query()->where('id', $resultProgress->id)->update(['created_at' => $pro['created_at']]);
                        $progressDetails = ProductProgressDetailModel::query()->where('progress_id', $pro['id'])->get()->toArray();
                        if ($resultProgress && $progressDetails) {
                            foreach ($progressDetails as $detail) {
                                $saveDetail = [
                                    'progress_id' => $resultProgress->id,
                                    'property'    => $detail['property'],
                                    'prop_key'    => $detail['prop_key'],
                                    'old_value'   => $detail['old_value'],
                                    'value'       => $detail['value'],
                                    'created_at'  => $detail['created_at'],
                                ];
                                $proDetail  = ProjectsProgressDetailsModel::create($saveDetail);
                                ProjectsProgressDetailsModel::query()->where('id', $proDetail->id)->update(['created_at' => $detail['created_at']]);
                            }
                        }
                    }

                }
            });
        }
        return true;
    }

    /**
     * 迁移产品成员到项目
     * @return void
     */
    public function migrateMembersToProject()
    {
        $products = $this->model::query()->orderBy('id', 'asc')->get()->toArray();
        $projectModel = make(ProjectModel::class);
        $productMembersModel = make(ProductMemberModel::class);
        foreach ($products as $product) {
            $project = $projectModel::query()->where('name', $product['name'])->first();
            if ($project) {
                $productMembers = $productMembersModel::query()->where('product_id', $product['id'])->pluck('user_id')->toArray();
                if ($productMembers) {
                    make(MemberService::class)->doEditByProjectUser($project->id, ['role_ids' => 4, 'user_id' => $productMembers, 'inherited_from' => 0]);
                }
            }
        }
        return true;
    }

    /**
     * 获取定制产品销量
     * @return void
     */
    public function getCustomizedSaleCount(int $productId, int $clientId)
    {
        // 量产数量（已销售数量）
        return $productId > 0 && $clientId > 0 ? ClientSaleKyptTableModel::query(true)
            ->where('prod_id', $productId)
            ->where('client_id', $clientId)
            ->where('delete_time', 0)
            ->sum('num') : 0;
    }

    public function getNodesIssues($nodes)
    {
        $flowIssueIds = array_column(array_column($nodes, 'properties'), 'issue_id');
        $issues = IssueModel::query()->with(['issueStatus', 'customFields', 'issueAssigned'])
            ->with('childIssue:id,subject,parent_id')
            ->whereIn('id', $flowIssueIds)->get()->toArray();
        $issues = array_column($issues, null, 'id');

        // 遍历 $issues 数组，添加 assigned 字段
        foreach ($issues as &$issue) {
            if (!empty($issue['issue_assigned'])) {
                // 提取 issue_assigned 数组中的 id 值
                $issue['assigned'] = array_column($issue['issue_assigned'], 'user_id');
            } else {
                // 若 issue_assigned 为空，则 assigned 也为空数组
                $issue['assigned'] = [];
            }
        }

        foreach ($nodes as &$node) {
            if (!empty($issues[$node['properties']['issue_id']])) {
                $node['properties']['issue'] = $issues[$node['properties']['issue_id']];
                $node['properties']['issue']['name'] = $node['properties']['issue']['subject'];
            }
        }
        return $nodes;
    }

    /**
     * 校验关联的流程任务是否皆已完成 20250315 考虑到关联事项关系暂无区分主从关系，检测逻辑待定
     */
    public function checkFlowTaskIsFinished($ids)
    {
        // 确保 $ids 是数组
        $ids = is_array($ids) ? $ids : explode(',', $ids);

        // 查询 issue 关系数据
        $issueRelations = IssueRelationModel::query()
            ->whereIn('issue_from_id', $ids)
            ->orWhereIn('issue_to_id', $ids)
            ->get()
            ->toArray();

        // 提取 issue_from_id 和 issue_to_id
        $allIssueIds = [];
        foreach ($issueRelations as $relation) {
            $allIssueIds[] = $relation['issue_from_id'];
            $allIssueIds[] = $relation['issue_to_id'];
        }

        // 去重并去除 $ids 中已存在的值
        $uniqueIssueIds = array_values(array_diff(array_unique($allIssueIds), $ids));

        if (!$uniqueIssueIds || count($uniqueIssueIds) == 0) {
            return [];
        }

        // 查询所有 issue 的状态
        $allIssueInfo = make(IssueModel::class)::query()
            ->select('id', 'status_id', 'project_id', 'tracker_id', 'subject')
            ->whereIn('id', $uniqueIssueIds)
            ->get()
            ->toArray();

        $productId = $allIssueInfo[0]['project_id'] ?? 0;
        $trackerId = $allIssueInfo[0]['tracker_id'] ?? 0;
        $issueStatus = make(IssueService::class)->getNewIssueStatus(null, $productId, $trackerId);

        // 获取 "已完成" 状态的 ID
        $finishedStatusIds = array_column(array_filter($issueStatus, function ($status) {
            return $status['name'] === '已完成';
        }), 'id');

        // 过滤掉 "已完成" 状态的 issue
        $unfinishedIssues = array_filter($allIssueInfo, function ($issue) use ($finishedStatusIds) {
            return !in_array($issue['status_id'], $finishedStatusIds);
        });

        return $unfinishedIssues;
    }

    /**
     * 校验子流程任务是否皆已完成
     */
    public function checkFlowChildTaskIsFinished($ids)
    {
        // 确保 $ids 是数组
        $ids = is_array($ids) ? $ids : explode(',', $ids);


        // 查询 issue 关系数据
        $childIssues = IssueModel::query()
            ->whereIn('parent_id', $ids)
            ->get()
            ->toArray();

        // 提取 issue_from_id 和 issue_to_id
        $allIssueIds = [];
        foreach ($childIssues as $childIssue) {
            $allIssueIds[] = $childIssue['id'];
        }

        // 去重并去除 $ids 中已存在的值
        $uniqueIssueIds = array_values(array_diff(array_unique($allIssueIds), $ids));

        if (!$uniqueIssueIds || count($uniqueIssueIds) == 0) {
            return [];
        }

        // 查询所有 issue 的状态
        $allIssueInfo = make(IssueModel::class)::query()
            ->select('id', 'status_id', 'project_id', 'tracker_id', 'subject')
            ->whereIn('id', $uniqueIssueIds)
            ->get()
            ->toArray();

        $productId = $allIssueInfo[0]['project_id'] ?? 0;
        $trackerId = $allIssueInfo[0]['tracker_id'] ?? 0;
        $issueStatus = make(IssueService::class)->getNewIssueStatus(null, $productId, $trackerId);

        // 获取 "已完成" 状态的 ID
        $finishedStatusIds = array_column(array_filter($issueStatus, function ($status) {
            return $status['name'] === '已完成';
        }), 'id');

        // 过滤掉 "已完成" 状态的 issue
        $unfinishedIssues = array_filter($allIssueInfo, function ($issue) use ($finishedStatusIds) {
            return !in_array($issue['status_id'], $finishedStatusIds);
        });

        return $unfinishedIssues;
    }

    /**
     * 同步产品研发流程
     * @return array
     */
    // public function syncProductFlow()
    // {
    //     $defaultFlow = FlowTemplateModel::where('flow_type', 'project')->where('is_default', 1)->first();
    //     if ($defaultFlow) {
    //         $nodesTpl = $defaultFlow['template']['nodes'] ?? [];
    //         $flows = FlowModel::query()->get();
    //         $flows = $flows ? $flows->toArray() : [];
    //         foreach ($flows as $flow) {
    //             if (!empty($flow['nodes']) && count($flow['nodes']) > 0) {
    //                 // 获取当前流程的所有节点的名称数组
    //                 $flowNodesName = array_column(array_column($flow['nodes'], 'text'), 'value');
    //                 foreach ($nodesTpl as $tpl) {
    //                     // 如果当前流程没有该模板节点
    //                     if (!in_array($tpl['text']['value'], $flowNodesName)) {
    //                         // $f
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    //获取产品选项
    public function getProductOption(array $filter = [], array $op = [], string $sort = 'id', string $order = 'desc', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit,ProjectModel::query());
        $query = $query->leftJoin('projects_ext', 'projects_ext.project_id', '=', 'projects.id')
        ->leftJoin('projects_info', 'projects_info.project_id', '=', 'projects.id');
        if(empty($filter['project_type'])){
            $query = $query->where('project_type','=',ProjectCode::PROJECT_TYPE_PROD);
        }
        $list = $query->with('productParent')->orderBy($sort, $order)->limit($limit)->groupBy('projects.id')->select(
            [
                'projects.name',
                'projects.id',
            ])->get();
        $list = $list ? $list->toArray() : [];
        foreach ($list as &$item){
            $item['parent_product_ids'] = array_column($item['product_parent']?:[],'parent_id');
        }
        return $list;
    }
}