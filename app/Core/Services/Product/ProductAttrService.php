<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Product;

use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Redmine\ProductAttrKeyModel;
use App\Model\Redmine\ProductAttrValueModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductAttrService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var ProductAttrKeyModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $paginate = $query->with(['valueIds'])->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        $valueModel = make(ProductAttrValueModel::class);
        if (!empty($paginate['data'])) {
            foreach ($paginate['data'] as &$datum) {
                $datum['values'] = $valueModel::query()->whereIn('id', $datum['value_ids'])->get()->toArray();
            }
        }
        return $paginate;
    }
}