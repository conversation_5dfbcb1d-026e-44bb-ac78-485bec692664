<?php

declare(strict_types=1);

namespace App\Core\Utils;

use App\Model\Points\PointConfigModel;

/**
 * 积分类型管理工具类
 * 统一管理 point_type 和 action_type 的可用范围
 */
class PointTypeManager
{
    /**
     * 获取所有积分类型配置
     */
    public static function getAllPointTypes(): array
    {
        $pointConfigs = PointConfigModel::query()->get()->keyBy('action_type')->toArray();
        return $pointConfigs;
    }

    /**
     * 获取指定积分类型的配置
     */
    public static function getPointTypeConfig(string $pointType): ?array
    {
        $allTypes = self::getAllPointTypes();
        return $allTypes[$pointType] ?? null;
    }

    /**
     * 获取积分类型的名称
     */
    public static function getPointTypeName(string $pointType): string
    {
        $config = self::getPointTypeConfig($pointType);
        return $config['name'] ?? $pointType;
    }

    /**
     * 获取积分类型的描述
     */
    public static function getPointTypeDescription(string $pointType): string
    {
        $config = self::getPointTypeConfig($pointType);
        return $config['description'] ?? '';
    }

    /**
     * 获取积分类型的默认积分值
     */
    public static function getPointTypePoints(string $pointType): int
    {
        $config = self::getPointTypeConfig($pointType);
        return $config['points'] ?? 0;
    }


    /**
     * 获取积分类型的分类
     */
    public static function getPointTypeCategory(string $pointType): string
    {
        $config = self::getPointTypeConfig($pointType);
        return $config['category'] ?? 'unknown';
    }

    /**
     * 按分类获取积分类型
     */
    public static function getPointTypesByCategory(string $category): array
    {
        $allTypes = self::getAllPointTypes();
        $result = [];
        
        foreach ($allTypes as $type => $config) {
            if (($config['category'] ?? '') === $category) {
                $result[$type] = $config;
            }
        }
        
        return $result;
    }

    /**
     * 获取所有分类配置
     */
    public static function getAllCategories(): array
    {
        return config('points.categories', []);
    }

    /**
     * 获取分类配置
     */
    public static function getCategoryConfig(string $category): ?array
    {
        $allCategories = self::getAllCategories();
        return $allCategories[$category] ?? null;
    }

    /**
     * 获取分类名称
     */
    public static function getCategoryName(string $category): string
    {
        $config = self::getCategoryConfig($category);
        return $config['name'] ?? $category;
    }

    /**
     * 获取分类颜色
     */
    public static function getCategoryColor(string $category): string
    {
        $config = self::getCategoryConfig($category);
        return $config['color'] ?? '#909399';
    }

    /**
     * 验证积分类型是否存在
     */
    public static function isValidPointType(string $pointType): bool
    {
        return self::getPointTypeConfig($pointType) !== null;
    }

    /**
     * 获取可用的积分类型列表（用于下拉选择等）
     */
    public static function getPointTypeOptions(): array
    {
        $allTypes = self::getAllPointTypes();
        $options = [];
        
        foreach ($allTypes as $type => $config) {
            $options[] = [
                'value' => $type,
                'label' => $config['name'] ?? $type,
                'description' => $config['description'] ?? '',
                'category' => $config['category'] ?? 'unknown',
                'points' => $config['points'] ?? 0,
            ];
        }
        
        return $options;
    }

    /**
     * 按分类获取积分类型选项
     */
    public static function getPointTypeOptionsByCategory(): array
    {
        $allTypes = self::getAllPointTypes();
        $allCategories = self::getAllCategories();
        $result = [];
        
        foreach ($allCategories as $categoryKey => $categoryConfig) {
            $result[$categoryKey] = [
                'category' => $categoryConfig,
                'types' => []
            ];
        }
        
        foreach ($allTypes as $type => $config) {
            $category = $config['category'] ?? 'unknown';
            if (!isset($result[$category])) {
                $result[$category] = [
                    'category' => [
                        'name' => $category,
                        'description' => '',
                        'color' => '#909399'
                    ],
                    'types' => []
                ];
            }
            
            $result[$category]['types'][] = [
                'value' => $type,
                'label' => $config['name'] ?? $type,
                'description' => $config['description'] ?? '',
                'points' => $config['points'] ?? 0,
            ];
        }
        
        return array_filter($result, function($item) {
            return !empty($item['types']);
        });
    }

    /**
     * 获取Wiki相关的积分类型
     */
    public static function getWikiPointTypes(): array
    {
        return self::getPointTypesByCategory('wiki');
    }

    /**
     * 获取系统相关的积分类型
     */
    public static function getSystemPointTypes(): array
    {
        return self::getPointTypesByCategory('system');
    }

    /**
     * 获取管理员相关的积分类型
     */
    public static function getAdminPointTypes(): array
    {
        return self::getPointTypesByCategory('admin');
    }
}