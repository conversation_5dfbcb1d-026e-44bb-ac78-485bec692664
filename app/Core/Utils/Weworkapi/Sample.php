<?php

// // 情况一,两个审批人会签情况
// // 1.第一个审批人同意后加密数据示例
// {
//     "ToUserName": "ww761dbc2ad3b8becd",
//     "Encrypt": "NCLzEbigBFYrTJzDBGZsWpNF/lTqga2e/xvllN5S8A7+OnFoWOoQMukrmIMVqvpwgRVqNBVuuPQ4xZWUAypqEOrmXLO+3FJdFLaBV7XLBDyqQIHdZ2zrRAIovwUSWaPknsqFZJWpbQrZulO200hms0+VYIsQZ9rNP80GuxrGd7F7WvwB5jvC+2Pne3qY/z1BcWCl/cN7izs2mC+tsGrdY8rZo9z0nUKZxlkILXvrByrYA9+ZRyTlvuUSFQ4pUmihfxQMt7aJfteNjXFoLRN+nzHGUqOUs4il5VhO4blWSWj06qmMbtGRXmOM3BPKY0cscSndaNZ8YmYm7ce9u6EL9lB78kmWxIUxwkVzFDoVdTP6UEWH4Cn/8ELd4hra/cy6wF/uHyPAOwoei1Mm+Wy57oEZTRDFNTOYr4QxdjKhiYHldH59X1SdanYgfMS/JZj9Y9kWcrnMD0iVgWbSd6EJZI8+zT6/Ld0bKAXpLZRr2I8lejkTp0fgSN23tpzF6epYcNv9Bco6wnf4HwJvTz5hoiKNJy32kR6Vqwwo02vN12ewxr0ikPz8dnXwjuYLsyzk1BgxtRTC9vJzjbuMz8jYW3SQ+/dhNs4/Gerwy9mXg2sxE7hLVhmJ5s+wEl6MA3OlJGA3p60SFGLldYt086H6iJ8VzLUudOm25MkgvczIg99KGESgkXOAdofo5CQP7H6tSDiRxpibA5SM0y3+HE3gMM7yKBupoHXRT0F8PBkG4OM3ZQlOoaVNYFsCgOiWKgWdJuMnAqN6Rg61bNkuvibTepj5OugEEAXvke5EIRw/W3e/oZjeQlgr0vQTIsBWAGvMEJDrkCP8PrYm29BJBIF3TWljrcjhJQPYq/5JFPJJGpcmfBMJL0r3dI7yF2p8P2EWdiX/1Va1UPc/JrwP4HnSgSWng57y//+gZOC9RXCqEFUGR8IfO3hZFSPYhriJVRCe+R4DHALbC9ULrZAdsi/4AO9+zxz40YkB9hdDVS5wOyiPchlHMCT48AL8ViaDduV6edgHn+ETFV7GtoPX48rztwRUgn2IRGr5KWSp6OlXvnmwt/f6LhdxSp6J+jbR8cYi5hTH7cvuBYEwEU9JJQyfVomQqHClM0qfuJBNhYCc65BzQ29JLRjBUJYAd0KEOMfskE/u27VddOwRfcVXmZ3m2pKBGWLF8zUzTN5ZwJVhUUI6Jw4z0EIE5lH6s7dEMWgL8lGzY0IIK5lB6g228tCyR2dZDrGYNb6IxKUyRRP3cFj6N9GkLmFhH66EB2++h19gS5kswpIYbwFMfC99/z7zYm83QDcJExS7bZEsxk/O91tRwjxN15/L7j1+BuGTysPEgJiaYWMry229Btwy5INdmqlerJAjiJRFSRo7/1ZNRzPaE/2sryfmjCiwS4DsaWWP8BOdAvCfXT9qmsK9W/zJqVH3njgQZm8xJ3WfCnZnX0pHGDFqbh3+yoDEwgSnpqdJkkneXZrnw8lfP27JbReuop8YM4caQ2sVQJSDIuRtC3yNtbMGZBQ8NDBQtGes7vUg",
//     "AgentID": "3010040",
//     "msg_signature": "c8f3c0724c02d5c00757b13b669cbd10b236db4c",
//     "timestamp": "1683708023",
//     "nonce": "1682967248"
// }
//
//
// // 2.1:第二个审批人同意后加密数据示例
// {
//     "ToUserName": "ww761dbc2ad3b8becd",
//     "Encrypt": "XMKh8LdpfoTw8WysPHXqxbVv5cDs2umA3lVM6ij0LM/fHsZbGQPcIHG684Gsu7vG/3mW779z62A7cVGoYNhqPfPlBtuEDrH+pU+vNjwNzrZ4U9J+YWW5+HrTEs3z88NEecfxFlLrvYQW8O4w0yKbdchqxfAm/FOVroqvoAF4zkvJ5pNnEzic3YNKl/fd+PhL8pZa83kKdjm94B2b7isOxC/NXUPew3KrQNFxzbCOfj/CRqo/Zk1g34yh6DkwnKLl1XDUPbV5gJvTjS69zrmMpLQCH1FvyfglNpGEETc+ACGK/WM3+6uOMr7sh/G+mxstBtvq0sgHtNydfpiiTRX4otvjJlc6UeIWNjQlpMnJqpQrf3sXlf935GN4SYh3zUeL2g+bRMxMYkWu5tzP/NRZXgqBpsb5QyJa3WoQ8cC3c49X4x/X356gAgZcXISc8jxoMMJTQI29ZskrFqP0d2XFnLttUjRkSfKAJNLmFTiNXyNHP1NH3TUMWJwEpZFJt+m37U9rzCKF0lEwIEZwYEUkj9gYFu9fnFXkBmH6rnHz+V0D9+QJHd8lqSm4I00rcKj8tL7YpJf6ONs3LspCtryhhxzFxEbX4mO6yMv+pxD/XPo0XexXGaqpYkfuMk+edPrnoAXc4F6yHL+dIXfb8vN9wKeXugppl6N7Uo2X09BLp4OCBEG0mP9YoJIPOXAmkt5tZGNiARLL+xx8vgXWYpqxLnrqJ2lcQHYvGqxAW+3l6bb3fhmEH2aMlSETtc73Wh6klT8WFpC91WQ/qYfV+b72JwNMH8IcaHhqvU1vuxqVCLPlDJzHY/Qs8vaYx2ZfS0ANBhvM4uXQ5XDX4lY3ZYIW61hAobPdKK0kC3MX/Bs/xDEBcsnYPC+1SM1+PCtUnEaB2BPK9AxMKb/slLJSSyvkalQQ/iZTRLE1+P2bTgbgS7Onwv64RwycIvcmchCPMwOaWBfV8iom9vaBUW0N3oY7O660PVlt7Q1ajNJrRMUCQIwZCgMoNe+nKi6hGFuFihFNLLBatJjwPEF/oRydu+r/h33W9DQFMzWGauNrDNwvjkrcHb9tprQ4uf19cSVltUAyBkq0CZEkYDOAgPhEPrx9pd+ObiVZHp5HISq2zT7og8nsOMuFtLEzaoqAgQ2yPUfprCHrdeBtDe/0gmo90wkDc/1mz4yjWy5vSwQFGu8gapyVrcjr8FB9Y+vx9jZyoxJfZF0oa0ftcFl5mjhOGLDfXCKmF2QUUj0+nDdn1jXYccU43k3iR+1FV2tjybETTq2oyHnvQB4oLlGT4ZiwxFFY2kdiOa7vP3Gzn0/+ret4EP2XkUoRswx33p73F9tsphDLEsLT+I7LV/83/iZTspnlieoawGou4gmKAiodM37q3mWxzqYgjJzjahUHPvvsflawuC9k/3vZm+tYY3oZa8AX4Ky/t8LNYIhUaw2z++Cd5IeeWOUMABXSDh0xroPMkHInIh9SBcFFgoI+Fo59QCSxr6gxu+n8c8uFXO6yEZkcM0GXbJaEHFg3ayN3I164pUYk",
//     "AgentID": "3010040",
//     "msg_signature": "13e9e51d78230007c62b726250d52876071cee67",
//     "timestamp": "1683719763",
//     "nonce": "1683380288"
// }
//
// // 2.2:第二个审批人同意后解密数据
// {"ToUserName":"ww761dbc2ad3b8becd","FromUserName":"sys","CreateTime":"1683719763","MsgType":"event","Event":"sys_approval_change","AgentID":"3010040","ApprovalInfo":{"SpNo":"202305090001","SpName":"产品借用","SpStatus":"2","TemplateId":"3WLJWdGZsCpZpkxkJYbF5ADRPsXWU71fSQBjVm7m","ApplyTime":"1683613880","Applyer":{"UserId":"XiaoJiaJie","Party":"20"},"SpRecord":{"SpStatus":"2","ApproverAttr":"2","Details":[{"Approver":{"UserId":"foyLin"},"Speech":[],"SpStatus":"2","SpTime":"1683719763"},{"Approver":{"UserId":"HuangZhiQiang"},"Speech":[],"SpStatus":"2","SpTime":"1683708023"}]},"Notifyer":[{"UserId":"foyLin"},{"UserId":"HuangZhiQiang"}],"StatuChangeEvent":"2"}}

include_once "WXBizMsgCrypt.php";

// 假设企业号在公众平台上设置的参数如下
$encodingAesKey = "jWmYm7qr5nMoAUwZRjGtBxmz3KA1tkAj3ykkR6q2B2C";
$token = "QDG6eK";
$corpId = "wx5823bf96d3bd56c7";

/*
------------使用示例一：验证回调URL---------------
*企业开启回调模式时，企业号会向验证url发送一个get请求
假设点击验证时，企业收到类似请求：
* GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
* HTTP/1.1 Host: qy.weixin.qq.com
接收到该请求时，企业应
1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及公众平台推送过来的随机加密字符串(echostr),
这一步注意作URL解码。
2.验证消息体签名的正确性
3. 解密出echostr原文，将原文当作Get请求的response，返回给公众平台
第2，3步可以用公众平台提供的库函数VerifyURL来实现。
*/

// $sVerifyMsgSig = HttpUtils.ParseUrl("msg_signature");
$sVerifyMsgSig = "5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3";
// $sVerifyTimeStamp = HttpUtils.ParseUrl("timestamp");
$sVerifyTimeStamp = "1409659589";
// $sVerifyNonce = HttpUtils.ParseUrl("nonce");
$sVerifyNonce = "263014780";
// $sVerifyEchoStr = HttpUtils.ParseUrl("echostr");
$sVerifyEchoStr = "P9nAzCzyDtyTWESHep1vC5X9xho/qYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp+4RPcs8TgAE7OaBO+FZXvnaqQ==";

// 需要返回的明文
$sEchoStr = "";

$wxcpt = new WXBizMsgCrypt($token, $encodingAesKey, $corpId);
$errCode = $wxcpt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
if ($errCode == 0) {
    //var_dump($sEchoStr);
    //
    // 验证URL成功，将sEchoStr返回
    // HttpUtils.SetResponce($sEchoStr);
} else {
    print("ERR: " . $errCode . "\n\n");
}

/*
------------使用示例二：对用户回复的消息解密---------------
用户回复消息或者点击事件响应时，企业会收到回调消息，此消息是经过公众平台加密之后的密文以post形式发送给企业，密文格式请参考官方文档
假设企业收到公众平台的回调消息如下：
POST /cgi-bin/wxpush? msg_signature=477715d11cdb4164915debcba66cb864d751f3e6&timestamp=1409659813&nonce=1372623149 HTTP/1.1
Host: qy.weixin.qq.com
Content-Length: 613
<xml>
<ToUserName><![CDATA[wx5823bf96d3bd56c7]]></ToUserName><Encrypt><![CDATA[RypEvHKD8QQKFhvQ6QleEB4J58tiPdvo+rtK1I9qca6aM/wvqnLSV5zEPeusUiX5L5X/0lWfrf0QADHHhGd3QczcdCUpj911L3vg3W/sYYvuJTs3TUUkSUXxaccAS0qhxchrRYt66wiSpGLYL42aM6A8dTT+6k4aSknmPj48kzJs8qLjvd4Xgpue06DOdnLxAUHzM6+kDZ+HMZfJYuR+LtwGc2hgf5gsijff0ekUNXZiqATP7PF5mZxZ3Izoun1s4zG4LUMnvw2r+KqCKIw+3IQH03v+BCA9nMELNqbSf6tiWSrXJB3LAVGUcallcrw8V2t9EL4EhzJWrQUax5wLVMNS0+rUPA3k22Ncx4XXZS9o0MBH27Bo6BpNelZpS+/uh9KsNlY6bHCmJU9p8g7m3fVKn28H3KDYA5Pl/T8Z1ptDAVe0lXdQ2YoyyH2uyPIGHBZZIs2pDBS8R07+qN+E7Q==]]></Encrypt>
<AgentID><![CDATA[218]]></AgentID>
</xml>
企业收到post请求之后应该
1.解析出url上的参数，包括消息体签名(msg_signature)，时间戳(timestamp)以及随机数字串(nonce)
2.验证消息体签名的正确性。
3.将post请求的数据进行xml解析，并将<Encrypt>标签的内容进行解密，解密出来的明文即是用户回复消息的明文，明文格式请参考官方文档
第2，3步可以用公众平台提供的库函数DecryptMsg来实现。
*/

// $sReqMsgSig = HttpUtils.ParseUrl("msg_signature");
$sReqMsgSig = "477715d11cdb4164915debcba66cb864d751f3e6";
// $sReqTimeStamp = HttpUtils.ParseUrl("timestamp");
$sReqTimeStamp = "1409659813";
// $sReqNonce = HttpUtils.ParseUrl("nonce");
$sReqNonce = "1372623149";
// post请求的密文数据
// $sReqData = HttpUtils.PostData();
$sReqData = "<xml><ToUserName><![CDATA[wx5823bf96d3bd56c7]]></ToUserName><Encrypt><![CDATA[RypEvHKD8QQKFhvQ6QleEB4J58tiPdvo+rtK1I9qca6aM/wvqnLSV5zEPeusUiX5L5X/0lWfrf0QADHHhGd3QczcdCUpj911L3vg3W/sYYvuJTs3TUUkSUXxaccAS0qhxchrRYt66wiSpGLYL42aM6A8dTT+6k4aSknmPj48kzJs8qLjvd4Xgpue06DOdnLxAUHzM6+kDZ+HMZfJYuR+LtwGc2hgf5gsijff0ekUNXZiqATP7PF5mZxZ3Izoun1s4zG4LUMnvw2r+KqCKIw+3IQH03v+BCA9nMELNqbSf6tiWSrXJB3LAVGUcallcrw8V2t9EL4EhzJWrQUax5wLVMNS0+rUPA3k22Ncx4XXZS9o0MBH27Bo6BpNelZpS+/uh9KsNlY6bHCmJU9p8g7m3fVKn28H3KDYA5Pl/T8Z1ptDAVe0lXdQ2YoyyH2uyPIGHBZZIs2pDBS8R07+qN+E7Q==]]></Encrypt><AgentID><![CDATA[218]]></AgentID></xml>";
$sMsg = "";  // 解析之后的明文
$errCode = $wxcpt->DecryptMsg($sReqMsgSig, $sReqTimeStamp, $sReqNonce, $sReqData, $sMsg);
if ($errCode == 0) {
    // 解密成功，sMsg即为xml格式的明文
    //var_dump($sMsg);
    // TODO: 对明文的处理
    /*
    "<xml><ToUserName><![CDATA[wx5823bf96d3bd56c7]]></ToUserName>
<FromUserName><![CDATA[mycreate]]></FromUserName>
<CreateTime>1409659813</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[hello]]></Content>
<MsgId>4561255354251345929</MsgId>
<AgentID>218</AgentID>
</xml>"
*/
} else {
    print("ERR: " . $errCode . "\n\n");
    //exit(-1);
}

/*
------------使用示例三：企业回复用户消息的加密---------------
企业被动回复用户的消息也需要进行加密，并且拼接成密文格式的xml串。
假设企业需要回复用户的明文如下：
<xml>
<ToUserName><![CDATA[mycreate]]></ToUserName>
<FromUserName><![CDATA[wx5823bf96d3bd56c7]]></FromUserName>
<CreateTime>1348831860</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[this is a test]]></Content>
<MsgId>1234567890123456</MsgId>
<AgentID>128</AgentID>
</xml>
为了将此段明文回复给用户，企业应：
1.自己生成时间时间戳(timestamp),随机数字串(nonce)以便生成消息体签名，也可以直接用从公众平台的post url上解析出的对应值。
2.将明文加密得到密文。
3.用密文，步骤1生成的timestamp,nonce和企业在公众平台设定的token生成消息体签名。
4.将密文，消息体签名，时间戳，随机数字串拼接成xml格式的字符串，发送给企业号。
以上2，3，4步可以用公众平台提供的库函数EncryptMsg来实现。
*/

// 需要发送的明文
$sRespData = "<xml><ToUserName><![CDATA[mycreate]]></ToUserName><FromUserName><![CDATA[wx5823bf96d3bd56c7]]></FromUserName><CreateTime>1348831860</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[this is a test]]></Content><MsgId>1234567890123456</MsgId><AgentID>128</AgentID></xml>";
$sEncryptMsg = ""; //xml格式的密文
$errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
if ($errCode == 0) {
    //var_dump($sEncryptMsg);
    print("done \n");
    // TODO:
    // 加密成功，企业需要将加密之后的sEncryptMsg返回
    // HttpUtils.SetResponce($sEncryptMsg);  //回复加密之后的密文
} else {
    print("ERR: " . $errCode . "\n\n");
    // exit(-1);
}