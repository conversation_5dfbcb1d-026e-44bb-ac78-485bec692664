<?php

namespace App\Core\Utils\Uploads\Drivers;

use App\Constants\StatusCode;
use App\Exception\AppException;
use OSS\OssClient;
use OSS\Credentials\EnvironmentVariableCredentialsProvider;

class <PERSON>yun extends DriversBase implements UploadInterface
{
    private $config;
    private $endpoint;
    private $bucket;
    private $client;

    public function __construct()
    {
        $this->endpoint = env('OSS_ENDPOINT');
        $this->bucket   = env('OSS_BUCKET');
        $provider       = new EnvironmentVariableCredentialsProvider();
        $this->config   = [
            "provider"         => $provider,
            "endpoint"         => $this->endpoint,
            "signatureVersion" => OssClient::OSS_SIGNATURE_VERSION_V4,
            "region"           => "cn-hangzhou"
        ];
        $this->client = new OssClient($this->config);
    }

    /**
     * @param $fileName 文件名称
     * @param $filePath 文件本地绝对路径
     * @param $remotePath 保存在OSS的目录(需要包含文件名称)
     * @return array|null
     * @throws \OSS\Http\RequestCore_Exception
     */
    public function upload($filePath, $remotePath = 'bi/uploads', $options = []): array
    {
        var_dump('来了');
        try {
            return $this->client->uploadFile($this->bucket, $remotePath, $filePath, $options);
        } catch (\OSS\Core\OssException $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
    

    /**
     * 判断文件是否存在
     * @param $remotePath 文件的OSS路径(含文件名称):bi/uploads/20250722/image-15113869535.png
     * @return bool
     */
    public function isExist($remotePath): bool
    {
        try{
            return $this->client->doesObjectExist($this->bucket, $remotePath);
        } catch(\OSS\Core\OssException $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}