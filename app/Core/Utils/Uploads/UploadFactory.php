<?php

namespace App\Core\Utils\Uploads;

use App\Core\Utils\Uploads\Drivers\UploadInterface;
use App\Exception\AppException;
use App\Constants\StatusCode;

class UploadFactory
{
    private const DRIVER_NAMESPACE = '\App\Core\Utils\Uploads\Drivers\\';

    public static function create(string $service): UploadInterface {
        $class = self::DRIVER_NAMESPACE . $service;
        if (!class_exists($class)) {
            throw new AppException(StatusCode::ERR_SERVER, "Unsupported service: {$service}");
        }
        return make($class);
    }
}