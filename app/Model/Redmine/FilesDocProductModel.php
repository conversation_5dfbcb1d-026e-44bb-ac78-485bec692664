<?php

declare (strict_types=1);
namespace App\Model\Redmine;

/**
 * @property int $id 
 * @property int $attachment_id
 * @property int $product_id
 * @property int $category_id
 * @property int $category_pid
 * @property string $file_type 
 * @property int $responsible_person 
 * @property string $file_version 
 * @property int $file_attribute
 * @property string $file_path
 * @property string $open_status
 * @property string $created_at 
 * @property string $updated_at 
 */
class FilesDocProductModel extends \App\Model\Redmine\RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'files_doc_product';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id',
        'attachment_id',
        'category_id',
        'category_pid',
        'file_type',
        'file_status',
        'responsible_person',
        'file_version',
        'file_attribute',
        'open_status',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'attachment_id' => 'integer', 'responsible_person' => 'integer'];


    public function version()
    {
        return  $this->hasOne('App\Model\Redmine\FilesDocProductVersionModel', 'id', 'file_version');
    }

    public function user()
    {
        return $this->hasOne('App\Model\Redmine\UserModel', 'id', 'responsible_person');
    }

    public function attribute()
    {
        return $this->hasOne('App\Model\Redmine\CategoryModel', 'id', 'file_attribute');
    }

    public function attachment()
    {
        return $this->hasOne('App\Model\Redmine\AttachmentModel', 'id', 'attachment_id');
    }
}