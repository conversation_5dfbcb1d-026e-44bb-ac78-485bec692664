<?php
/*
 * @Description: 
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-21 09:42:35
 * @LastEditors: 张权江 <EMAIL>
 * @LastEditTime: 2025-07-03 09:13:35
 */

declare (strict_types=1);

namespace App\Model\Redmine;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property string $title
 * @property int $project_id
 * @property int $version_id
 * @property int $status
 * @property int $is_archived
 * @property string $begin_time
 * @property string $end_time
 * @property string $description
 * @property string $conclusion
 * @property int $created_by
 * @property int $updated_by
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 */
class TestPlanModel extends RedmineBaseModel
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'test_plan';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'title',
        'project_id',
        'version_id',
        'status',
        'is_archived',
        'begin_date',
        'end_date',
        'description',
        'conclusion',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'          => 'integer',
        'project_id'  => 'integer',
        'version_id'  => 'integer',
        'status'      => 'integer',
        'is_archived' => 'integer',
        'created_by'  => 'integer',
        'updated_by'  => 'integer'
    ];

    public function planUser()
    {
        return $this->hasMany('App\Model\Redmine\TestPlanUserModel', 'test_plan_id', 'id');
    }

    public function planCase()
    {
        return $this->hasMany('App\Model\Redmine\TestPlanCaseModel', 'test_plan_id', 'id')->join('test_case', function ($join) {
            $join->on('test_plan_case.test_case_id', '=', 'test_case.id')
                ->whereNull('test_case.deleted_at')
                ->select([
                    'test_plan_case.*',
                    'test_case.library_id',
                    'test_case.directory_id'
                ]);
        });
    }

}