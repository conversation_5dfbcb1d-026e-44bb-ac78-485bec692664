<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Model;

use App\Constants\ProductionCode;
use App\Core\Utils\Log;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkStatusModel;
use Exception;
use Hyperf\Context\Context;
use Hyperf\Database\Model\Events\Created;
use Hyperf\Database\Model\Events\Deleted;
use Hyperf\Database\Model\Events\Updated;

abstract class ProductionModel extends Model
{
    //不符合规则的表名：已有规则production_order_**,assemble_order_**
    const PRODUCTION_ORDER_INVALID_TABLE = [
        'production_outhelp_record'
    ];
    const ASSEMBLE_ORDER_INVALID_TABLE = [

    ];
    //属性类型
    const PROPERTY_ATTACHMENT_ID = 'attachment_id';
    const PROPERTY_ATTACHMENT_IDS = 'attachment_ids';
    const PROPERTY_ATTACHMENTS = 'attachments';//附件
    const PROPERTY_IMAGES = 'images';//图片
    const PROPERTY_OPTION = 'option';//选项
    const PROPERTY_STRING = 'string';
    const PROPERTY_TEXT = 'text';
    const PROPERTY_USER_ID = 'user_id';
    const PROPERTY_USER_IDS = 'user_ids';//用户id集合
    const PROPERTY_WORK_STATUS_ID = 'work_status_id';
    //属性汇总
    const TOTAL_PROPERTY = [
        self::PROPERTY_ATTACHMENT_ID  => '附件id',
        self::PROPERTY_ATTACHMENT_IDS => '附件id集合',
        self::PROPERTY_ATTACHMENTS    => '附件',
        self::PROPERTY_IMAGES         => '图片集合',
        self::PROPERTY_OPTION         => '选项',
        self::PROPERTY_STRING         => '字符串',
        self::PROPERTY_TEXT           => '文本',
        self::PROPERTY_USER_ID        => '用户id',
        self::PROPERTY_USER_IDS       => '用户id集合',
        self::PROPERTY_WORK_STATUS_ID => '工作状态id',
    ];
    //记录日志的字段
    protected $logField = [];
    //日志变更类型，用于前端显示
    protected $changeCategory = '';


    /**
     * 新增钩子
     * @param Created $event
     * @return void
     */
    public function created(Created $event)
    {
        $this->setChangeTableData('create');
    }

    protected function setChangeTableData($operationType)
    {
        try {
            //获取更新的模型，表以及更新的字段
            $model = get_class($this);
            $table = $this->getTable();
            $origin = $this->getOriginal();
            $now = $this->getAttributes();
            $dirty = $this->getDirty();
            //设置日志的订单类型和订单id
            $order = $this->setOrderType();
            //表记录日志
            $params = [
                'operation_type' => $operationType,
                'order_type'     => $order['order_type'] ?? 0,
                'order_id'       => $order['order_id'] ?? 0,
                'model'        => $model,
                'table_name'   => $table,
                'table_row_id' => $this->id,
                'old_value'    => $origin,
                'new_value'    => $now,
                'dirty'          => $dirty,
            ];
            $this->setChangeCategory($params);
            $logData = Context::get(ProductionCode::LOG_KEY_TABLE_DATA, []);
            $logData[] = $params;
            Context::set(ProductionCode::LOG_KEY_TABLE_DATA, $logData);
        } catch (Exception $e) {
            Log::get('system', 'system')->info("记录生产日志失败：" . $e->getMessage());
        }
    }

    protected function setChangeCategory(&$params)
    {
        $params['change_category'] = $this->changeCategory;
        $params['change_category_tag'] = '';
    }

    /**
     * 根据表名来确认订单类型和订单id
     * @param $dirty
     * @return array
     */
    private function setOrderType()
    {
        $orderType = 0;
        $orderId = 0;
        $tableName = $this->getTable();
        //如果表名是包含assemble_order，则是组装订单，如果等于assemble_order，则订单id为id，否则为assemble_order_id
        //增加属于生产订单或者组装订单的表但是表名不符合规则的判断
        if (strpos($tableName, 'assemble_order') !== false || in_array($tableName, self::ASSEMBLE_ORDER_INVALID_TABLE)) {
            $orderType = ProductionCode::ORDER_TYPE_ASSEMBLE;
            if ($tableName == 'assemble_order') {
                $orderId = $this->id;
            } else {
                $orderId = isset($this->assemble_order_id) ? $this->assemble_order_id : 0;
            }
        } elseif (strpos($tableName, 'production_order') !== false || in_array($tableName, self::PRODUCTION_ORDER_INVALID_TABLE)) {
            $orderType = ProductionCode::ORDER_TYPE_PRODUCTION;
            if ($tableName == 'production_order') {
                $orderId = $this->id;
            } else {
                $orderId = isset($this->production_order_id) ? $this->production_order_id : 0;
            }
        }
        return [
            'order_type' => $orderType,
            'order_id'   => $orderId
        ];

    }

    /**
     * 更新钩子
     * @param Updated $event
     * @return void
     */
    public function updated(Updated $event)
    {
        $this->setChangeTableData('update');
    }

    /**
     * 删除钩子
     * @param Deleted $event
     * @return void
     */
    public function deleted(Deleted $event)
    {
        $this->setChangeTableData('delete');
    }

    /**
     * 获取日志需记录字段
     * @return array|mixed
     */
    public function getLogField($operateType = '')
    {

        $logField = $this->logField;

        foreach ($logField as &$value) {
            $value['property_text'] = !empty($value['property']) ? (self::TOTAL_PROPERTY[$value['property']] ?? '') : '';
        }
        return $logField;
    }

    public function setFieldValueText(array $fields)
    {
        $this->filterEmptyField($fields);
        $fields = $this->setPublicFieldValueText($fields);
        return $this->setSelfFieldValueText($fields);
    }

    /**
     * 过滤掉格式更新但值为空的字段
     * @param $fields
     * @return void
     */
    public function filterEmptyField(&$fields)
    {
        foreach ($fields as $key => $value) {
            if (empty($value['property'])) {
                continue;
            }
            $property = $value['property'];
            switch ($property) {
                case self::PROPERTY_ATTACHMENT_IDS:
                case self::PROPERTY_IMAGES:
                    $oldValueArr = json_decode($value['old_value'] ?? '[]', true);
                    $newValueArr = json_decode($value['new_value'] ?? '[]', true);
                    if (empty($oldValueArr) && empty($newValueArr)) {
                        unset($fields[$key]);
                    }
                    break;
                default:
                    if (empty($value['old_value']) && empty($value['new_value'])) {
                        unset($fields[$key]);
                    }
                    break;
            }
        }
    }

    private function setPublicFieldValueText(array $fields)
    {
        //汇总当property为user_id和user_ids的旧新值
        $userField = array_filter($fields, function ($field) {
            return $field['property'] == self::PROPERTY_USER_ID;
        });
        $userIdsField = array_filter($fields, function ($field) {
            return $field['property'] == self::PROPERTY_USER_IDS;
        });
        
        $userId = array_merge(array_column($userField, 'old_value'), array_column($userField, 'new_value'));
        
        // 处理user_ids字段的用户ID集合
        foreach ($userIdsField as $field) {
            $oldUserIds = json_decode($field['old_value'] ?? '[]', true);
            $newUserIds = json_decode($field['new_value'] ?? '[]', true);
            if (is_array($oldUserIds)) {
                $userId = array_merge($userId, $oldUserIds);
            }
            if (is_array($newUserIds)) {
                $userId = array_merge($userId, $newUserIds);
            }
        }
        
        $userId = array_filter(array_unique($userId));
        $userData = $userId ? UserModel::query()->whereIn('id', $userId)->pluck('name', 'id')->toArray() : [];

        foreach ($fields as $key => &$value) {
            if (empty($value['property'])) {
                continue;
            }
            $property = $value['property'];
            switch ($property) {
                case self::PROPERTY_ATTACHMENT_ID:
                case self::PROPERTY_ATTACHMENT_IDS:
                    //附件id类的，不保存文本描述
                    $value['old_value_text'] = $value['new_value_text'] = '';
                    break;
                case self::PROPERTY_USER_ID:
                    //用户ID类的，转为用户名
                    $value['old_value_text'] = $userData[$value['old_value']] ?? '';
                    $value['new_value_text'] = $userData[$value['new_value']] ?? '';
                    break;
                case self::PROPERTY_USER_IDS:
                    //用户ID集合类的，转为用户名集合
                    $oldUserIds = json_decode($value['old_value'] ?? '[]', true);
                    $newUserIds = json_decode($value['new_value'] ?? '[]', true);
                    
                    $oldUserNames = [];
                    if (is_array($oldUserIds)) {
                        foreach ($oldUserIds as $uid) {
                            if (isset($userData[$uid])) {
                                $oldUserNames[] = $userData[$uid];
                            }
                        }
                    }
                    
                    $newUserNames = [];
                    if (is_array($newUserIds)) {
                        foreach ($newUserIds as $uid) {
                            if (isset($userData[$uid])) {
                                $newUserNames[] = $userData[$uid];
                            }
                        }
                    }
                    
                    $value['old_value_text'] = implode('、', $oldUserNames);
                    $value['new_value_text'] = implode('、', $newUserNames);
                    break;
                case self::PROPERTY_WORK_STATUS_ID:
                    $value['old_value_text'] = WorkStatusModel::query()->where('id', $value['old_value'])->value('name') ?? '';
                    $value['new_value_text'] = WorkStatusModel::query()->where('id', $value['new_value'])->value('name') ?? '';
                    break;
//                case self::PROPERTY_TEXT:
//                    //文本类的，只是改变格式(如null改为'')但为空时，不记录
//                    if(empty($value['old_value'])&&empty($value['new_value'])){
//                        unset($fields[$key]);
//                    }else{
//                        $value['old_value_text'] = $value['old_value'];
//                        $value['new_value_text'] = $value['new_value'];
//                    }
//                    break;
                default:
                    $value['old_value_text'] = $value['old_value'];
                    $value['new_value_text'] = $value['new_value'];
                    break;
            }
        }
        return $fields;
    }

    //每个表的字段的自定义属性，比如状态
    public function setSelfFieldValueText(array $fields): array
    {
        return $fields;
    }
}
