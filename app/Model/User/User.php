<?php

declare (strict_types=1);
namespace App\Model\User;

use Hyperf\Database\Model\SoftDeletes;
use Qbhy\HyperfAuth\AuthAbility;
use Qbhy\HyperfAuth\Authenticatable;

/**
 * @property int $id 
 * @property string $userid 成员UserID
 * @property string $name 
 * @property int $mobile
 * @property string $password
 * @property string $salt
 * @property string $department 
 * @property string $order 部门内的排序值，默认为0
 * @property string $position 
 * @property int $gender 性别:0表示未定义，1表示男性，2表示女性
 * @property string $email 
 * @property string $biz_mail 
 * @property string $is_leader_in_dept 
 * @property string $direct_leader 
 * @property string $avatar 
 * @property string $thumb_avatar 
 * @property string $telephone 
 * @property string $alias 
 * @property string $extattr 
 * @property int $status 
 * @property string $qr_code 
 * @property string $external_profile 
 * @property string $external_position 
 * @property string $address 
 * @property string $open_userid 
 * @property string $main_department 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class User extends \App\Model\Model implements Authenticatable
{
    use SoftDeletes, AuthAbility;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'userid', 'name', 'mobile', 'password', 'salt', 'department', 'order', 'position', 'gender', 'email', 'biz_mail',
        'is_leader_in_dept', 'direct_leader', 'avatar', 'thumb_avatar', 'telephone', 'alias', 'extattr', 'status',
        'qr_code', 'external_profile', 'external_position', 'address', 'open_userid', 'main_department'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'mobile' => 'integer',
        'department' => 'array',
        'order' => 'array',
        'is_leader_in_dept' => 'array',
        'direct_leader' => 'array',
        'extattr' => 'array',
        'external_profile' => 'array',
        'gender' => 'integer',
        'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

}