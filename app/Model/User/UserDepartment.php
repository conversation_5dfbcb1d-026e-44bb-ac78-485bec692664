<?php

declare (strict_types=1);
namespace App\Model\User;

use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property string $name 
 * @property string $name_en 
 * @property int $department_leader 部门负责人的UserID
 * @property int $parentid 父部门id
 * @property int $order 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class UserDepartment extends \App\Model\Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_department';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['id', 'name', 'department_leader', 'parentid', 'order'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer', 'department_leader' => 'array', 'parentid' => 'integer', 'order' => 'integer',
        'created_at' => 'datetime', 'updated_at' => 'datetime'];


}