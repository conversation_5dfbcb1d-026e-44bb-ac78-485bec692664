<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

/**
 * @property int $stat_id 
 * @property int $doc_id 
 * @property string $date 
 * @property int $view_count 
 * @property int $like_count 
 * @property int $comment_count 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class WikiDocumentStatisticModel extends \App\Model\Model
{
    protected $primaryKey = 'stat_id';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'wiki_document_statistics';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'doc_id',
        'date',
        'view_count',
        'like_count',
        'comment_count',
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'stat_id'       => 'integer',
        'doc_id'        => 'integer',
        'view_count'    => 'integer',
        'like_count'    => 'integer',
        'comment_count' => 'integer',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    /**
     * 获取格式化后的创建时间（不显示秒）
     *
     * @return string
     */
    public function getCreatedAtAttribute($value)
    {
        return $value ? substr($value, 0, 16) : null;
    }

    /**
     * 获取格式化后的更新时间（不显示秒）
     *
     * @return string
     */
    public function getUpdatedAtAttribute($value)
    {
        return $value ? substr($value, 0, 16) : null;
    }

    /**
     * 所属文档
     */
    public function document()
    {
        return $this->belongsTo(WikiDocumentModel::class, 'doc_id', 'doc_id');
    }
}