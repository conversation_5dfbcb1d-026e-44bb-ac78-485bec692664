<?php

declare(strict_types=1);

namespace App\Model\TchipBi;

use Carbon\Carbon;

/**
 * @property string $status_type 状态类型标识
 * @property string $description 业务含义描述
 * @property int $is_active 是否启用
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
class WikiDocumentStatusTypeModel extends \App\Model\Model
{
    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'status_type';

    /**
     * 主键类型
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * 是否自动递增
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'wiki_document_status_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'status_type',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取所有启用的状态类型
     *
     * @return \Hyperf\Database\Model\Collection
     */
    public static function getActiveTypes()
    {
        return static::where('is_active', 1)->get();
    }

    /**
     * 检查状态类型是否存在且启用
     *
     * @param string $statusType
     * @return bool
     */
    public static function isActiveType(string $statusType): bool
    {
        return static::where('status_type', $statusType)
            ->where('is_active', 1)
            ->exists();
    }

    /**
     * 获取状态类型描述
     *
     * @param string $statusType
     * @return string|null
     */
    public static function getDescription(string $statusType): ?string
    {
        $type = static::where('status_type', $statusType)->first();
        return $type ? $type->description : null;
    }

    /**
     * 状态类型常量
     */
    const TYPE_PREMIUM = 'premium';    // 精华认证
    const TYPE_TRAINING = 'training';  // 培训认证
    const TYPE_AUDIT = 'audit';        // 审核状态

    /**
     * 获取所有状态类型常量
     *
     * @return array
     */
    public static function getAllTypes(): array
    {
        return [
            self::TYPE_PREMIUM,
            self::TYPE_TRAINING,
            self::TYPE_AUDIT,
        ];
    }
}