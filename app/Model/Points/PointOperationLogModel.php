<?php

declare(strict_types=1);

namespace App\Model\Points;

use App\Model\TchipBi\UserModel;
use Hyperf\DbConnection\Model\Model;

/**
 * 积分操作日志模型
 * 
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $operation_type 操作类型
 * @property int $before_points 操作前积分
 * @property int $after_points 操作后积分
 * @property int $point_change 积分变化量
 * @property string $operation_reason 操作原因
 * @property int $operator_id 操作员ID
 * @property string $operator_type 操作员类型
 * @property int $related_record_id 关联的积分记录ID
 * @property string $batch_id 批量操作ID
 * @property string $ip_address 操作IP
 * @property string $user_agent 用户代理
 * @property \Carbon\Carbon $created_at
 */
class PointOperationLogModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'point_operation_logs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'operation_type',
        'before_points',
        'after_points',
        'point_change',
        'operation_reason',
        'operator_id',
        'operator_type',
        'related_record_id',
        'batch_id',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'before_points' => 'integer',
        'after_points' => 'integer',
        'point_change' => 'integer',
        'operator_id' => 'integer',
        'related_record_id' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(\App\Model\User::class, 'user_id', 'id');
    }

    /**
     * 关联操作员
     */
    public function operator()
    {
        return $this->belongsTo(UserModel::class, 'operator_id', 'id');
    }

    /**
     * 关联积分记录
     */
    public function relatedRecord()
    {
        return $this->belongsTo(PointRecordModel::class, 'related_record_id', 'id');
    }

    /**
     * 按用户查询作用域
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按操作类型查询作用域
     */
    public function scopeByOperationType($query, string $type)
    {
        return $query->where('operation_type', $type);
    }

    /**
     * 按操作员查询作用域
     */
    public function scopeByOperator($query, int $operatorId)
    {
        return $query->where('operator_id', $operatorId);
    }

    /**
     * 系统操作作用域
     */
    public function scopeSystemOperation($query)
    {
        return $query->where('operator_type', 'system');
    }

    /**
     * 管理员操作作用域
     */
    public function scopeAdminOperation($query)
    {
        return $query->where('operator_type', 'admin');
    }

    /**
     * 最新记录作用域
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}