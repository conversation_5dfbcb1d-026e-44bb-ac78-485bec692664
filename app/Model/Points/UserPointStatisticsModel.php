<?php

declare(strict_types=1);

namespace App\Model\Points;

use App\Model\Redmine\UserModel;
use Hyperf\DbConnection\Model\Model;

/**
 * 用户积分统计模型
 * 
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $stat_date 统计日期
 * @property string $stat_type 统计类型
 * @property int $points_earned 获得积分
 * @property int $points_consumed 消费积分
 * @property int $net_points 净积分
 * @property int $article_published 发布文档数
 * @property int $likes_received 获得点赞数
 * @property int $comments_made 评论数
 * @property int $documents_viewed 浏览文档数
 * @property int $active_days 活跃天数
 * @property int $rank_in_period 期间排名
 * @property int $achievements_unlocked 解锁成就数
 * @property int $level_at_period_start 期间开始时等级
 * @property int $level_at_period_end 期间结束时等级
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserPointStatisticsModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_point_statistics';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'stat_date',
        'stat_type',
        'points_earned',
        'points_consumed',
        'net_points',
        'article_published',
        'likes_received',
        'comments_made',
        'documents_viewed',
        'active_days',
        'rank_in_period',
        'achievements_unlocked',
        'level_at_period_start',
        'level_at_period_end',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'stat_date' => 'date',
        'points_earned' => 'integer',
        'points_consumed' => 'integer',
        'net_points' => 'integer',
        'article_published' => 'integer',
        'likes_received' => 'integer',
        'comments_made' => 'integer',
        'documents_viewed' => 'integer',
        'active_days' => 'integer',
        'rank_in_period' => 'integer',
        'achievements_unlocked' => 'integer',
        'level_at_period_start' => 'integer',
        'level_at_period_end' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }

    /**
     * 按用户查询作用域
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按统计类型查询作用域
     */
    public function scopeByStatType($query, string $type)
    {
        return $query->where('stat_type', $type);
    }

    /**
     * 按日期查询作用域
     */
    public function scopeByDate($query, string $date)
    {
        return $query->where('stat_date', $date);
    }

    /**
     * 日统计作用域
     */
    public function scopeDaily($query)
    {
        return $query->where('stat_type', 'daily');
    }

    /**
     * 周统计作用域
     */
    public function scopeWeekly($query)
    {
        return $query->where('stat_type', 'weekly');
    }

    /**
     * 月统计作用域
     */
    public function scopeMonthly($query)
    {
        return $query->where('stat_type', 'monthly');
    }

    /**
     * 年统计作用域
     */
    public function scopeYearly($query)
    {
        return $query->where('stat_type', 'yearly');
    }

    /**
     * 按日期排序作用域
     */
    public function scopeByDateDesc($query)
    {
        return $query->orderBy('stat_date', 'desc');
    }

    /**
     * 按获得积分排序作用域
     */
    public function scopeByPointsEarnedDesc($query)
    {
        return $query->orderBy('points_earned', 'desc');
    }
}