<?php

declare(strict_types=1);

namespace App\Model\Points;

use Hyperf\DbConnection\Model\Model;

/**
 * 等级配置模型
 * 
 * @property int $id 主键ID
 * @property int $level 等级数值
 * @property string $level_name 等级名称
 * @property string $level_title 等级称号
 * @property int $min_points 最低积分要求
 * @property int $max_points 最高积分
 * @property string $color 等级颜色
 * @property string $icon 等级图标URL
 * @property string $badge_image 等级徽章图片URL
 * @property array $privileges 等级特权配置
 * @property int $upgrade_reward 升级奖励积分
 * @property string $description 等级描述
 * @property bool $is_active 是否启用
 * @property int $sort_order 排序
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class LevelConfigModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'level_configs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'level',
        'level_name',
        'level_title',
        'min_points',
        'max_points',
        'color',
        'icon',
        'badge_image',
        'privileges',
        'upgrade_reward',
        'description',
        'is_active',
        'sort_order',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'level' => 'integer',
        'min_points' => 'integer',
        'max_points' => 'integer',
        'privileges' => 'array',
        'upgrade_reward' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联用户积分
     */
    public function userPoints()
    {
        return $this->hasMany(UserPointsModel::class, 'level', 'level');
    }

    /**
     * 启用状态作用域
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 按等级排序作用域
     */
    public function scopeByLevel($query)
    {
        return $query->orderBy('level');
    }

    /**
     * 按排序字段排序作用域
     */
    public function scopeBySortOrder($query)
    {
        return $query->orderBy('sort_order')->orderBy('level');
    }

    /**
     * 等级范围查询作用域
     */
    public function scopeInLevelRange($query, int $minLevel, int $maxLevel)
    {
        return $query->whereBetween('level', [$minLevel, $maxLevel]);
    }

    /**
     * 根据积分查找等级作用域
     */
    public function scopeForPoints($query, int $points)
    {
        return $query->where('min_points', '<=', $points)
            ->where(function ($subQuery) use ($points) {
                $subQuery->where('max_points', '>=', $points)
                    ->orWhereNull('max_points');
            });
    }
}