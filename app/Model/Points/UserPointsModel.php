<?php

declare(strict_types=1);

namespace App\Model\Points;

use App\Model\TchipBi\UserModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
 * 用户积分模型
 * 
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property int $current_points 当前可用积分
 * @property int $total_points 累计获得积分
 * @property int $consumed_points 已消费积分
 * @property int $level 用户等级
 * @property string $level_name 等级名称
 * @property string $last_points_at 最后获得积分时间
 * @property string $level_updated_at 等级更新时间
 * @property int $version 乐观锁版本号
 * @property int $daily_points 今日获得积分
 * @property int $weekly_points 本周获得积分
 * @property int $monthly_points 本月获得积分
 * @property int $year_points 本年获得积分
 * @property string $last_daily_reset 日积分最后重置日期
 * @property string $last_weekly_reset 周积分最后重置日期
 * @property string $last_monthly_reset 月积分最后重置日期
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserPointsModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_points';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'current_points',
        'total_points',
        'consumed_points',
        'level',
        'level_name',
        'last_points_at',
        'level_updated_at',
        'version',
        'daily_points',
        'weekly_points',
        'monthly_points',
        'year_points',
        'last_daily_reset',
        'last_weekly_reset',
        'last_monthly_reset',
        'last_yearly_reset',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'current_points' => 'integer',
        'total_points' => 'integer',
        'consumed_points' => 'integer',
        'level' => 'integer',
        'version' => 'integer',
        'daily_points' => 'integer',
        'weekly_points' => 'integer',
        'monthly_points' => 'integer',
        'year_points' => 'integer',
        'last_points_at' => 'datetime',
        'level_updated_at' => 'datetime',
        'last_daily_reset' => 'date',
        'last_weekly_reset' => 'date',
        'last_monthly_reset' => 'date',
        'last_yearly_reset' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }

    /**
     * 关联积分记录
     */
    public function pointRecords()
    {
        return $this->hasMany(PointRecordModel::class, 'user_id', 'user_id');
    }

    /**
     * 关联用户成就
     */
    public function userAchievements()
    {
        return $this->hasMany(UserAchievementModel::class, 'user_id', 'user_id');
    }

    /**
     * 获取等级配置
     */
    public function levelConfig()
    {
        return $this->belongsTo(LevelConfigModel::class, 'level', 'level');
    }

    /**
     * 按积分排序的作用域
     */
    public function scopeByPointsDesc($query)
    {
        return $query->orderBy('current_points', 'desc');
    }

    /**
     * 按等级排序的作用域
     */
    public function scopeByLevelDesc($query)
    {
        return $query->orderBy('level', 'desc')->orderBy('current_points', 'desc');
    }

    /**
     * 活跃用户作用域（最近30天有积分变动）
     */
    public function scopeActive($query)
    {
        return $query->where('last_points_at', '>=', Carbon::now()->subDays(30));
    }
}