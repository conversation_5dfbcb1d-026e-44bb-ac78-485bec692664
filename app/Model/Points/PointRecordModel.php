<?php

declare(strict_types=1);

namespace App\Model\Points;

use App\Model\TchipBi\UserModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
 * 积分记录模型
 * 
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $point_type 积分类型
 * @property int $point_change 积分变化量
 * @property int $current_points 变化后的当前积分
 * @property string $source_type 来源类型
 * @property int $source_id 来源对象ID
 * @property string $source_title 来源对象标题
 * @property string $description 积分变化描述
 * @property string $remark 管理员备注
 * @property float $multiplier 积分倍数
 * @property string $bonus_reason 额外奖励原因
 * @property int $is_daily_first 是否为当日首次该类型操作
 * @property int $is_reversed 是否已被撤销
 * @property string $reversed_at 撤销时间
 * @property string $reversed_reason 撤销原因
 * @property int $created_by 操作人
 * @property string $ip_address 操作IP地址
 * @property string $user_agent 用户代理
 * @property \Carbon\Carbon $created_at
 */
class PointRecordModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'point_records';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'point_type',
        'point_change',
        'current_points',
        'source_type',
        'source_id',
        'source_title',
        'description',
        'remark',
        'multiplier',
        'bonus_reason',
        'is_daily_first',
        'is_reversed',
        'reversed_at',
        'reversed_reason',
        'created_by',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'point_change' => 'integer',
        'current_points' => 'integer',
        'source_id' => 'integer',
        'multiplier' => 'float',
        'is_daily_first' => 'boolean',
        'is_reversed' => 'boolean',
        'reversed_at' => 'datetime',
        'created_by' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }

    /**
     * 关联创建者
     */
    public function creator()
    {
        return $this->belongsTo(UserModel::class, 'created_by', 'id');
    }

    /**
     * 关联积分配置
     */
    public function pointConfig()
    {
        return $this->belongsTo(PointConfigModel::class, 'point_type', 'action_type');
    }

    /**
     * 今日首次作用域
     */
    public function scopeDailyFirst($query)
    {
        return $query->where('is_daily_first', true);
    }

    /**
     * 未撤销作用域
     */
    public function scopeNotReversed($query)
    {
        return $query->where('is_reversed', false);
    }

    /**
     * 按类型查询作用域
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('point_type', $type);
    }

    /**
     * 按用户查询作用域
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 今日记录作用域
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::now()->toDateString());
    }

    /**
     * 本周记录作用域
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    /**
     * 本月记录作用域
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('created_at', [
            Carbon::now()->startOfMonth(),
            Carbon::now()->endOfMonth()
        ]);
    }

    /**
     * 按积分变化排序作用域
     */
    public function scopeByPointsDesc($query)
    {
        return $query->orderBy('point_change', 'desc');
    }

    /**
     * 最新记录作用域
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}