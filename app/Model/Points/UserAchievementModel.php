<?php

declare(strict_types=1);

namespace App\Model\Points;

use App\Model\TchipBi\UserModel;
use Hyperf\DbConnection\Model\Model;

/**
 * 用户成就记录模型
 * 
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property int $achievement_id 成就ID
 * @property string $achievement_code 成就代码
 * @property int $progress 当前进度值
 * @property int $target_value 目标值
 * @property float $progress_percentage 完成百分比
 * @property bool $is_completed 是否已完成
 * @property string $completed_at 完成时间
 * @property string $notified_at 通知时间
 * @property bool $is_displayed 是否在用户页面显示
 * @property int $completion_count 完成次数
 * @property string $last_updated_at 进度最后更新时间
 * @property array $metadata 扩展数据
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserAchievementModel extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_achievements';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'achievement_id',
        'achievement_code',
        'progress',
        'target_value',
        'progress_percentage',
        'is_completed',
        'completed_at',
        'notified_at',
        'is_displayed',
        'completion_count',
        'last_updated_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'achievement_id' => 'integer',
        'progress' => 'integer',
        'target_value' => 'integer',
        'progress_percentage' => 'float',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'notified_at' => 'datetime',
        'is_displayed' => 'boolean',
        'completion_count' => 'integer',
        'last_updated_at' => 'datetime',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }

    /**
     * 关联成就表
     */
    public function achievement()
    {
        return $this->belongsTo(AchievementModel::class, 'achievement_id', 'id');
    }

    /**
     * 已完成作用域
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * 进行中作用域
     */
    public function scopeInProgress($query)
    {
        return $query->where('is_completed', false)->where('progress', '>', 0);
    }

    /**
     * 按用户查询作用域
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按成就查询作用域
     */
    public function scopeByAchievement($query, int $achievementId)
    {
        return $query->where('achievement_id', $achievementId);
    }

    /**
     * 显示的成就作用域
     */
    public function scopeDisplayed($query)
    {
        return $query->where('is_displayed', true);
    }

    /**
     * 按完成时间排序作用域
     */
    public function scopeByCompletedAt($query)
    {
        return $query->orderBy('completed_at', 'desc');
    }

    /**
     * 按进度百分比排序作用域
     */
    public function scopeByProgress($query)
    {
        return $query->orderBy('progress_percentage', 'desc');
    }
}