<?php

declare(strict_types=1);

namespace App\Request;

use Hyperf\Validation\Request\FormRequest;

class MarketingContentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title'       => 'required',
            'platform_id' => 'required',
            'channel_id'  => 'required',
            'exposure'    => 'required',
            'link'        => 'required',
            'type'        => 'required',
        ];
    }
}
