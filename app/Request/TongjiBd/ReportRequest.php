<?php

declare(strict_types=1);

namespace App\Request\TongjiBd;

use Hyperf\Validation\Request\FormRequest;

class ReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'site_id' => 'required',
        ];
    }

    // public function messages(): array
    // {
    //     // return parent::messages(); // TODO: Change the autogenerated stub
    //     return [
    //         'account.require' => 'account is not number'
    //     ];
    // }
}
