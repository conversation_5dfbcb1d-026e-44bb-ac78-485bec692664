<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/21 下午8:27
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\SystemService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\Utils\Coroutine;
use Hyperf\Utils\Coroutine\Concurrent;
use Hyperf\Utils\Exception\ExceptionThrower;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("系统设置")
 * @AutoController()
 */
class SystemController extends BaseController
{
    /**
     * @Inject()
     * @var SystemService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("清除缓存")
     * @return ResponseInterface
     */
    public function flushCache(): ResponseInterface
    {
        $result = $this->service->flushCache();
        return $this->response->success($result);
    }

    /**
     * 立刻执行任务
     * @return ResponseInterface
     */
    public function doNowTask()
    {
        $id = $this->request->input('id');
        if (!$id) {
            $this->response->error('pass id is not exist');
        }
        $result = $this->service->doNowTask($id);
        return $this->response->success($result);
    }

    public function imgToBase64()
    {
        $url = $this->request->input('url');
        $base = '';
        if ($url) {
            if (!is_array($url)) {
                $base = imgToBase64($url);
            } else {
                $base = [];
                $concurrent = new Concurrent(20);
                foreach ($url as $u) {
                    $channel = new \Swoole\Coroutine\Channel();
                    $concurrent->create(function () use ($channel, $u) {
                        try {
                            $result = [
                                'key' => $u['key'],
                                'base64' => imgToBase64($u['url'])
                            ];
                        } catch (Throwable $exception) {
                            $result = new ExceptionThrower($exception);
                        } finally {
                            $channel->push($result ?? null);
                        }
                    });
                    $result = $channel->pop(null);
                    $base[] = $result;
                }
            }
        }
        return $this->response->success($base);
    }
}