<?php

declare(strict_types=1);

namespace App\Controller\Project;

use App\Controller\BaseController;
use App\Core\Services\Project\CustomWorkflowService;
use App\Constants\StatusCode;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Di\Annotation\Inject;

/**
 * @Controller()
 */
class CustomWorkflowController extends BaseController
{
    /**
     * @Inject()
     * @var CustomWorkflowService
     */
    protected $customWorkflowService;

    /**
     * 获取项目状态列表
     * @RequestMapping(path="/custom-workflow/statuses", methods="GET")
     */
    public function getProjectStatuses()
    {
        try {
            $projectId = $this->request->input('project_id');
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $data = $this->customWorkflowService->getProjectStatuses($projectId);
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 创建自定义状态
     * @RequestMapping(path="/custom-workflow/statuses/create", methods="POST")
     */
    public function createStatus()
    {
        try {
            $projectId = $this->request->input('project_id');
            $params = $this->request->all();
            unset($params['project_id']);
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $data = $this->customWorkflowService->createCustomStatus($projectId, $params);
            
            return $this->response->success($data, '创建成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 更新自定义状态
     * @RequestMapping(path="/custom-workflow/statuses/update", methods="POST")
     */
    public function updateStatus()
    {
        try {
            $statusId = $this->request->input('status_id');
            $params = $this->request->all();
            unset($params['status_id']);
            
            if (empty($statusId)) {
                return $this->response->error('状态ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $result = $this->customWorkflowService->updateCustomStatus($statusId, $params);
            
            return $this->response->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 删除自定义状态
     * @RequestMapping(path="/custom-workflow/statuses/delete", methods="POST")
     */
    public function deleteStatus()
    {
        try {
            $statusId = $this->request->input('status_id');
            
            if (empty($statusId)) {
                return $this->response->error('状态ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $result = $this->customWorkflowService->deleteCustomStatus($statusId);
            
            return $this->response->success($result, '删除成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取工作流配置(暂无用)
     * @RequestMapping(path="/custom-workflow/workflows", methods="GET")
     */
    public function getWorkflows()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            
            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $data = $this->customWorkflowService->getCustomWorkflows($projectId, $trackerId);
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取工作流矩阵
     * @RequestMapping(path="/custom-workflow/matrix", methods="GET")
     */
    public function getWorkflowMatrix()
    {
        try {
            $projectId = $this->request->input('project_id');
            $templateId = $this->request->input('template_id'); // 模板ID参数
            $forNewTemplate = $this->request->input('for_new_template', false); // 是否为创建新模板

            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            // 支持按模板ID查询工作流矩阵，或为创建新模板返回空配置
            $data = $this->customWorkflowService->getWorkflowMatrix($projectId, null, $templateId, $forNewTemplate);

            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 保存工作流配置
     * @RequestMapping(path="/custom-workflow/workflows/save", methods="POST")
     */
    public function saveWorkflows()
    {
        try {
            $projectId = $this->request->input('project_id');
            $configs = $this->request->input('configs', []);

            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            // 项目级别工作流配置，不再需要 tracker_id
            $result = $this->customWorkflowService->saveWorkflowConfig($projectId, null, $configs);

            return $this->response->success($result, '保存成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取可用状态列表
     * @RequestMapping(path="/custom-workflow/available-statuses", methods="GET")
     */
    public function getAvailableStatuses()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            $currentStatusId = $this->request->input('current_status_id');
            $issueId = $this->request->input('issue_id');
            
            if (empty($projectId) || empty($trackerId) || empty($currentStatusId)) {
                return $this->response->error('项目ID、事项类型ID和当前状态ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $userId = getRedmineUserId();
            $data = $this->customWorkflowService->getAvailableStatuses(
                $projectId, 
                $trackerId, 
                $currentStatusId, 
                $userId, 
                $issueId
            );
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 检查是否启用自定义工作流
     * @RequestMapping(path="/custom-workflow/enabled", methods="GET")
     */
    public function isCustomWorkflowEnabled()
    {
        try {
            $projectId = $this->request->input('project_id');
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $enabled = $this->customWorkflowService->isCustomWorkflowEnabled($projectId);
            
            return $this->response->success(['enabled' => $enabled]);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 切换自定义工作流状态
     * @RequestMapping(path="/custom-workflow/toggle", methods="POST")
     */
    public function toggleCustomWorkflow()
    {
        try {
            $projectId = $this->request->input('project_id');
            $enabled = $this->request->input('enabled', false);
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $result = $this->customWorkflowService->toggleCustomWorkflow($projectId, $enabled);
            
            $message = $enabled ? '已启用自定义工作流' : '已禁用自定义工作流';
            return $this->response->success($result, $message);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取项目角色列表
     * @RequestMapping(path="/custom-workflow/roles", methods="GET")
     */
    public function getProjectRoles()
    {
        try {
            $projectId = $this->request->input('project_id');
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $roles = $this->customWorkflowService->getProjectRoles($projectId);
            
            return $this->response->success($roles);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取事项类型列表
     * @RequestMapping(path="/custom-workflow/trackers", methods="GET")
     */
    public function getProjectTrackers()
    {
        try {
            $projectId = $this->request->input('project_id');
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $trackers = $this->customWorkflowService->getProjectTrackers($projectId);
            
            return $this->response->success($trackers);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    // =================== 模板管理API ===================

    /**
     * 获取可用的工作流模板列表
     * @RequestMapping(path="/custom-workflow/templates", methods="GET")
     */
    public function getAvailableTemplates()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            $data = $this->customWorkflowService->getAvailableTemplates($projectId, $trackerId);
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 创建模板
     * @RequestMapping(path="/custom-workflow/templates/create", methods="POST")
     */
    public function createTemplate()
    {
        try {
            $projectId = $this->request->input('project_id');
            // $trackerId = $this->request->input('tracker_id');
            $templateName = $this->request->input('template_name');
            $description = $this->request->input('description');
            $configs = $this->request->input('configs', []);
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            // if (empty($trackerId)) {
            //     return $this->response->error('事项类型不能为空', StatusCode::VALIDATION_ERROR);
            // }
            
            if (empty($templateName)) {
                return $this->response->error('模板名称不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            if (empty($configs)) {
                return $this->response->error('配置数据不能为空', StatusCode::VALIDATION_ERROR);
            }

            $templateId = $this->customWorkflowService->createTemplate($projectId, $templateName, $configs, $description);
            
            return $this->response->success(['template_id' => $templateId], '模板创建成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 更新模板
     * @RequestMapping(path="/custom-workflow/templates/update", methods="POST")
     */
    public function updateTemplate()
    {
        try {
            $templateId = $this->request->input('template_id');
            $templateName = $this->request->input('template_name');
            $description = $this->request->input('description');
            $configs = $this->request->input('configs', []);
            
            if (empty($templateId)) {
                return $this->response->error('模板ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            if (empty($templateName)) {
                return $this->response->error('模板名称不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            if (empty($configs)) {
                return $this->response->error('配置数据不能为空', StatusCode::VALIDATION_ERROR);
            }

            $result = $this->customWorkflowService->updateTemplate($templateId, $templateName, $configs, $description);
            
            return $this->response->success($result, '模板更新成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 应用模板到项目
     * @RequestMapping(path="/custom-workflow/templates/apply-to-project", methods="POST")
     */
    public function applyTemplateToProject()
    {
        try {
            $projectId = $this->request->input('project_id');
            $templateId = $this->request->input('template_id');
            
            if (empty($projectId)) {
                return $this->response->error('项目ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            if (empty($templateId)) {
                return $this->response->error('模板ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            $result = $this->customWorkflowService->applyTemplateToProject($templateId, $projectId);
            
            return $this->response->success($result, '模板应用成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 复制模板
     * @RequestMapping(path="/custom-workflow/templates/copy", methods="POST")
     */
    public function copyTemplate()
    {
        try {
            $sourceTemplateId = $this->request->input('source_template_id');
            $newTemplateName = $this->request->input('new_template_name');
            
            if (empty($sourceTemplateId) || empty($newTemplateName)) {
                return $this->response->error('源模板ID和新模板名称不能为空', StatusCode::VALIDATION_ERROR);
            }

            $newTemplateId = $this->customWorkflowService->copyTemplate($sourceTemplateId, $newTemplateName);
            
            return $this->response->success(['template_id' => $newTemplateId], '模板复制成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 删除模板
     * @RequestMapping(path="/custom-workflow/templates/delete", methods="POST")
     */
    public function deleteTemplate()
    {
        try {
            $templateId = $this->request->input('template_id');
            
            if (empty($templateId)) {
                return $this->response->error('模板ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $result = $this->customWorkflowService->deleteTemplate($templateId);
            
            return $this->response->success($result, '模板删除成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取模板详情
     * @RequestMapping(path="/custom-workflow/templates/details", methods="GET")
     */
    public function getTemplateDetails()
    {
        try {
            $templateId = $this->request->input('template_id');
            
            if (empty($templateId)) {
                return $this->response->error('模板ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $data = $this->customWorkflowService->getTemplateDetails($templateId);
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 绑定模板到事项类型
     * @RequestMapping(path="/custom-workflow/templates/bind-to-tracker", methods="POST")
     */
    public function bindTemplateToTracker()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            $templateId = $this->request->input('template_id');
            
            $result = $this->customWorkflowService->bindTemplateToTracker($projectId, $trackerId, $templateId);
            
            return $this->response->success($result, '绑定模板成功');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 解绑模板
     * @RequestMapping(path="/custom-workflow/templates/unbind-from-tracker", methods="POST")
     */
    // public function unbindTemplateFromTracker()
    // {
    //     try {
    //         $projectId = $this->request->input('project_id');
    //         $trackerId = $this->request->input('tracker_id');
            
    //         $result = $this->customWorkflowService->unbindTemplateFromTracker($projectId, $trackerId);
            
    //         return $this->response->success($result, '解绑模板成功');
    //     } catch (\Exception $e) {
    //         return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
    //     }
    // }

    /**
     * 获取事项类型的模板绑定
     * @RequestMapping(path="/custom-workflow/templates/tracker-binding", methods="GET")
     */
    public function getTrackerTemplateBinding()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            
            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }
            
            $data = $this->customWorkflowService->getTrackerTemplateBinding($projectId, $trackerId);
            
            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 检查事项类型是否有自定义工作流绑定
     * @RequestMapping(path="/custom-workflow/check-binding", methods="GET")
     */
    public function checkBinding()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');

            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            $result = $this->customWorkflowService->checkTrackerBinding($projectId, $trackerId);

            return $this->response->success($result);

        } catch (\Exception $e) {
            return $this->response->error('检查绑定状态失败：' . $e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取事项类型配置的状态列表
     * @RequestMapping(path="/custom-workflow/tracker-statuses", methods="GET")
     */
    public function getTrackerConfiguredStatuses()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');

            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            $data = $this->customWorkflowService->getTrackerConfiguredStatuses($projectId, $trackerId);

            return $this->response->success($data);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 检查迁移需求
     * @RequestMapping(path="/custom-workflow/check-migration", methods="POST")
     */
    public function checkMigration()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            $templateId = $this->request->input('template_id');

            if (empty($projectId) || empty($trackerId) || empty($templateId)) {
                return $this->response->error('项目ID、事项类型ID和模板ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            $result = $this->customWorkflowService->checkTemplateMigrationNeed($projectId, $trackerId, $templateId);

            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 执行迁移
     * @RequestMapping(path="/custom-workflow/execute-migration", methods="POST")
     */
    public function executeMigration()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');
            $statusMappings = $this->request->input('status_mappings', []);

            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            // 如果没有状态映射，直接返回成功（无需迁移）
            if (empty($statusMappings)) {
                return $this->response->success([
                    'success' => true,
                    'total_affected_issues' => 0,
                    'migration_details' => []
                ], '无需状态迁移，直接绑定模板');
            }

            // 验证状态映射格式
            foreach ($statusMappings as $mapping) {
                if (!isset($mapping['from_status_id']) || !isset($mapping['to_status_id'])) {
                    return $this->response->error('状态映射格式错误', StatusCode::VALIDATION_ERROR);
                }
            }

            $result = $this->customWorkflowService->executeMigration($projectId, $trackerId, $statusMappings);

            return $this->response->success($result, '状态迁移完成');
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 检查解除绑定的迁移需求 针对系统默认模板
     * @RequestMapping(path="/custom-workflow/check-unbind-migration", methods="POST")
     */
    public function checkUnbindMigration()
    {
        try {
            $projectId = $this->request->input('project_id');
            $trackerId = $this->request->input('tracker_id');

            if (empty($projectId) || empty($trackerId)) {
                return $this->response->error('项目ID和事项类型ID不能为空', StatusCode::VALIDATION_ERROR);
            }

            $result = $this->customWorkflowService->checkUnbindMigrationNeed($projectId, $trackerId);

            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error($e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

}