<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/23 下午3:52
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;

/**
 * 表格测试数据
 * @AutoController()
 */
class TableController extends BaseController
{
    public function getList()
    {
        $result['list'] = [
            [
                'author'      => "朱秀兰",
                'datetime'    => "1983-07-11 22'>46'>06",
                'description' => "定个到分下非反始这厂细两至构",
                'id'          => "340000201901159158",
                'img'         => "https'>//fastly.jsdelivr.net/gh/chuzhixin/image/table/vab-image-15.jpg",
                'pageViews'   => "4036",
                'percent'     => "95",
                'percentage'  => "95",
                'rate'        => 4,
                'status'      => "deleted",
                'switch'      => "false",
                'timestamp'   => "1655970377138",
                'title'       => "Du<PERSON><PERSON>rsck Ykowlpmtns",
                'type'        => "1",
                'uuid'        => "C18564c9-f8c7-5fC8-8da6-df3eb5FfFF1c",
            ]
        ];
        $result['total'] = 1;
        return $this->response->success($result);
    }
}