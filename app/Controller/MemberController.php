<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午7:58
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Controller;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\MemberService;
use App\Request\User\ResetPasswordRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("成员类接口")
 * @AutoController()
 */
class MemberController extends BaseController
{
    /**
     * @Inject()
     * @var MemberService
     */
    protected $memberService;

    public function index()
    {
        return 1;
    }
    /**
     * 公司部门结构成员表
     * @return void
     */
    public function memberStruct()
    {
        $result = $this->memberService->memberStruct();
        return $this->response->success($result);
    }

    /**
     * 我的团队
     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function myTeam()
    {
        $result = $this->memberService->myTeamList();
        return $this->response->success($result);
    }
}