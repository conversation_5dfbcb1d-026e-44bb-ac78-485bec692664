<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaReturnOrderExpressService;
use App\Request\Project\OverViewRequest;
use App\Request\TchipOa\OaFiles\OaFilesEditRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("回货快递单管理")
 * @AutoController()
 */
class OaReturnOrderExpressController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaReturnOrderExpressService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    public function overView()
    {
        $id = $this->request->input('id');
        $result = $this->service->getOverView($id);
        return $this->response->success($result);
    }

    public function doEdit()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    public function ocrGetCode()
    {
        $file = $this->request->file('file');
        if (!$file) {
            return $this->response->error(__('common.Missing_parameter'));
        }
        $result = $this->service->ocrGetCode($file);
        return $this->response->success($result);
    }

}