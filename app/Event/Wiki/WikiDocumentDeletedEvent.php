<?php

declare(strict_types=1);

namespace App\Event\Wiki;

/**
 * Wiki文档删除事件
 */
class WikiDocumentDeletedEvent
{
    /**
     * @var int 文档ID
     */
    public int $documentId;

    /**
     * @var int 文档创建者用户ID
     */
    public int $authorId;

    /**
     * @var int 操作者用户ID
     */
    public int $operatorId;

    /**
     * @var string 文档标题
     */
    public string $title;

    /**
     * @var array 额外数据
     */
    public array $extraData;

    public function __construct(int $documentId, int $authorId, int $operatorId, string $title, array $extraData = [])
    {
        $this->documentId = $documentId;
        $this->authorId = $authorId;
        $this->operatorId = $operatorId;
        $this->title = $title;
        $this->extraData = $extraData;
    }
}