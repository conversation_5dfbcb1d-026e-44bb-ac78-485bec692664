<?php

declare(strict_types=1);

namespace App\Event\Wiki;

/**
 * Wiki文档阅读事件
 */
class WikiDocumentViewedEvent
{
    /**
     * @var int 文档ID
     */
    public int $documentId;

    /**
     * @var int 阅读者用户ID
     */
    public int $viewerId;

    /**
     * @var int 文档创建者用户ID
     */
    public int $authorId;

    /**
     * @var string 文档标题
     */
    public string $title;

    /**
     * @var int 当前阅读总数
     */
    public int $currentViewCount;

    /**
     * @var array 额外数据
     */
    public array $extraData;

    public function __construct(int $documentId, int $viewerId, int $authorId, string $title, int $currentViewCount, array $extraData = [])
    {
        $this->documentId = $documentId;
        $this->viewerId = $viewerId;
        $this->authorId = $authorId;
        $this->title = $title;
        $this->currentViewCount = $currentViewCount;
        $this->extraData = $extraData;
    }
}
