<?php

declare(strict_types=1);

namespace App\Event\Wiki;

/**
 * Wiki文档创建事件
 */
class WikiDocumentCreatedEvent
{
    /**
     * @var int 文档ID
     */
    public int $documentId;

    /**
     * @var int 创建者用户ID
     */
    public int $userId;

    /**
     * @var string 文档标题
     */
    public string $title;

    /**
     * @var array 额外数据
     */
    public array $extraData;

    public function __construct(int $documentId, int $userId, string $title, array $extraData = [])
    {
        $this->documentId = $documentId;
        $this->userId = $userId;
        $this->title = $title;
        $this->extraData = $extraData;
    }
}