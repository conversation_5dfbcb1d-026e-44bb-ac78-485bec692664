<?php

declare(strict_types=1);

namespace App\Event\Wiki;

/**
 * Wiki文档阅读里程碑事件
 * 当文档阅读量达到特定里程碑时触发（如每10个阅读量）
 */
class WikiDocumentViewMilestoneEvent
{
    /**
     * @var int 文档ID
     */
    public int $documentId;

    /**
     * @var int 文档创建者用户ID
     */
    public int $authorId;

    /**
     * @var string 文档标题
     */
    public string $title;

    /**
     * @var int 当前阅读总数
     */
    public int $currentViewCount;

    /**
     * @var int 里程碑值（如10, 20, 30...）
     */
    public int $milestoneValue;

    /**
     * @var array 额外数据
     */
    public array $extraData;

    public function __construct(int $documentId, int $authorId, string $title, int $currentViewCount, int $milestoneValue, array $extraData = [])
    {
        $this->documentId = $documentId;
        $this->authorId = $authorId;
        $this->title = $title;
        $this->currentViewCount = $currentViewCount;
        $this->milestoneValue = $milestoneValue;
        $this->extraData = $extraData;
    }
}
