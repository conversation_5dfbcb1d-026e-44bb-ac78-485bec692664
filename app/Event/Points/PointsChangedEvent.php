<?php

declare(strict_types=1);

namespace App\Event\Points;

/**
 * 积分变化事件
 * 用于触发成就检测等后续逻辑
 */
class PointsChangedEvent
{
    /**
     * @var int 用户ID
     */
    public int $userId;

    /**
     * @var string 操作类型
     */
    public string $actionType;

    /**
     * @var int 积分变化量（正数为增加，负数为减少）
     */
    public int $pointChange;

    /**
     * @var string 积分来源类型
     */
    public string $sourceType;

    /**
     * @var int|null 积分来源ID
     */
    public ?int $sourceId;

    /**
     * @var array 额外数据
     */
    public array $extraData;

    /**
     * @var string 变化描述
     */
    public string $description;

    public function __construct(
        int $userId,
        string $actionType,
        int $pointChange,
        string $sourceType,
        ?int $sourceId = null,
        string $description = '',
        array $extraData = []
    ) {
        $this->userId = $userId;
        $this->actionType = $actionType;
        $this->pointChange = $pointChange;
        $this->sourceType = $sourceType;
        $this->sourceId = $sourceId;
        $this->description = $description;
        $this->extraData = $extraData;
    }
}