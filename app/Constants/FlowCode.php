<?php
declare(strict_types=1);

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

class FlowCode extends AbstractConstants
{
    /* 流程状态-未开始 */
    const STATUS_NOTSTART = 3;
    /* 流程状态-进行中 */
    const STATUS_PROGRESS = 2;
    /* 流程状态-完成 */
    const STATUS_DONE = 1;
    /* 流程状态-超时 */
    const STATUS_OVER = 4;

    // station产品给定的流程节点子任务标题
    const CHILD_ISSUE_SUBJECT_MAP = [
        // 1.阶段-项目立项
        '产品调研文案' => [
            '明确目标用户和产品定位',
            '明确成本范围和售价目标',
            '明确前期销售模式'
        ],
        '功能规划文档' => [
            '模具需求分析',
            '硬件规格分析',
            '软件需求分析',
        ],

        // 2.阶段-产品研发
        '硬件' => [
            '原理图设计',
            'PCBA设计',
            '结构板',
        ],
        '外壳' => [
            '外观设计输出',
            '3D外观手板输出',
            '结构方案输出',
            '3D结构样机输出',
        ],
        '硬件:PCBA V0.1 及系统软件' => [
            'PCB回板',
            'PCBA回板',
            'PCBA系统软件'
        ],
        '外壳:3D功能样机' => [
            '3D功能样机评审'
        ],
        '包装说明书' => [
            '配件选型'
        ], // 3D 结构&功能 样机的包装说明书节点
        '硬件:PCBA V0' => [
            '原理图设计',
            'PCB设计',
            'PCB回板',
            'PCBA 回板',
            'PCBA 系统软件功能调试',
            'PCBA 电气测试输出',
            'PCBA 高低温测试输出',
            'PCBA 静电测试输出'
        ], // v0.2~v0.x一系列  // 未生效其使用
        '硬件:PCBA V0.x及系统软件' => [
            '原理图设计',
            'PCB设计',
            'PCB回板',
            'PCBA 回板',
            'PCBA 系统软件功能调试',
            'PCBA 电气测试输出',
            'PCBA 高低温测试输出',
            'PCBA 静电测试输出'
        ],
        '硬件:PCBA V1.0 及系统软件' => [
            '原理图设计',
            'PCB设计',
            'PCB回板',
            'PCBA 回板',
            'PCBA 系统软件'
        ],
        '模具开发' => [
            '模具输出(制图/开模/T0)',
            'T1样机',
            'T2样机',
        ],
        '整机评审' => [
            'CNC样机',
            '软件评审',
            '包装说明书配件'
        ],

        // 3.阶段-小批量
        '第一次小批量' => [
            'PCBA V1.0小批量',
            '外壳小批量',
            '第一次问题收集、处理优化'
        ],
    ];

    // station产品给定的流程的事项分类用以标志对应事项
    const stationIssueClass = [
        '立项', '研发', '小批量', '大批量'
    ];

    // station产品给定的流程节点状态
    const progressNodeStatusIdToTextMap = [
        11 => '未开始',
        12 => '进行中',
        13 => '已完成',
    ];

    // station产品给定的流程节点状态
    const progressNodeStatusTextToIdMap = [
        '未开始' => 11,
        '进行中' => 12,
        '已完成' => 13,
    ];

}