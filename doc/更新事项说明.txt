2022.11.23更新事项
- 新增 bi_config，参考fastadmin创建的系统参数设置表，参考请见文档数据库结构文档
       · 添加数据 title=周报周期,name=report_period,type=array,value={"start":{"work":"now","day":"mon","time":"10:00"},"end":{"work":"next","day":"mon","time":"10:00"}}
       · 参数主要功能统计填写周报时间段
- 新增 bi_oa_report_inform数据表，暂时用不上
- 新增 每周通知员工填写周报
       · app/Task/TchipOa/InformUserReportTask
       需要先设定bi_config表的周报周期数据
- 新增 bi_user表中新增字段do_report，是否需要填写周报
- 新增 旧oa系统中部门表和公司表到bi库中，有一些字段经过修改，参考请见文档数据库结构文档。
       · oa_part,oa_company
- 新增 接口，参考APIPOST
      · 根据部门ID获取员工列表 OA-用户-根据部门ID获取员工列表
          /departmentManagement/departmentsUsers
      · 获取OA模块公司列表 OA-用户-OA公司列表
          /tchip_oa/user_company/getList
      · 获取OA模块部门列表 OA-用户-OA部门列表
          /tchip_oa/user_part/getList
      · OA周报统计接口 OA-周报-周报统计
          /tchip_oa/oa_report/statistics
      · 根据登陆用户获取对应的部门列表
          /departmentManagement/userDepartment
      · 根据部门ID获取员工列表
          /departmentManagement/departmentsUsers