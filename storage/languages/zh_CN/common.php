<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/29 下午7:42
 * <AUTHOR>
 * @Description
 *
 */

return [
    'No_results_were_found'                          => '记录未找到',
    'No_rows_were_updated'                           => '未更新任何数据',
    'Missing_parameter'                              => '缺少参数',
    'Missing_parameter:%s'                           => '缺少参数:%s',
    'Delete_fail'                                    => '删除失败',
    'Data_is_exist'                                  => '数据已存在',
    'No_file_upload_or_server_upload_limit_exceeded' => '未上传文件或超出服务器上传限制',
    'File_is_too_big'                                => '当前上传(:size1 M)，最大允许上传文件大小 :size2 M',
    'Uploaded_file_format_is_limited'                => '上传文件格式受限制',
    'Uploaded_file_is_not_a_valid_image'             => '上传文件不是有效的图片文件',
    'Upload_illegal_files'                           => '非法上传文件',
    'Directory_creation_failed:%s'                   => '目录创建失败:%s',
    'Has_the_same_filename:%s'                       => '存在同名文件：%s',
    'Api_key_is_timeout'                             => 'Apikey失效请重新登陆',
    'Sync_third_user_exception'                      => '第三方用户数据异常',
    'File_write_failed'                              => '文件写入失败',
    'Crontab_is_exist'                               => '任务 :name 已存在！',
    'User_exception'                                 => '用户数据异常',
    'Cannot_choose_oneself'                          => '不能选择自己为父级！',
    'Email_is_exist'                                 => '该邮箱已存在！',
    'User_error'                                     => '用户异常！',
    'Project_members_not_exist'                      => '项目不存在成员',
    'To_do_issue_exist'                              => '存在未完成的子事项',
    'Data_exception'                                 => '数据异常',
    'Open_issue_to_close_issue'                      => '无法将打开事项保存到已关闭的事项',
    'File_not_exist'        => '文件不存在',
    'File_incorrect_format' => '文件模版不正确',
    'Repeat_operation' => '请勿重新操作'
];